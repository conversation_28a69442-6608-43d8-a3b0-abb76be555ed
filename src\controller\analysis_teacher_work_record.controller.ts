import { Controller, Get, Inject, Query } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { AnalysisTeacherWorkRecordService } from '../service/analysis_teacher_work_record.service';

@Controller('/analysis_teacher_work_record')
export class AnalysisTeacherWorkRecordController {
  @Inject()
  ctx: Context;

  @Inject()
  service: AnalysisTeacherWorkRecordService;

  @Get('/statistic', { summary: '统计' })
  async statistic(@Query() query: any) {
    return this.service.statistic(query);
  }

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({ query: queryInfo, offset, limit });
  }
}
