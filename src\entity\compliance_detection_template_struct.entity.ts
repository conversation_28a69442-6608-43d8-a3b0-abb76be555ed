import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ComplianceDetectionTemplateDetail } from '.';

export interface ComplianceDetectionTemplateStructAttributes {
  /** 主键 */
  id: string;
  /** 达标检测范本详情id */
  detail_id: number;
  /** 结构名称 */
  name: string;
  /** 试题ids */
  questionIds: string[];
  /** 排序 */
  sort_order: number;
  detail?: ComplianceDetectionTemplateDetail;
}

@Table({
  tableName: 'compliance_detection_template_struct',
  timestamps: true,
  comment: '达标检测范本结构表',
})
export class ComplianceDetectionTemplateStruct
  extends Model<ComplianceDetectionTemplateStructAttributes>
  implements ComplianceDetectionTemplateStructAttributes
{
  @Column({
    type: DataType.STRING(64),
    primaryKey: true,
    comment: '主键',
  })
  id: string;

  @ForeignKey(() => ComplianceDetectionTemplateDetail)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '达标检测范本详情id',
  })
  detail_id: number;

  @BelongsTo(() => ComplianceDetectionTemplateDetail, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  detail?: ComplianceDetectionTemplateDetail;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '结构名称',
  })
  name: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    comment: '试题ids',
    get() {
      const newValue = this.getDataValue('questionIds');
      return newValue ? newValue.split(',') : [];
    },
    set(value: string[]) {
      this.setDataValue('questionIds', value.join(','));
    },
  })
  questionIds: string[];

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '排序',
  })
  sort_order: number;
}
