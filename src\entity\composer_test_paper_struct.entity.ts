import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ComposerTestPaper } from '.';

export interface ComposerTestPaperStructAttributes {
  /** 主键 */
  id: string;
  /** 试卷id */
  composerTestPaperId: number;
  /** 结构名称 */
  name: string;
  /** 试题ids */
  questionIds: string[];
  /** 排序 */
  sortOrder: number;
  /** 分值 */
  score: number;
  /** 时长 */
  duration: number;
  testPaper?: ComposerTestPaper;
}

@Table({
  tableName: 'composer_test_paper_struct',
  timestamps: true,
  comment: '试卷结构表',
  indexes: [
    {
      fields: ['composer_test_paper_id'],
    },
  ],
})
export class ComposerTestPaperStruct
  extends Model<ComposerTestPaperStructAttributes>
  implements ComposerTestPaperStructAttributes
{
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    primaryKey: true,
    comment: '唯一标识',
  })
  id: string;

  @ForeignKey(() => ComposerTestPaper)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '试卷id',
    field: 'composer_test_paper_id',
  })
  composerTestPaperId: number;

  @BelongsTo(() => ComposerTestPaper, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  testPaper?: ComposerTestPaper;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '结构名称',
  })
  name: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '试题ids',
    field: 'question_ids',
    get() {
      const rawValue = this.getDataValue('questionIds');
      return rawValue ? rawValue.split(',') : [];
    },
    set(value: string[]) {
      this.setDataValue('questionIds', value.join(','));
    },
  })
  questionIds: string[];

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '排序',
    field: 'sort_order',
  })
  sortOrder: number;

  @Column({
    type: DataType.FLOAT,
    allowNull: false,
    comment: '分值',
  })
  score: number;
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '时长',
    field: 'duration',
  })
  duration: number;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '创建时间',
    field: 'created_at',
  })
  createdAt?: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '更新时间',
    field: 'updated_at',
  })
  updatedAt?: Date;
}
