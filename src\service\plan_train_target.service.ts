import { Inject, Provide } from '@midwayjs/core';
import { PlanTrainTarget } from '../entity/plan_train_target.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class PlanTrainTargetService extends BaseService<PlanTrainTarget> {
  @Inject()
  ctx: Context;

  constructor() {
    super('培养目标');
  }
  getModel = () => {
    return PlanTrainTarget;
  };

  /**
   * 批量删除
   * @param ids 需要删除的id数组
   */
  async bulkDestroy(ids) {
    await PlanTrainTarget.destroy({
      where: {
        id: ids,
      },
    });
  }
}
