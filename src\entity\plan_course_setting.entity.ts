import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
} from 'sequelize-typescript';
import { PlanCourseSubject } from '.';

export interface PlanCourseSettingAttributes {
  /** 主键 */
  id: number;
  /** 课程类型 */
  type?: string;
  /** 课程表id */
  course_subject_id?: number;
  /** 课程名称 */
  course_name?: string;
  /** 年级 */
  grade_name?: string;
  /** 学校id */
  enterprise_id?: number;
  course_subject?: PlanCourseSubject;
}

@Table({
  tableName: 'plan_course_setting',
  timestamps: true,
  comment: '课程设置表',
})
export class PlanCourseSetting
  extends Model<PlanCourseSettingAttributes>
  implements PlanCourseSettingAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @Column({
    type: DataType.ENUM('国家课程', '地方课程', '校本课程'),
    allowNull: true,
    comment: '课程类型',
  })
  type?: string;

  @ForeignKey(() => PlanCourseSubject)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '课程科目id',
  })
  course_subject_id?: number;

  @BelongsTo(() => PlanCourseSubject, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  course_subject?: PlanCourseSubject;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '课程名称',
  })
  course_name?: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '年级',
  })
  grade_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '学校id',
  })
  enterprise_id?: number;
}
