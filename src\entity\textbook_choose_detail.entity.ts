import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { TextbookChoose } from '.';

export interface TextbookChooseDetailAttributes {
  /** ID，主键 */
  id: number;
  /** 选用记录ID */
  textbook_choose_id: number;
  /** 区域编号 */
  area_code: string;
}

@Table({
  tableName: 'textbook_choose_detail',
  underscored: true,
  timestamps: false,
})
export class TextbookChooseDetail
  extends Model<TextbookChooseDetailAttributes>
  implements TextbookChooseDetailAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  @ForeignKey(() => TextbookChoose)
  textbook_choose_id: number;

  @BelongsTo(() => TextbookChoose)
  textbook_choose: TextbookChoose;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  area_code: string;
}
