import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';

export interface ProvinceHomeworkGoalAttributes {
  /** 主键ID */
  id: number;
  /** 学段 */
  grade_section_code?: string;
  /** 学段名称 */
  grade_section_name?: string;
  /** 作业目标描述 */
  goal_description?: string;
  /** 所属学科名称 */
  subject_name?: string;
  /** 所属学科id */
  subject_id?: number;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({
  tableName: 'province_homework_goal',
  timestamps: true,
  comment: '作业目标表',
})
export class ProvinceHomeworkGoal
  extends Model<ProvinceHomeworkGoalAttributes>
  implements ProvinceHomeworkGoalAttributes
{
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学段',
  })
  grade_section_code?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学段名称',
  })
  grade_section_name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '作业目标描述',
  })
  goal_description?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '所属学科名称',
  })
  subject_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '所属学科id',
  })
  subject_id?: number;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    comment: '创建时间',
  })
  createdAt?: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    comment: '更新时间',
  })
  updatedAt?: Date;
}
