import { Inject, Logger, Provide } from '@midwayjs/core';
import {
  LessonWorkDesignDetail,
  LessonWorkCatalog,
  LessonWorkDesignQuestion,
  LessonWorkDesignStruct,
  // LessonWorkDesign,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { SystemQuestions } from '../model';
import { InjectEntityModel } from '@midwayjs/typegoose';
import { ReturnModelType } from '@typegoose/typegoose';
import { CustomError } from '../error/custom.error';
import { QuestionService } from './question.service';
import { randomUUID } from '@midwayjs/core/dist/util/uuid';

@Provide()
export class LessonWorkDesignDetailService extends BaseService<LessonWorkDesignDetail> {
  @Inject()
  ctx: Context;

  @Logger()
  logger;

  @Inject()
  questionService: QuestionService;

  constructor() {
    super('课时作业设计详情');
  }
  getModel = () => {
    return LessonWorkDesignDetail;
  };

  @InjectEntityModel(SystemQuestions)
  systemQuestions: ReturnModelType<typeof SystemQuestions>;

  /**
   * 获取单个
   * @param id id
   * @memberof LessonWorkDesignDetailService
   */
  async show(id) {
    return await LessonWorkDesignDetail.findOne({
      where: { id },
      include: [{ model: LessonWorkDesignQuestion }],
    });
  }

  /**
   * 创建课时作业设计详情
   * @param body
   * @return {*} 创建结果
   */
  async create(body) {
    const { questions, structs, ...info } = body;
    const transaction = await LessonWorkDesignDetail.sequelize.transaction();
    try {
      // 获取最大排序索引
      const maxIndex = await this.getMaxIndex(info.design_id, info.catalog_id);
      // 创建作业
      const res = await LessonWorkDesignDetail.create(
        { ...info, sort_order: maxIndex + 1 },
        { transaction }
      );
      // 如果创建作业有题目信息，则直接入库
      if (questions) {
        const data = questions.map((item, index) => {
          return {
            ...item,
            designDetail_id: res.id,
            sort_order: index + 1,
          };
        });
        await LessonWorkDesignQuestion.bulkCreate(data, { transaction });
      }
      // 如果创建作业有结构信息，则直接入库
      if (structs) {
        const structArr = structs.map((item, index) => {
          return {
            ...item,
            designDetail_id: res.id,
            sort_order: index + 1,
          };
        });
        await LessonWorkDesignStruct.bulkCreate(structArr, { transaction });
      }
      // else {
      //   // 获取课时作业设计详情
      //   const lessonWorkDesign = await LessonWorkDesign.findOne({
      //     where: { id: info.design_id },
      //   });
      //   // 批量创建课时作业题目
      //   await this.createBulk(
      //     JSON.stringify(lessonWorkDesign),
      //     JSON.stringify(res),
      //     transaction
      //   );
      // }
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 获取最大排序索引
   * @param design_id 课时作业设计id
   * @param catalog_id 课时作业设计目录id
   * @return {*} 最大排序索引
   * @memberof LessonWorkDesignDetailService
   */
  async getMaxIndex(design_id, catalog_id) {
    const maxIndex = await LessonWorkDesignDetail.max('sort_order', {
      where: {
        design_id,
        catalog_id,
      },
    });
    return (maxIndex as number) || 0;
  }

  /**
   * 批量创建课时作业题目信息
   * @param lessonWorkDesign 课时作业设计
   * @param lessonWorkDesignDetail 课时作业详情
   * @param transaction 事务
   * @memberof LessonWorkDesignDetailService
   */
  async createBulk(
    lessonWorkDesign,
    lessonWorkDesignDetail,
    transaction = null
  ) {
    const { catalog_id, id } = lessonWorkDesignDetail;

    // 根据课时作业目录id获取教材目录信息
    const catalogInfo = await LessonWorkCatalog.findOne({
      where: {
        id: catalog_id,
      },
    });

    // 根据课时作业目录信息获取对应来源、课时的试题信息
    const questions = await this.systemQuestions.aggregate([
      {
        $match: {
          source: lessonWorkDesign.source,
          period: catalogInfo.title,
          pid: null, // 查找pid为null的试题，排除掉子题
        },
      },
      { $sort: { createdAt: 1 } }, // 按照createdAt字段升序排序
    ]);

    //根据课时信息过滤试题
    const questionList = questions.filter(v => v.period === catalogInfo.title);
    // 删除之前创建的题目
    await LessonWorkDesignQuestion.destroy({
      where: { designDetail_id: id },
      transaction,
    });

    // 组装作业题目数据
    const data = questionList.map((v, index) => ({
      designDetail_id: id,
      question_id: typeof v._id === 'string' ? v._id : v._id.toString(),
      sort_order: index + 1,
    }));
    await LessonWorkDesignQuestion.bulkCreate(data, { transaction });
  }

  /**
   * 导入课时范本详情
   * @param filePath 文件路径
   * @param questionInfo 试题信息
   * @param info 详情数据
   * @returns
   */
  async importLessonWorkDetails(
    filePath: string,
    questionInfo: any,
    info: any
  ) {
    const transaction = await LessonWorkDesignDetail.sequelize.transaction();
    // 创建作业详情
    try {
      // 获取最大排序索引
      const maxIndex = await this.getMaxIndex(info.design_id, info.catalog_id);
      // 创建作业范本详情
      const res = await LessonWorkDesignDetail.create(
        { ...info, sort_order: maxIndex + 1 },
        { transaction }
      );
      // 先导入试题
      const questions = await this.questionService.importQuestions(
        filePath,
        questionInfo
      );
      // 作业详情试题
      // 作业详情结构
      if (questions && questions.length > 0) {
        const workQuestions = [];
        const questionIds = [];
        questions.forEach((item, index) => {
          questionIds.push(item._id);
          workQuestions.push({
            designDetail_id: res.id,
            sort_order: index + 1,
            question_id: item._id,
            source_table: item.tableName,
          });
        });
        const defaultStruct = {
          id: `hidden_${randomUUID()}`,
          name: '暂无分组',
          designDetail_id: res.id,
          sort_order: 1,
          questionIds: questionIds,
        };
        await LessonWorkDesignQuestion.bulkCreate(workQuestions, {
          transaction,
        });
        await LessonWorkDesignStruct.create(defaultStruct, {
          transaction,
        });
      }
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      this.logger.error(
        `Failed to importLessonWorkDetails, message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError(error.message);
    }
  }
}
