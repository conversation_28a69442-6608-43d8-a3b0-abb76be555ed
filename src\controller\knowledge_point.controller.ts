import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { KnowledgePointService } from '../service/knowledge_point.service';
import { CustomError } from '../error/custom.error';
import { isNil, isUndefined } from 'lodash';

@Controller('/knowledge_point')
export class KnowledgePointController {
  @Inject()
  ctx: Context;

  @Inject()
  service: KnowledgePointService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;
    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return await this.service.findAll({ query: queryInfo, offset, limit });
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    if (typeof body.pid !== 'undefined') {
      body.pid =
        isNil(body.pid) || isUndefined(body.pid) || body.pid === 0
          ? null
          : body.pid;
    }
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() body: any) {
    if (!body.subjectId) {
      throw new CustomError('未选择科目！');
    }
    if (typeof body.pid !== 'undefined') {
      body.pid =
        isNil(body.pid) || isUndefined(body.pid) || body.pid === 0
          ? null
          : body.pid;
    }
    const res = await this.service.create(body);
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: number, @Body() body: any) {
    if (typeof body.pid !== 'undefined') {
      body.pid =
        isNil(body.pid) || isUndefined(body.pid) || body.pid === 0
          ? null
          : body.pid;
    }
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }

  @Get('/tree', { summary: '获取树形结构' })
  async getTree(@Query('subjectId') subjectId: number) {
    const res = await this.service.getKnowledgePointTreeBySubject(subjectId);
    return res;
  }

  @Get('/points', { summary: '获取子级知识点' })
  async getTreeById(
    @Query('pid') parentId: number,
    @Query('subjectId') subjectId: number
  ) {
    const res = await this.service.getKnowledgePointList(subjectId, parentId);
    return res;
  }
}
