import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ClassWorkDetail } from '.';

export interface ClassWorkStructAttributes {
  /** 主键 */
  id: string;
  /** 课时作业范本详情id */
  classworkDetail_id: number;
  /** 结构名称 */
  name: string;
  /** 试题ids */
  questionIds: string[];
  /** 排序 */
  sort_order: number;
  classWorkDetail?: ClassWorkDetail;
}

@Table({
  tableName: 'class_work_struct',
  timestamps: true,
  comment: '课时作业范本结构表',
})
export class ClassWorkStruct
  extends Model<ClassWorkStructAttributes>
  implements ClassWorkStructAttributes
{
  @Column({
    type: DataType.STRING(64),
    primaryKey: true,
    comment: '主键',
  })
  id: string;

  @ForeignKey(() => ClassWorkDetail)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '课时作业范本详情id',
  })
  classworkDetail_id: number;

  @BelongsTo(() => ClassWorkDetail, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  classWorkDetail?: ClassWorkDetail;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '结构名称',
  })
  name: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '试题ids',
    get() {
      const rawValue = this.getDataValue('questionIds');
      return rawValue ? rawValue.split(',') : [];
    },
    set(value: string[]) {
      this.setDataValue('questionIds', value.join(','));
    },
  })
  questionIds: string[];

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '排序',
  })
  sort_order: number;
}
