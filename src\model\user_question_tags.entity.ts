import { ModelOptions, prop, index } from '@typegoose/typegoose';

@ModelOptions({
  schemaOptions: {
    collection: 'user_question_tags',
    timestamps: false,
  },
})
@index({ userId: 1, questionId: 1 }, { name: 'userQuestionTagIndex' }) // 添加复合索引
export class UserQuestionTags {
  /** 用户id */
  @prop({ required: true })
  userId: number;

  /** 试题id */
  @prop({ required: true })
  questionId: string;

  /** 标记内容 */
  @prop({ required: true })
  tags: any[];
}
