import { Inject, Provide } from '@midwayjs/core';
import {
  HomeworkDetailTemplateMapping,
  SchoolHomework,
  SchoolHomeworkDetail,
  Textbook,
  // TextbookCatalog,
  TextbookTemp,
  TextbookTempDetail,
  User,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { SchoolQuestions, SystemQuestions } from '../model';
import { ReturnModelType } from '@typegoose/typegoose';
import { InjectEntityModel } from '@midwayjs/typegoose';
import { Types } from 'mongoose';
import { SystemQuestionService } from './system_question.service';

@Provide()
export class SchoolHomeworkService extends BaseService<SchoolHomework> {
  @Inject()
  ctx: Context;
  @InjectEntityModel(SystemQuestions)
  systemQuestions: ReturnModelType<typeof SystemQuestions>;

  @Inject()
  systemQuestionService: SystemQuestionService;

  @InjectEntityModel(SchoolQuestions)
  schoolQuestions: ReturnModelType<typeof SchoolQuestions>;
  constructor() {
    super('校本作业');
  }
  getModel = () => {
    return SchoolHomework;
  };
  /**
   * 查询列表
   *
   * @param {*} query 查询条件
   * @param {*} offset 分页，跳过
   * @param {*} limit 分页，截取
   * @return {*} res
   * @memberof School_workService
   */
  async getList(query, offset, limit) {
    const queryOption: any = {};
    const result: any = {};

    const hasPaging = offset !== undefined || limit !== undefined;
    if (hasPaging) {
      queryOption.offset = Number(offset) || 0;
      queryOption.limit = Number(limit) || 10;
    }
    const where = {
      ...query,
      // 自定义查询参数参数
    };
    if (query.status) {
      where.status = query.status.split(',');
    }
    // 年纪学科教材版本册次范本来源作者
    queryOption.include = [
      {
        model: TextbookTemp,
        as: 'textbookTemp',
        attributes: ['name', 'source'],
        include: {
          model: Textbook,
          attributes: ['nj', 'jc', 'xk', 'jcbb'],
        },
      },
      { model: User, as: 'account', attributes: ['name'] },
    ];

    queryOption.where = where;

    // 其他参数
    queryOption.order = [['createdAt', 'desc']];

    const res = await SchoolHomework.findAll(queryOption);
    if (hasPaging) {
      // 获取总数
      const total = await SchoolHomework.count({
        where,
        distinct: true,
      });
      result.total = total || 0;
    }
    //TODO:处理查询逻辑
    // const jcCodes = [];
    // const arr = JSON.parse(JSON.stringify(res));
    // const ids = arr
    //   .map(a => {
    //     const {
    //       textbookTemp: {
    //         xbzy_zy: { nj, xk, jc, jcbb },
    //       },
    //     } = a;
    //     jcCodes.push(jc);
    //     return [nj, xk, jcbb];
    //   })
    //   .flat();
    // const info = await Category.findAll({
    //   where: { code: ids },
    //   attributes: ['code', 'name'],
    // });
    // let finalRes = arr.map(a => {
    //   const infoData = JSON.parse(JSON.stringify(info));
    //   const {
    //     textbookTemp: {
    //       xbzy_zy: { nj, xk, jcbb },
    //     },
    //   } = a;
    //   a.textbookTemp.xbzy_zy.nj = infoData.find(b => b.code === nj);
    //   a.textbookTemp.xbzy_zy.xk = infoData.find(b => b.code === xk);
    //   a.textbookTemp.xbzy_zy.jcbb = infoData.find(b => b.code === jcbb);
    //   return a;
    // });
    // const jcInfoArr = await TextbookCatalog.findAll({
    //   where: { : jcCodes },
    //   attributes: ['item_text', 'item_value'],
    // });
    // finalRes = finalRes.map(f => {
    //   const jcInfoData = JSON.parse(JSON.stringify(jcInfoArr));
    //   const obj = {
    //     code: f.textbookTemp.xbzy_zy.jc,
    //     value: jcInfoData
    //       .filter(b => b.item_value === f.textbookTemp.xbzy_zy.jc)
    //       .map(b => b.item_text),
    //   };
    //   f.textbookTemp.xbzy_zy.jc = obj;
    //   return f;
    // });
    result.list = res;
    return result;
  }

  /**
   * 查询单个
   *
   * @param {*} id id
   * @return {*} res
   * @memberof School_wor
   */
  async get(id) {
    const result = await SchoolHomework.findOne({
      where: {
        id,
      },
      include: {
        model: TextbookTemp,
        as: 'textbookTemp',
        attributes: ['name', 'source'],
        include: [{ model: Textbook }],
      },
    });
    return result;
  }

  /**
   * 创建
   *
   * @param {*} data 数据
   * @return {*} res
   * @memberof School_wor
   */
  async create(data) {
    let transaction;
    try {
      transaction = await SchoolHomework.sequelize.transaction();
      if (!transaction) throw new CustomError('创建校本作业时事务创建失败');
      // 获取模板详情
      const filter = { id: data.textbook_temp_id };
      const include = {
        model: TextbookTempDetail,
        attributes: { exclude: ['textbook_temp_id'] },
      };
      const textBookTemp = await TextbookTemp.findOne({
        where: filter,
        include,
        transaction,
      });
      if (!textBookTemp) throw new Error('关联的范本不存在');
      if (!textBookTemp.details || !textBookTemp.details.length)
        throw new Error('关联的范本详情不存在');
      // 增加校本作业
      const schoolData = {
        ...data,
        source: textBookTemp.source,
      };
      const res = await SchoolHomework.create(schoolData, { transaction });
      // 构造数据
      const originalData = []; // 范本详情原始数据
      const mappingData = []; // 映射表数据
      const questionBankIds = []; // 题目id数组
      textBookTemp.details.forEach(detail => {
        const { dataValues } = detail;
        originalData.push(dataValues);
        const { question_bank_id, id, type } = dataValues;
        const obj: any = {
          temp_detail_id: id,
          type,
        };
        if (question_bank_id) {
          questionBankIds.push(question_bank_id);
          obj.temp_detail_question_id = question_bank_id;
        }
        if (type === '小题') {
          obj.school_detail_question_id = new Types.ObjectId().toString();
        }
        mappingData.push(obj);
      });
      // 插入映射表数据 得到所有对应的ids
      const insertMappingRes = await HomeworkDetailTemplateMapping.bulkCreate(
        mappingData,
        { transaction }
      );
      // 根据源数据与映射表数据 构造校本详情数据 存入校本详情表
      const xj = [];
      const dt = [];
      const xt = [];
      originalData.forEach(d => {
        const { id, pid } = d;
        const mappingObj = insertMappingRes.find(
          item => item.templateDetailId === id
        );
        if (pid) {
          const parentMappingObj = insertMappingRes.find(
            item => item.templateDetailId === pid
          );
          if (parentMappingObj) d.pid = parentMappingObj.schoolDetailId;
        }
        const { schoolDetailQuestionId, schoolDetailId } = mappingObj;
        d.question_bank_id = schoolDetailQuestionId;
        d.id = schoolDetailId;
        d.author = data.author;
        d.school_work_id = res.dataValues.id;
        if (d.type === '小节') xj.push(d);
        if (d.type === '大题') dt.push(d);
        if (d.type === '小题') xt.push(d);
      });
      // 增加校本作业详情
      await SchoolHomeworkDetail.bulkCreate([...xj, ...dt, ...xt], {
        transaction,
      });
      // 将范本作业关联的题目 复制到校本题库中
      // 查询出所有题目 进行复制到校本题库中
      const questions = await this.systemQuestions
        .find({
          _id: { $in: questionBankIds },
        })
        .exec();
      // 题目id赋值
      const insertDataArr = questions.map(item => {
        const itemData = JSON.parse(JSON.stringify(item));
        const { _id, ...question } = itemData;
        const id = mappingData.find(
          d => d.temp_detail_question_id === _id
        ).school_detail_question_id;
        delete question.createdAt;
        delete question.updatedAt;
        return {
          ...question,
          _id: new Types.ObjectId(id),
        };
      });
      await this.schoolQuestions.insertMany(insertDataArr);
      await transaction.commit();
      return res;
    } catch (error) {
      if (transaction) await transaction.rollback();
      throw new CustomError('创建校本失败');
    }
  }

  /**
   * 更新
   *
   * @param {*} data 数据
   * @param {*} id id
   * @memberof School_wor
   */
  async update(data, id) {
    let transaction;
    try {
      transaction = await SchoolHomework.sequelize.transaction();
      await SchoolHomework.update(data, {
        where: {
          id,
        },
        transaction,
      });
      transaction.commit();
    } catch (error) {
      await transaction.rollback();
      return null;
    }
  }

  /**
   * 按条件更新
   *
   * @param {*} data 数据
   * @param {*} [where={}] 匹配条件
   * @memberof School_wor
   */
  async edit(data, where = {}) {
    await SchoolHomework.update(data, { where });
  }

  /**
   * 删除
   *
   * @param {*} id id
   * @memberof School_wor
   */
  async destroy(id) {
    try {
      // 查询校本作业关联的所有小节、大题、小题
      const childrenInfos = await SchoolHomeworkDetail.findAll({
        where: { schoolHomeworkId: id },
      });
      if (childrenInfos.length > 0) {
        const childrenDetailIds = [];
        const smQuestionIds = [];
        childrenInfos.forEach(item => {
          const { id, questionId, type } = item.dataValues;
          if (type === '小题') smQuestionIds.push(questionId);
          childrenDetailIds.push(id);
        });
        if (smQuestionIds.length > 0)
          await this.systemQuestionService.destroyByIds(
            smQuestionIds,
            'school'
          ); // 校本题库删除小题
        // 删除所有映射数据
        await SchoolHomeworkDetail.destroy({
          where: { id: childrenDetailIds },
        });
      }
      // 删除校本作业
      await SchoolHomework.destroy({
        where: {
          id,
        },
      });
    } catch (error) {
      console.error(
        `Failed to execute destroy schoolWork, message is: ${error.message}`
      );
      throw error;
    }
  }
}
