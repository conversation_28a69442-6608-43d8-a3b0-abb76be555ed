import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { TextbookConfigService } from '../service/textbook_config.service';
import { CustomError } from '../error/custom.error';
import { TextbookConfig, QuestionRelation, ClassRelation } from '../entity';
import { Op } from 'sequelize';

@Controller('/textbook_config')
export class TextbookConfigController {
  @Inject()
  ctx: Context;

  @Inject()
  service: TextbookConfigService;

  @Get('/', { summary: '获取教材版本配置列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      include: [{ model: QuestionRelation }, { model: ClassRelation }],
      order: [['createdAt', 'DESC']],
    });
  }

  @Get('/:id', { summary: '根据id查询校本作业配置' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定校本作业配置');
    }
    return res;
  }

  @Post('/', { summary: '创建教材版本配置' })
  async create(@Body() data: any) {
    // 添加校验
    const {
      enterpriseCode,
      gradeCode,
      semesterCode,
      sourceCode,
      subjectCode,
      textbook_version,
      volume,
    } = data;
    const configInfo = await TextbookConfig.findOne({
      where: {
        enterpriseCode,
        gradeCode,
        semesterCode,
        sourceCode,
        subjectCode,
        textbook_version,
        volume,
      },
    });
    if (configInfo) {
      throw new CustomError('该校本作业配置已存在，请勿重复添加');
    }

    const res = await this.service.create(data);
    return res;
  }

  @Put('/:id', { summary: '更新教材版本配置' })
  async update(@Param('id') id: string, @Body() data: any) {
    // 添加校验
    const {
      enterpriseCode,
      gradeCode,
      semesterCode,
      sourceCode,
      subjectCode,
      textbook_version,
      volume,
    } = data;
    const configInfo = await TextbookConfig.findOne({
      where: {
        id: { [Op.ne]: id },
        enterpriseCode,
        gradeCode,
        semesterCode,
        sourceCode,
        subjectCode,
        textbook_version,
        volume,
      },
    });
    if (configInfo) {
      throw new CustomError('该校本作业配置已存在，请勿重复添加');
    }

    await this.service.update({ id }, data);
    return true;
  }

  @Del('/:id', { summary: '删除教材版本配置' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }
}
