import { Inject, Provide } from '@midwayjs/core';
import { QuestionCheckRecord } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { PersonalQuestionService } from './personal_question.service';
import { Op } from 'sequelize';
import { Types } from 'mongoose';
@Provide()
export class QuestionCheckRecordService extends BaseService<QuestionCheckRecord> {
  @Inject()
  ctx: Context;

  @Inject()
  personalQuestionService: PersonalQuestionService;

  constructor() {
    super('试题审核');
  }
  getModel = () => {
    return QuestionCheckRecord;
  };

  /**
   * 试题审核 （个人->学校）
   * @param questionIds 试题id数组
   * @param checkInfo 审核信息
   */
  async bulkCreate(questions: any[], checkInfo: any) {
    const transaction = await QuestionCheckRecord.sequelize.transaction();
    try {
      const { checkStatus, suggestion } = checkInfo;
      // 批量创建审核记录
      const checkRecords = questions.map(question => {
        const { questionBankId, difficulty, author, type, commitAt } = question;
        return {
          ...checkInfo,
          questionBankId,
          questionDifficultyCode: difficulty.code,
          questionDifficultyName: difficulty.name,
          questionTypeCode: type.code,
          questionTypeName: type.name,
          commitAt,
          authorId: author.id,
          authorName: author.name,
        };
      });
      await QuestionCheckRecord.bulkCreate(checkRecords);
      const questionIds = questions.map(q => q.questionBankId);
      // 审核通过后应该将该题复制到校本题库中
      if (checkStatus === '已通过') {
        // 组装试题结构推送到学校题库
        await this.personalQuestionService.shareToSchool(questionIds);
      }
      // 更新试题审核状态
      await this.personalQuestionService.updateBulk(questionIds, {
        checkStatus,
        suggestion,
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * 处理试题审核记录的清理与状态更新
   * @param questionId 试题ID
   */
  async cleanAndUpdateCheckRecords(questionBankId: string) {
    const transaction = await QuestionCheckRecord.sequelize.transaction();
    try {
      // 查询距当前时间最近的一条已通过的试题审核记录
      const latestPassedRecord = await QuestionCheckRecord.findOne({
        where: {
          questionBankId,
          checkStatus: '已通过',
        },
        order: [['createdAt', 'DESC']], // 按创建时间倒序排列
        transaction,
      });
      // 确认删除范围
      const destroyFilter = latestPassedRecord
        ? {
            questionBankId,
            createdAt: { [Op.gt]: latestPassedRecord.createdAt }, // 删除晚于最新成功记录的记录
          }
        : {
            questionBankId,
          };
      // 更新试题状态
      const questionInfo = latestPassedRecord
        ? {
            checkStatus: '已通过',
            suggestion: undefined, // 可根据需求调整
            commitAt: latestPassedRecord.commitAt,
          }
        : {
            checkStatus: undefined,
            isShared: false,
            suggestion: undefined, // 可根据需求调整
            commitAt: undefined,
          };
      await QuestionCheckRecord.destroy({
        where: destroyFilter,
        transaction,
      });
      // 更新试题状态为 null
      await this.personalQuestionService.updateMany(
        {
          $or: [
            { _id: new Types.ObjectId(questionBankId) },
            { pid: new Types.ObjectId(questionBankId) },
          ],
        },
        questionInfo
      );
      // 提交事务
      await transaction.commit();
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      throw error;
    }
  }
}
