import { Inject, Provide } from '@midwayjs/core';
import {
  ClassWorkDetail,
  ClassWorkQuestion,
  ClassWorkStruct,
  LessonWorkCatalog,
  LessonWorkDesign,
  LessonWorkDesignDetail,
  TextbookCatalog,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { LessonWorkCatalogService } from './lesson_work_catalog.service';

@Provide()
export class LessonWorkDesignService extends BaseService<LessonWorkDesign> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课时作业设计');
  }
  getModel = () => {
    return LessonWorkDesign;
  };

  @Inject()
  lessonWorkCatalogService: LessonWorkCatalogService;

  /**
   * 创建课时作业
   * 复制对应教材目录，以及对应目录下的作业 作业详情、作业题目、作业结构
   * @param info 作业设计信息
   */
  async create(info) {
    const transaction = await LessonWorkDesign.sequelize.transaction();
    try {
      // 如果没有使用课时作业范本，则直接创建课时作业信息及目录信息
      if (!info.class_work_id) {
        // 创建课时作业信息
        const res = await LessonWorkDesign.create(info, { transaction });

        // 复制当前教材目录信息
        await this.comonCopyCatalog(info, res.id, transaction);

        await transaction.commit();
        return res;
      } else {
        //如果使用课时作业范本，则将对应课时目录下的作业信息、试题信息、结构信息复制到对应作业设计中
        // 创建课时作业信息
        const res = await LessonWorkDesign.create(info, { transaction });

        // 复制当前教材目录信息
        await this.comonCopyCatalog(info, res.id, transaction);

        await transaction.commit();
        return res;
      }
    } catch (error) {
      transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 更新课时作业设计
   * @param id 课时作业设计id
   * @param info 更新数据
   */
  async updateById(id, info) {
    const transaction = await LessonWorkDesign.sequelize.transaction();
    try {
      // 如果是发布作业设计更新作业设计及设计下的详情都为发布
      if (info.status === '发布') {
        await LessonWorkDesign.update(info, { where: { id }, transaction });
        await LessonWorkDesignDetail.update(
          { status: info.status },
          { where: { design_id: id }, transaction }
        );
      } else if (info.class_work_id) {
        // 如果是编辑作业设计，判断是否更换作业范本，如果更换作业范本，需要清除所有作业试题、结构信息，根据新的范本生成
        const designInfo = await LessonWorkDesign.findOne({
          where: { id },
        });

        // 如果传入的课时作业范本id不等于当前课时作业范本id
        if (designInfo.class_work_id !== info.class_work_id) {
          // 删除课时作业设计目录信息
          await LessonWorkCatalog.destroy({
            where: { lessonWorkDesign_id: id },
            transaction,
          });
          // 删除课时作业设计详情信息，会一并删除试题、结构信息
          await LessonWorkDesignDetail.destroy({
            where: { design_id: id },
            transaction,
          });

          // 复制当前教材目录信息及课时作业试题、结构信息
          await this.comonCopyCatalog(info, id, transaction);
        }
        // 更新课时作业设计信息
        await LessonWorkDesign.update(info, { where: { id }, transaction });
      } else {
        // 更新课时作业设计信息
        await LessonWorkDesign.update(info, { where: { id }, transaction });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 公共方法，复制对应教材目录，以及对应目录下的作业信息、试题信息、结构信息
   * @param designInfo 课时作业设计详情
   * @param id 课时作业设计id
   * @param transaction 事务
   */
  async comonCopyCatalog(designInfo, id, transaction) {
    let classWorkDetail = [];
    // 如果课时作业范本id存在，则获取课时作业范本下的作业信息、试题信息、结构信息
    if (designInfo.class_work_id) {
      classWorkDetail = await ClassWorkDetail.findAll({
        where: {
          class_work_id: designInfo.class_work_id,
          status: '发布', //查询已发布的范本作业，未发布不做同步
        },
        include: [{ model: ClassWorkQuestion }, { model: ClassWorkStruct }],
      });
    }

    // 根据教材名录id, 获取对应教材目录信息
    const textbookCatalogs = await TextbookCatalog.findAll({
      where: {
        textbookChecklist_id: designInfo.textbookChecklist_id,
      },
      order: [['id', 'ASC']], //避免复制目录层级错乱
    });

    // 如果教材目录信息存在，则复制教材目录信息及作业信息、试题信息、结构信息
    if (textbookCatalogs && textbookCatalogs.length > 0) {
      // await this.lessonWorkCatalogService.copyCatalog(
      //   textbookCatalogs,
      //   id,
      //   transaction,
      //   classWorkDetail
      // );
      // 使用优化后的逻辑复制对应信息
      await this.lessonWorkCatalogService.copyCatalogNew(
        textbookCatalogs,
        id,
        transaction,
        classWorkDetail
      );
    }
  }
}
