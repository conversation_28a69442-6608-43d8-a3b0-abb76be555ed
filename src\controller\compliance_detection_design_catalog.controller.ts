import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ComplianceDetectionDesignCatalogService } from '../service/compliance_detection_design_catalog.service';
import { CustomError } from '../error/custom.error';

@Controller('/compliance_detection_design_catalog')
export class ComplianceDetectionDesignCatalogController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ComplianceDetectionDesignCatalogService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['sort_order', 'ASC']],
    });
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() body: any) {
    // 取出参数中的old_id，此时的old_id是上级目录的id，要根据id查询上级目录的old_id
    const { old_id } = body;
    if (old_id) {
      const res = await this.service.findById(old_id);
      if (!res) {
        throw new CustomError('old_id无效');
      }
      body.old_id = res.old_id;
      body.old_title = res.old_title;
    }
    const res = await this.service.create(body);
    return res;
  }

  @Put('/:id', { summary: '编辑' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }

  @Post('/insert/:id', { summary: '插入节点' })
  async insert(
    @Param('id') id: number,
    @Body()
    body: { title: string; old_id: number | null; action: 'insert' | 'append' }
  ) {
    const { title, old_id, action } = body;
    if (!title || !action) {
      throw new CustomError('参数错误');
    }
    if (!['insert', 'append'].includes(action)) {
      throw new CustomError('操作类型错误');
    }
    let o_id = null;
    let o_title = null;
    if (old_id) {
      const res = await this.service.findById(old_id);
      if (!res) {
        throw new CustomError('old_id无效');
      }
      o_id = res.old_id;
      o_title = res.old_title;
    }
    return this.service.insert(id, title, o_id, o_title, action);
  }
}
