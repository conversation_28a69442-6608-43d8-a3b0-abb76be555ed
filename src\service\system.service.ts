import { Logger, Provide, Scope, ScopeEnum } from '@midwayjs/core';
import {
  Dictionary,
  Area,
  Feature,
  Permission,
  Role,
  User,
  UserAttributes,
  Subject,
  Publisher,
} from '../entity';
import {
  initData_areas,
  initData_dictionary,
  initData_feature,
  initData_permission,
  initData_publisher,
  initData_subject,
} from '../common/InitData';

@Provide()
@Scope(ScopeEnum.Request, { allowDowngrade: true })
export class SystemService {
  @Logger()
  logger;

  async checkDictionary() {
    const dictionaryCount = await Dictionary.count();
    if (!dictionaryCount) {
      this.logger.info('初始化字典数据');
      await Dictionary.bulkCreate(initData_dictionary);
    }
  }

  async checkArea() {
    const areaCount = await Area.count();
    if (!areaCount) {
      this.logger.info('初始化地区数据');
      await Area.bulkCreate(initData_areas);
    }
  }

  async checkFeature() {
    const feature = await Feature.findOne({
      where: {
        name: '首页',
      },
    });
    if (!feature) {
      this.logger.info('初始化功能数据');
      // 清空表然后批量插入
      await Feature.destroy({ where: {} });
      await Feature.bulkCreate(initData_feature);
    }
  }

  async checkPermission() {
    const permission = await Permission.findOne({
      where: {
        name: '查看',
      },
    });
    if (!permission) {
      this.logger.info('初始化权限数据');
      await Permission.destroy({ where: {} });
      await Permission.bulkCreate(initData_permission);
    }
  }

  async checkRole() {
    const role = await Role.findOne({
      where: {
        name: '管理员',
      },
    });
    if (!role) {
      this.logger.info('初始化角色数据');
      const admin = await Role.create({
        name: '管理员',
        description: '内置身份',
      });
      await admin.$set(
        'permissions',
        await Permission.findAll({ where: { name: '管理' } })
      );
    }
  }

  async checkUser() {
    const user = await User.findOne({
      where: {
        username: 'admin',
      },
    });
    if (!user) {
      this.logger.info('初始化管理员账号');
      const adminUser: Omit<UserAttributes, 'id'> = {
        username: 'admin',
        password: 'admin',
        nickname: '系统管理员',
      };
      const user = await User.create(adminUser);
      user.$set('roles', await Role.findOne({ where: { name: '管理员' } }));
    }
  }

  async checkSubject() {
    const subjectCount = await Subject.count();
    if (!subjectCount) {
      this.logger.info('初始化科目数据');
      await Subject.bulkCreate(initData_subject);
    }
  }

  async checkPublisher() {
    const publisherCount = await Publisher.count();
    if (!publisherCount) {
      this.logger.info('初始化出版单位数据');
      await Publisher.bulkCreate(initData_publisher);
    }
  }

  async checkAndInitSystem() {
    try {
      await this.checkDictionary();
      await this.checkArea();
      await this.checkFeature();
      await this.checkPermission();
      await this.checkRole();
      await this.checkUser();
      await this.checkSubject();
      await this.checkPublisher();
    } catch (error) {
      console.error(error);
    }
  }
}
