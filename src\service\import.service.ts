import { Inject, Provide } from '@midwayjs/core';
import { CustomError } from '../error/custom.error';
import { Context } from '@midwayjs/koa';
import { CustomLogger } from '../common/CustomLogger';
import { ExcelColumnImportOptions, ExcelUtil } from '../common/ExcelUtil';
import { Subject } from '../entity';

@Provide()
export class ImportService {
  @Inject()
  ctx: Context;

  @Inject()
  logger: CustomLogger;

  /**
   * 导入学科
   *
   * @param {{ filePath: string }} params params
   * @param {string} params.filePath 上传好的临时文件路径
   * @return {*} res
   * @memberof ImportService
   */
  async importSubjects(params: { filePath: string }) {
    try {
      const { filePath } = params;

      // 定义Excel导入选项
      const options: {
        startLineNo?: number;
        columns: ExcelColumnImportOptions[];
      } = {
        startLineNo: 3, // 从第三行开始读取数据
        columns: [
          {
            title: '学段',
            enName: 'grade_section',
            type: 'string',
          },
          {
            title: '学科',
            enName: 'subject',
            type: 'string',
          },
        ],
      };

      // 从Excel文件读取数据
      const { data, errInfo } = ExcelUtil.readToJson(filePath, options);
      if (errInfo && Object.keys(errInfo).length) {
        throw new CustomError(JSON.stringify(errInfo));
      }

      // 处理Excel数据，转换为学科信息
      const subjects = data.map(item => {
        const { grade_section, subject } = item;
        const info: any = { subject };

        // 根据年级设置学段
        switch (grade_section) {
          case '小学':
            info.grade_section = 'PRIMARY';
            break;
          case '初中':
            info.grade_section = 'MIDDLE';
            break;
          case '高中':
            info.grade_section = 'HIGH';
            break;
          default:
            throw new CustomError(`年级${grade_section}不正确`);
        }
        return info;
      });

      this.logger.info({
        source: 'debug',
        message: `开始导入学科，共${data.length}条数据`,
      });

      console.time('导入学科数据');
      let transaction;
      try {
        // 开始数据库事务
        transaction = await Subject.sequelize.transaction();

        // 批量处理学科数据
        await Promise.all(
          subjects.map(async (subject, index) => {
            try {
              // 检查学科是否已存在
              const existingSubject = await Subject.findOne({
                where: {
                  grade_section: subject.grade_section,
                  subject: subject.subject,
                },
                transaction,
              });

              // 更新或创建学科
              if (existingSubject) {
                console.log('更新学科：', subject.subject);
                await existingSubject.update({ ...subject }, { transaction });
              } else {
                console.log('创建学科：', subject.subject);
                await Subject.create({ ...subject }, { transaction });
              }
            } catch (error) {
              console.error('处理学科数据时出错:', error);
              throw new CustomError(
                `第${index + 4}行数据处理失败: ${error.message}`
              );
            }
          })
        );

        // 提交事务
        await transaction.commit();
      } catch (error) {
        // 回滚事务
        if (transaction) {
          await transaction.rollback();
        }
        throw new CustomError(`导入学科失败: ${error.message}`);
      }

      console.timeEnd('导入学科数据');
      this.logger.info({
        source: 'debug',
        message: '【导入学科】成功',
      });
      return true;
    } catch (error) {
      this.logger.info({
        source: 'debug',
        message: '【导入学科】失败',
        username: this.ctx.state.user?.username || '系统',
        details: JSON.stringify(error),
      });
      throw new CustomError(error.message);
    }
  }
}
