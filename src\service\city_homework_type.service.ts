import { Inject, Provide } from '@midwayjs/core';
import { CityHomeworkType } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class CityHomeworkTypeService extends BaseService<CityHomeworkType> {
  @Inject()
  ctx: Context;

  constructor() {
    super('市级作业设计类型');
  }
  getModel = () => {
    return CityHomeworkType;
  };
}
