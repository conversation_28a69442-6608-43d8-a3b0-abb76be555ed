import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { PlanCoursePrincipleService } from '../service/plan_course_principle.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { PlanTrainTarget } from '../entity';

@Controller('/plan_course_principle')
export class PlanCoursePrincipleController {
  @Inject()
  ctx: Context;

  @Inject()
  service: PlanCoursePrincipleService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // 基本原则名称和内容模糊查询
    if (queryInfo.name) {
      queryInfo.name = {
        [Op.like]: `%${queryInfo.name}%`,
      };
    }
    if (queryInfo.describe) {
      queryInfo.describe = {
        [Op.like]: `%${queryInfo.describe}%`,
      };
    }

    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      include: [{ model: PlanTrainTarget, attributes: ['id', 'name'] }],
    });
  }

  @Get('/:id', { summary: '查询单个' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() body: any) {
    const res = await this.service.create(body);
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Del('/', { summary: '批量删除' })
  async bulkDestroy(@Body() body: any) {
    const { ids } = body;
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new CustomError('参数ids必须是一个非空数组');
    }
    await this.service.bulkDestroy(ids);
  }
}
