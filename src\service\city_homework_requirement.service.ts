import { Inject, Provide } from '@midwayjs/core';
import { CityHomeworkRequirement } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class CityHomeworkRequirementService extends BaseService<CityHomeworkRequirement> {
  @Inject()
  ctx: Context;

  constructor() {
    super('市级作业设计要求');
  }
  getModel = () => {
    return CityHomeworkRequirement;
  };
}
