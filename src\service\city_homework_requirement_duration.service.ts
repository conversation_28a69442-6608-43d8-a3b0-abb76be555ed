import { Inject, Provide } from '@midwayjs/core';
import { CityHomeworkRequirementDuration } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class CityHomeworkRequirementDurationService extends BaseService<CityHomeworkRequirementDuration> {
  @Inject()
  ctx: Context;

  constructor() {
    super('市级学科设计要求时长');
  }
  getModel = () => {
    return CityHomeworkRequirementDuration;
  };
}
