/*
 * @Description: 鉴权错误过滤器
 * @Date: 2025-01-09 10:56:08
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-01-22 13:26:04
 */
import { Catch } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { Axios } from '@midwayjs/axios';
import { ErrorCode } from '../common/ErrorCode';

@Catch(Axios.AxiosError)
export class AxiosFilter {
  async catch(err: Axios.AxiosError, ctx: Context) {
    // Axios错误会到这里
    console.log('Axios错误：', err.response);
    ctx.logger.error(err);
    return {
      errCode: ErrorCode.BIZ_ERROR,
      msg: (err.response?.data as any)?.message || '请求失败',
    };
  }
}
