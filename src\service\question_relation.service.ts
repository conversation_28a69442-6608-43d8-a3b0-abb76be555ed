import { Inject, Provide } from '@midwayjs/core';
import { QuestionRelation } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class QuestionRelationService extends BaseService<QuestionRelation> {
  @Inject()
  ctx: Context;

  constructor() {
    super('题型关系表');
  }
  getModel = () => {
    return QuestionRelation;
  };

  /**
   * 批量创建题型关系
   * @param data 数据
   * @param transaction 事务
   * @return res
   */
  async bulkCreate(data, transaction = null) {
    const res = QuestionRelation.bulkCreate(data, { transaction });
    return res;
  }
}
