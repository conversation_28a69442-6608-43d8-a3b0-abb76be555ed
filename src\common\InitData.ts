import {
  FeatureAttributes,
  PermissionAttributes,
  PublisherAttributes,
  SubjectAttributes,
} from '../entity';
import { DictionaryAttributes } from '../entity/dictionary.entity';
import { AreaAttributes } from '../entity/area.entity';

/** 字典初始数据 */
export const initData_dictionary: Omit<DictionaryAttributes, 'id'>[] = [
  {
    type: 'grade_section',
    code: 'PRIMARY',
    name: '小学',
    description: '小学阶段',
    sortOrder: 1,
    status: 1,
  },
  {
    type: 'grade_section',
    code: 'MIDDLE',
    name: '初中',
    description: '初中阶段',
    sortOrder: 2,
    status: 1,
  },
  {
    type: 'grade_section',
    code: 'HIGH',
    name: '高中',
    description: '高中阶段',
    sortOrder: 3,
    status: 1,
  },
  {
    type: 'grade',
    code: '1',
    name: '一年级',
    description: '小学一年级',
    sortOrder: 1,
    status: 1,
  },
  {
    type: 'grade',
    code: '2',
    name: '二年级',
    description: '小学二年级',
    sortOrder: 2,
    status: 1,
  },
  {
    type: 'grade',
    code: '3',
    name: '三年级',
    description: '小学三年级',
    sortOrder: 3,
    status: 1,
  },
  {
    type: 'grade',
    code: '4',
    name: '四年级',
    description: '小学四年级',
    sortOrder: 4,
    status: 1,
  },
  {
    type: 'grade',
    code: '5',
    name: '五年级',
    description: '小学五年级',
    sortOrder: 5,
    status: 1,
  },
  {
    type: 'grade',
    code: '6',
    name: '六年级',
    description: '小学六年级',
    sortOrder: 6,
    status: 1,
  },
  {
    type: 'grade',
    code: '7',
    name: '初一',
    description: '初中一年级',
    sortOrder: 7,
    status: 1,
  },
  {
    type: 'grade',
    code: '8',
    name: '初二',
    description: '初中二年级',
    sortOrder: 8,
    status: 1,
  },
  {
    type: 'grade',
    code: '9',
    name: '初三',
    description: '初中三年级',
    sortOrder: 9,
    status: 1,
  },
  {
    type: 'grade',
    code: '10',
    name: '高一',
    description: '高中一年级',
    sortOrder: 10,
    status: 1,
  },
  {
    type: 'grade',
    code: '11',
    name: '高二',
    description: '高中二年级',
    sortOrder: 11,
    status: 1,
  },
  {
    type: 'grade',
    code: '12',
    name: '高三',
    description: '高中三年级',
    sortOrder: 12,
    status: 1,
  },
  {
    type: 'school_system',
    code: 'PRIMARY',
    name: '小学',
    description: '实施小学教育的学制',
    sortOrder: 1,
    status: 1,
  },
  {
    type: 'school_system',
    code: 'MIDDLE',
    name: '初中',
    description: '实施初中教育的学制',
    sortOrder: 2,
    status: 1,
  },
  {
    type: 'school_system',
    code: 'HIGH',
    name: '高中',
    description: '实施高中教育的学制',
    sortOrder: 3,
    status: 1,
  },
  {
    type: 'school_system',
    code: 'YEAR9',
    name: '九年一贯制',
    description: '小学和初中连贯教育的学制',
    sortOrder: 4,
    status: 1,
  },
  {
    type: 'school_system',
    code: 'YEAR12',
    name: '十二年一贯制',
    description: '小学、初中和高中连贯教育的学制',
    sortOrder: 5,
    status: 1,
  },
  {
    type: 'school_system',
    code: 'YEAR6',
    name: '完全中学',
    description: '同时实施初中和高中教育的学制',
    sortOrder: 6,
    status: 1,
  },
  {
    type: 'enterprise_type',
    code: 'SCHOOL',
    name: '学校',
    description: '单位类型为学校',
    sortOrder: 1,
    status: 1,
  },
  {
    type: 'enterprise_type',
    code: 'SERVER',
    name: '服务商',
    description: '单位类型为服务商',
    sortOrder: 2,
    status: 1,
  },
  {
    type: 'user_type',
    code: 'TEACHER',
    name: '教师',
    description: '教师',
    sortOrder: 1,
    status: 1,
  },
  {
    type: 'user_type',
    code: 'STUDENT',
    name: '学生',
    description: '学生',
    sortOrder: 2,
    status: 1,
  },
  {
    type: 'user_type',
    code: 'PARENT',
    name: '家长',
    description: '家长',
    sortOrder: 3,
    status: 1,
  },
  {
    type: 'user_type',
    code: 'MANAGER',
    name: '管理人员',
    description: '管理人员',
    sortOrder: 4,
    status: 1,
  },
  {
    type: 'question_type',
    code: 'SINGLE_CHOICE',
    name: '单选题',
    sortOrder: 1,
    status: 1,
  },
  {
    type: 'question_type',
    code: 'MULTIPLE_CHOICE',
    name: '多选题',
    sortOrder: 2,
    status: 1,
  },
  {
    type: 'question_type',
    code: 'JUDGMENT',
    name: '判断题',
    sortOrder: 3,
    status: 1,
  },
  {
    type: 'question_type',
    code: 'FILL_IN_THE_BLANK',
    name: '填空题',
    sortOrder: 4,
    status: 1,
  },
  {
    type: 'question_type',
    code: 'SUBJECTIVE',
    name: '主观题',
    sortOrder: 5,
    status: 1,
  },
  {
    type: 'book_source',
    code: 'PLATFORM',
    name: '平台',
    sortOrder: 1,
    status: 1,
  },
  {
    type: 'book_source',
    code: 'HU_JU_BI_JI',
    name: '湖居笔记',
    sortOrder: 2,
    status: 1,
  },
];

/** 行政区划初始数据 */
export const initData_areas: Omit<AreaAttributes, 'id'>[] = [
  { code: '610000', name: '陕西省', parentCode: null },
  { code: '610100', name: '西安市', parentCode: '610000' },
  { code: '610101', name: '市辖区', parentCode: '610100' },
  { code: '610102', name: '新城区', parentCode: '610100' },
  { code: '610103', name: '碑林区', parentCode: '610100' },
  { code: '610104', name: '莲湖区', parentCode: '610100' },
  { code: '610111', name: '灞桥区', parentCode: '610100' },
  { code: '610112', name: '未央区', parentCode: '610100' },
  { code: '610113', name: '雁塔区', parentCode: '610100' },
  { code: '610114', name: '阎良区', parentCode: '610100' },
  { code: '610115', name: '临潼区', parentCode: '610100' },
  { code: '610116', name: '长安区', parentCode: '610100' },
  { code: '610117', name: '西咸新区', parentCode: '610100' },
  { code: '610122', name: '蓝田县', parentCode: '610100' },
  { code: '610124', name: '周至县', parentCode: '610100' },
  { code: '610125', name: '鄠邑区', parentCode: '610100' },
  { code: '610126', name: '高陵区', parentCode: '610100' },
  { code: '610127', name: '经开区', parentCode: '610100' },
  { code: '610128', name: '高新区', parentCode: '610100' },
  { code: '610129', name: '曲江新区', parentCode: '610100' },
  { code: '610130', name: '浐灞生态区', parentCode: '610100' },
  { code: '610131', name: '航天基地', parentCode: '610100' },
  { code: '610132', name: '航空基地', parentCode: '610100' },
  { code: '610133', name: '国际港务区', parentCode: '610100' },
  { code: '610200', name: '铜川市', parentCode: '610000' },
  { code: '610201', name: '市辖区', parentCode: '610200' },
  { code: '610202', name: '王益区', parentCode: '610200' },
  { code: '610203', name: '印台区', parentCode: '610200' },
  { code: '610204', name: '耀州区', parentCode: '610200' },
  { code: '610222', name: '宜君县', parentCode: '610200' },
  { code: '610300', name: '宝鸡市', parentCode: '610000' },
  { code: '610301', name: '市辖区', parentCode: '610300' },
  { code: '610302', name: '渭滨区', parentCode: '610300' },
  { code: '610303', name: '金台区', parentCode: '610300' },
  { code: '610304', name: '陈仓区', parentCode: '610300' },
  { code: '610322', name: '凤翔县', parentCode: '610300' },
  { code: '610323', name: '岐山县', parentCode: '610300' },
  { code: '610324', name: '扶风县', parentCode: '610300' },
  { code: '610326', name: '眉县', parentCode: '610300' },
  { code: '610327', name: '陇县', parentCode: '610300' },
  { code: '610328', name: '千阳县', parentCode: '610300' },
  { code: '610329', name: '麟游县', parentCode: '610300' },
  { code: '610330', name: '凤县', parentCode: '610300' },
  { code: '610331', name: '太白县', parentCode: '610300' },
  { code: '610400', name: '咸阳市', parentCode: '610000' },
  { code: '610401', name: '市辖区', parentCode: '610400' },
  { code: '610402', name: '秦都区', parentCode: '610400' },
  { code: '610403', name: '杨陵区', parentCode: '610400' },
  { code: '610404', name: '渭城区', parentCode: '610400' },
  { code: '610422', name: '三原县', parentCode: '610400' },
  { code: '610423', name: '泾阳县', parentCode: '610400' },
  { code: '610424', name: '乾县', parentCode: '610400' },
  { code: '610425', name: '礼泉县', parentCode: '610400' },
  { code: '610426', name: '永寿县', parentCode: '610400' },
  { code: '610427', name: '彬县', parentCode: '610400' },
  { code: '610428', name: '长武县', parentCode: '610400' },
  { code: '610429', name: '旬邑县', parentCode: '610400' },
  { code: '610430', name: '淳化县', parentCode: '610400' },
  { code: '610431', name: '武功县', parentCode: '610400' },
  { code: '610481', name: '兴平市', parentCode: '610400' },
  { code: '610500', name: '渭南市', parentCode: '610000' },
  { code: '610501', name: '市辖区', parentCode: '610500' },
  { code: '610502', name: '临渭区', parentCode: '610500' },
  { code: '610521', name: '华县', parentCode: '610500' },
  { code: '610522', name: '潼关县', parentCode: '610500' },
  { code: '610523', name: '大荔县', parentCode: '610500' },
  { code: '610524', name: '合阳县', parentCode: '610500' },
  { code: '610525', name: '澄城县', parentCode: '610500' },
  { code: '610526', name: '蒲城县', parentCode: '610500' },
  { code: '610527', name: '白水县', parentCode: '610500' },
  { code: '610528', name: '富平县', parentCode: '610500' },
  { code: '610581', name: '韩城市', parentCode: '610500' },
  { code: '610582', name: '华阴市', parentCode: '610500' },
  { code: '610600', name: '延安市', parentCode: '610000' },
  { code: '610601', name: '市辖区', parentCode: '610600' },
  { code: '610602', name: '宝塔区', parentCode: '610600' },
  { code: '610621', name: '延长县', parentCode: '610600' },
  { code: '610622', name: '延川县', parentCode: '610600' },
  { code: '610623', name: '子长县', parentCode: '610600' },
  { code: '610624', name: '安塞县', parentCode: '610600' },
  { code: '610625', name: '志丹县', parentCode: '610600' },
  { code: '610626', name: '吴起县', parentCode: '610600' },
  { code: '610627', name: '甘泉县', parentCode: '610600' },
  { code: '610628', name: '富县', parentCode: '610600' },
  { code: '610629', name: '洛川县', parentCode: '610600' },
  { code: '610630', name: '宜川县', parentCode: '610600' },
  { code: '610631', name: '黄龙县', parentCode: '610600' },
  { code: '610632', name: '黄陵县', parentCode: '610600' },
  { code: '610700', name: '汉中市', parentCode: '610000' },
  { code: '610701', name: '市辖区', parentCode: '610700' },
  { code: '610702', name: '汉台区', parentCode: '610700' },
  { code: '610721', name: '南郑县', parentCode: '610700' },
  { code: '610722', name: '城固县', parentCode: '610700' },
  { code: '610723', name: '洋县', parentCode: '610700' },
  { code: '610724', name: '西乡县', parentCode: '610700' },
  { code: '610725', name: '勉县', parentCode: '610700' },
  { code: '610726', name: '宁强县', parentCode: '610700' },
  { code: '610727', name: '略阳县', parentCode: '610700' },
  { code: '610728', name: '镇巴县', parentCode: '610700' },
  { code: '610729', name: '留坝县', parentCode: '610700' },
  { code: '610730', name: '佛坪县', parentCode: '610700' },
  { code: '610800', name: '榆林市', parentCode: '610000' },
  { code: '610801', name: '市辖区', parentCode: '610800' },
  { code: '610802', name: '榆阳区', parentCode: '610800' },
  { code: '610821', name: '神木县', parentCode: '610800' },
  { code: '610822', name: '府谷县', parentCode: '610800' },
  { code: '610823', name: '横山县', parentCode: '610800' },
  { code: '610824', name: '靖边县', parentCode: '610800' },
  { code: '610825', name: '定边县', parentCode: '610800' },
  { code: '610826', name: '绥德县', parentCode: '610800' },
  { code: '610827', name: '米脂县', parentCode: '610800' },
  { code: '610828', name: '佳县', parentCode: '610800' },
  { code: '610829', name: '吴堡县', parentCode: '610800' },
  { code: '610830', name: '清涧县', parentCode: '610800' },
  { code: '610831', name: '子洲县', parentCode: '610800' },
  { code: '610900', name: '安康市', parentCode: '610000' },
  { code: '610901', name: '市辖区', parentCode: '610900' },
  { code: '610902', name: '汉滨区', parentCode: '610900' },
  { code: '610921', name: '汉阴县', parentCode: '610900' },
  { code: '610922', name: '石泉县', parentCode: '610900' },
  { code: '610923', name: '宁陕县', parentCode: '610900' },
  { code: '610924', name: '紫阳县', parentCode: '610900' },
  { code: '610925', name: '岚皋县', parentCode: '610900' },
  { code: '610926', name: '平利县', parentCode: '610900' },
  { code: '610927', name: '镇坪县', parentCode: '610900' },
  { code: '610928', name: '旬阳县', parentCode: '610900' },
  { code: '610929', name: '白河县', parentCode: '610900' },
  { code: '611000', name: '商洛市', parentCode: '610000' },
  { code: '611001', name: '市辖区', parentCode: '611000' },
  { code: '611002', name: '商州区', parentCode: '611000' },
  { code: '611021', name: '洛南县', parentCode: '611000' },
  { code: '611022', name: '丹凤县', parentCode: '611000' },
  { code: '611023', name: '商南县', parentCode: '611000' },
  { code: '611024', name: '山阳县', parentCode: '611000' },
  { code: '611025', name: '镇安县', parentCode: '611000' },
  { code: '611026', name: '柞水县', parentCode: '611000' },
];

/** 功能点初始数据 */
export const initData_feature: Omit<FeatureAttributes, 'id' | 'permissions'>[] =
  [
    { code: '010000', name: '首页', path: '', orderIndex: 1 },
    { code: '020000', name: '课程方案管理', path: '', orderIndex: 2 },
    { code: '030000', name: '课程标准管理', path: '', orderIndex: 3 },
    { code: '040000', name: '学科教材管理', path: '', orderIndex: 4 },
    {
      code: '040100',
      name: '义务教育学科管理',
      path: '',
      parentCode: '040000',
      orderIndex: 1,
    },
    {
      code: '040200',
      name: '出版单位管理',
      path: '',
      parentCode: '040000',
      orderIndex: 2,
    },
    {
      code: '040300',
      name: '教材版本管理',
      path: '',
      parentCode: '040000',
      orderIndex: 3,
    },
    {
      code: '040400',
      name: '教材名录库管理',
      path: '',
      parentCode: '040000',
      orderIndex: 4,
    },
    {
      code: '040500',
      name: '教材章节目录管理',
      path: '',
      parentCode: '040000',
      orderIndex: 5,
    },
    {
      code: '040600',
      name: '教材选用管理',
      path: '',
      parentCode: '040000',
      orderIndex: 6,
    },
    { code: '050000', name: '省级作业设计实施管理', path: '', orderIndex: 5 },
    { code: '060000', name: '市级作业设计指南管理', path: '', orderIndex: 6 },
    { code: '070000', name: '课时作业范本管理', path: '', orderIndex: 7 },
    { code: '080000', name: '达标检测范本管理', path: '', orderIndex: 8 },
    { code: '090000', name: '课时作业设计管理', path: '', orderIndex: 9 },
    { code: '100000', name: '达标检测设计管理', path: '', orderIndex: 10 },
    { code: '110000', name: '作业组卷管理', path: '', orderIndex: 11 },
    { code: '120000', name: '作业题库管理', path: '', orderIndex: 12 },
    { code: '980000', name: '基础数据管理', path: '', orderIndex: 98 },
    {
      code: '980100',
      name: '学年学期',
      path: '',
      parentCode: '980000',
      orderIndex: 1,
    },
    {
      code: '980200',
      name: '行政区划',
      path: '',
      parentCode: '980000',
      orderIndex: 2,
    },
    {
      code: '980300',
      name: '数据字典',
      path: '',
      parentCode: '980000',
      orderIndex: 3,
    },
    { code: '990000', name: '系统管理', path: '', orderIndex: 99 },
    {
      code: '990100',
      name: '单位管理',
      path: '',
      parentCode: '990000',
      orderIndex: 1,
    },
    {
      code: '990200',
      name: '用户管理',
      path: '',
      parentCode: '990000',
      orderIndex: 2,
    },
    {
      code: '990300',
      name: '角色管理',
      path: '',
      parentCode: '990000',
      orderIndex: 3,
    },
    {
      code: '990400',
      name: '权限点维护',
      path: '',
      parentCode: '990000',
      orderIndex: 4,
    },
    {
      code: '990500',
      name: '系统功能维护',
      path: '',
      parentCode: '990000',
      orderIndex: 5,
    },
  ];

/** 权限点初始数据 */
export const initData_permission: Omit<PermissionAttributes, 'id'>[] = [
  { name: '查看', featureCode: '010000', description: '查看首页' },
  { name: '管理', featureCode: '010000', description: '管理首页' },
  { name: '查看', featureCode: '020000', description: '查看课程方案' },
  { name: '管理', featureCode: '020000', description: '维护课程方案' },
  { name: '查看', featureCode: '030000', description: '查看课程标准' },
  { name: '管理', featureCode: '030000', description: '维护课程标准' },
  { name: '查看', featureCode: '040100', description: '查看学科信息' },
  { name: '管理', featureCode: '040100', description: '维护学科信息' },
  { name: '查看', featureCode: '040200', description: '查看出版单位' },
  { name: '管理', featureCode: '040200', description: '维护出版单位' },
  { name: '查看', featureCode: '040300', description: '查看教材版本' },
  { name: '管理', featureCode: '040300', description: '维护教材版本' },
  { name: '查看', featureCode: '040400', description: '查看教材名录库' },
  { name: '管理', featureCode: '040400', description: '维护教材名录库' },
  { name: '查看', featureCode: '040500', description: '查看教材章节目录' },
  { name: '管理', featureCode: '040500', description: '维护教材章节目录' },
  { name: '查看', featureCode: '040600', description: '查看教材选用信息' },
  { name: '管理', featureCode: '040600', description: '维护教材选用信息' },
  {
    name: '查看',
    featureCode: '050000',
    description: '查看省级作业设计实施信息',
  },
  {
    name: '管理',
    featureCode: '050000',
    description: '维护省级作业设计实施信息',
  },
  {
    name: '查看',
    featureCode: '060000',
    description: '查看市级作业设计指南信息',
  },
  {
    name: '管理',
    featureCode: '060000',
    description: '维护市级作业设计指南信息',
  },
  { name: '管理', featureCode: '070000', description: '维护课时作业范本' },
  { name: '管理', featureCode: '080000', description: '维护达标检测范本' },
  { name: '管理', featureCode: '090000', description: '课时作业设计' },
  { name: '管理', featureCode: '100000', description: '达标检测设计' },
  { name: '管理', featureCode: '110000', description: '作业组卷' },
  { name: '管理', featureCode: '120000', description: '维护作业题库' },
  { name: '查看', featureCode: '980100', description: '查看学年学期信息' },
  { name: '管理', featureCode: '980100', description: '维护学年学期信息' },
  { name: '查看', featureCode: '980200', description: '查看行政区划信息' },
  { name: '管理', featureCode: '980200', description: '维护行政区划信息' },
  { name: '查看', featureCode: '980300', description: '查看数据字典' },
  { name: '管理', featureCode: '980300', description: '维护数据字典' },
  { name: '查看', featureCode: '990100', description: '查看注册单位列表' },
  { name: '管理', featureCode: '990100', description: '维护注册单位信息' },
  { name: '查看', featureCode: '990200', description: '查看用户' },
  { name: '管理', featureCode: '990200', description: '管理用户' },
  { name: '查看', featureCode: '990300', description: '查看角色' },
  { name: '管理', featureCode: '990300', description: '管理角色' },
  { name: '查看', featureCode: '990400', description: '查看权限点' },
  { name: '管理', featureCode: '990400', description: '维护权限点' },
  { name: '查看', featureCode: '990500', description: '查看功能点' },
  { name: '管理', featureCode: '990500', description: '维护功能点' },
];

/** 学科初始数据 */
export const initData_subject: Omit<SubjectAttributes, 'id'>[] = [
  { grade_section: 'PRIMARY', subject: '语文' },
  { grade_section: 'MIDDLE', subject: '语文' },
  { grade_section: 'HIGH', subject: '语文' },
  { grade_section: 'PRIMARY', subject: '数学' },
  { grade_section: 'MIDDLE', subject: '数学' },
  { grade_section: 'HIGH', subject: '数学' },
  { grade_section: 'PRIMARY', subject: '英语' },
  { grade_section: 'MIDDLE', subject: '英语' },
  { grade_section: 'HIGH', subject: '英语' },
];

/** 出版单位初始数据 */
export const initData_publisher: Omit<PublisherAttributes, 'id'>[] = [
  {
    name: '人民教育出版社',
    description:
      '中国最大的教育出版社之一，主要出版教科书和教辅材料，其出版物在国内教育领域具有广泛的影响力和认可度，如部编版语文教材及配套教辅等。',
  },
  { name: '清华大学出版社' },
  { name: '北京大学出版社' },
  { name: '中国人民大学出版社' },
];
