import { Inject, Provide } from '@midwayjs/core';
import { ComposerPaper } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { Util } from '../common/Util';
import { CustomError } from '../error/custom.error';
import { ComposerTestPaperService } from './composer_test_paper.service';
import { CustomLogger } from '../common/CustomLogger';

@Provide()
export class ComposerPaperService extends BaseService<ComposerPaper> {
  @Inject()
  ctx: Context;
  @Inject()
  logger: CustomLogger;

  @Inject()
  composerTestPaperService: ComposerTestPaperService;

  constructor() {
    super('组卷方案');
  }
  getModel = () => {
    return ComposerPaper;
  };

  /**
   * 自动组卷
   * @param params
   * @returns
   */
  async executeAutoComposerPaper(params: any) {
    // 执行自动组卷的逻辑
    const {
      data,
      test_paper_num,
      composer_paper_id,
      isDestroy,
      startTime,
      username,
      composer_paper_name,
    } = params;
    console.log(`Start auto composer paper, count is ${test_paper_num}`);
    const composerPaper = await this.findById(composer_paper_id);
    try {
      if (!composerPaper) {
        throw new CustomError('请选择组卷方案');
      }
      await this.composerTestPaperService.autoBulkCreateTestPaper(
        data,
        composerPaper,
        test_paper_num,
        isDestroy
      );
      await this.update(
        { id: composer_paper_id },
        {
          process: false,
        }
      );
      this.logger.info({
        message: `【智能组卷${composer_paper_name}方案】成功`,
        source: '试卷',
        username: username || '系统',
      });
      return {
        errCode: 0,
        msg: `方案：${composer_paper_name}组卷完成`,
        data: {
          result: test_paper_num,
          duration: Util.getDuration(startTime),
        },
      };
    } catch (error) {
      console.log('🚀 ~ AutoComposerPaper ~ error:', error);
      await this.update(
        { id: composer_paper_id },
        {
          process: false,
        }
      );
      this.logger.error({
        message: `【智能组卷${composer_paper_name}方案】失败`,
        source: '试卷',
        username: username || '系统',
        details: JSON.stringify(error),
      });
      return {
        errCode: 400,
        msg: `方案：${composer_paper_name}组卷失败`,
        data: {
          result: 0,
          duration: Util.getDuration(startTime),
        },
      };
    }
  }

  /**
   * 删除组卷
   * @param params
   * @returns
   */
  async executeDeleteComposerPaper(params: any) {
    const { id, name, isDestroy, startTime, username } = params;
    // 判断isDestroy是否为true，如果为true则删除组卷以及组卷下的试卷，否则为一键清除试卷
    if (isDestroy) {
      try {
        await this.delete({ id });
        this.logger.info({
          message: `【删除组卷】${name}成功`,
          source: '试卷',
          username: username || '系统',
        });
        return {
          errCode: 0,
          msg: '删除组卷成功',
          data: {
            duration: Util.getDuration(startTime),
          },
        };
      } catch (error) {
        this.logger.error({
          message: `【删除组卷】${name}失败`,
          source: 'debug',
          username: username || '系统',
          details: JSON.stringify(error),
        });
        return {
          errCode: 500,
          msg: error.message,
          data: {
            duration: Util.getDuration(startTime),
          },
        };
      }
    } else {
      try {
        // 根据组卷id清空组卷下所有试卷
        await this.composerTestPaperService.destroyByComposerPaperId(id);
        this.logger.info({
          message: `【一键清除组卷】${name}成功`,
          source: '试卷',
          username: username || '系统',
        });
        return {
          errCode: 0,
          msg: '一键清除成功',
          data: {
            duration: Util.getDuration(startTime),
          },
        };
      } catch (error) {
        this.logger.error({
          message: `【一键清除组卷】${name}失败`,
          source: '试卷',
          username: username || '系统',
          details: JSON.stringify(error),
        });
        return {
          errCode: 500,
          msg: error.message,
          data: {
            duration: Util.getDuration(startTime),
          },
        };
      }
    }
  }
}
