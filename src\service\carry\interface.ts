export enum UserIdentity {
  '市级管理员' = 0,
  '区县管理员' = 1,
  '学校管理员' = 2,
  '教师' = 3,
  '学生' = 4,
  '家长' = 5,
}

export interface UserInfo_Carry {
  /** 用户ID */
  id: string;
  /** 用户名 */
  username: string;
  /** 真实姓名 */
  realname: string;
  /** 头像URL */
  avatar: string | null;
  /** 生日 */
  birthday: string | null;
  /** 性别 */
  sex: string | null;
  /** 电子邮箱 */
  email: string;
  /** 手机号码 */
  phone: string;
  /** 组织机构代码 */
  orgCode: string;
  /** 组织机构代码文本描述 */
  orgCodeTxt: string | null;
  /** 所属区域 */
  areaCode: string;
  /** 用户状态 */
  status: number;
  /** 工号 */
  workNo: string;
  /** 任教学段 */
  jobNumber: string;
  /** 任教学科 */
  jobSubject: string;
  /** 创建时间 */
  createTime: string;
  /** 更新者 */
  updateBy: string;
  /** 更新时间 */
  updateTime: string;
  /** 用户身份 */
  userIdentity: UserIdentity;
  /** 部门IDs */
  departIds: string;
}
