import { Rule, RuleType } from '@midwayjs/validate';

export class CreateSemesterDTO {
  @Rule(
    RuleType.string()
      .required()
      .pattern(/^\d{4}-\d{4}$/)
      .length(9)
  )
  year: string;

  @Rule(RuleType.number().valid(1, 2))
  term: number;

  @Rule(RuleType.string().required())
  code: string;

  @Rule(RuleType.date().required())
  start_date: Date;

  @Rule(RuleType.date().required())
  end_date: Date;

  @Rule(RuleType.number().required())
  status: number;
}

export class UpdateSemesterDTO {
  @Rule(
    RuleType.string()
      .pattern(/^\d{4}-\d{4}$/)
      .length(9)
  )
  year: string;

  @Rule(RuleType.number().valid(1, 2))
  term: number;

  @Rule(RuleType.string())
  code: string;

  @Rule(RuleType.date())
  start_date: Date;

  @Rule(RuleType.date())
  end_date: Date;

  @Rule(RuleType.number())
  status: number;
}
