import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ClassWorkStructService } from '../service/class_work_struct.service';
import { CustomError } from '../error/custom.error';
import { ClassWorkDetail } from '../entity';

@Controller('/class_work_struct')
export class ClassWorkStructController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ClassWorkStructService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['sort_order', 'ASC']],
      include: [{ model: ClassWorkDetail }],
    });
  }

  @Get('/:id', { summary: '根据ID查询单个' })
  async get(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    // 获取最大排序索引
    const maxIndex = await this.service.getMaxIndex(info.classworkDetail_id);
    // 创建作业
    const res = await this.service.create({
      ...info,
      sort_order: maxIndex + 1,
    });
    return res;
  }

  @Post('/bulkCreate/:detail_id', { summary: '批量新增' })
  async bulkCreate(@Param('detail_id') detail_id: string, @Body() body: any[]) {
    if (!detail_id) {
      throw new CustomError('缺少必要参数');
    }
    return this.service.bulkCreate(detail_id, body);
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Del('/bulkDelete', { summary: '全部清空' })
  async bulkDelete(@Body() body: any) {
    const { classworkDetail_id } = body;
    if (!classworkDetail_id) {
      throw new CustomError('缺少必要参数');
    }
    return this.service.bulkDelete(classworkDetail_id);
  }
}
