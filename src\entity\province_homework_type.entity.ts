import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';

export interface ProvinceHomeworkTypeAttributes {
  /** 主键ID */
  id: number;
  /** 学段 */
  grade_section_code?: string;
  /** 学段名称 */
  grade_section_name?: string;
  /** 作业目标id */
  // homework_goal_id?: string;
  /** 作业名称 */
  // name: string;
  /** 作业细目 */
  description?: string;
  /** 作业类型 */
  type?: string;
  /** 所属学科名称 */
  subject_name?: string;
  /** 所属学科id */
  subject_id?: number;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({
  tableName: 'province_homework_type',
  timestamps: true,
  comment: '省级作业类型表',
})
export class ProvinceHomeworkType
  extends Model<ProvinceHomeworkTypeAttributes>
  implements ProvinceHomeworkTypeAttributes
{
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学段',
  })
  grade_section_code?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学段名称',
  })
  grade_section_name?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '作业目标id',
  // })
  // homework_goal_id?: string;

  // @Column({
  //   type: DataType.STRING(64),
  //   allowNull: true,
  //   comment: '作业名称',
  // })
  // name: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '描述',
  })
  description?: string;

  @Column({
    type: DataType.ENUM('基础型', '发展型', '拓展型', '实践性', '跨学科型'),
    allowNull: true,
    comment: '作业类型',
  })
  type?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '所属学科名称',
  })
  subject_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '所属学科id',
  })
  subject_id?: number;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    comment: '创建时间',
  })
  createdAt?: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    comment: '更新时间',
  })
  updatedAt?: Date;
}
