import { Inject, Provide } from '@midwayjs/core';
import { ClassRelation } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class ClassRelationService extends BaseService<ClassRelation> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课时关系表');
  }
  getModel = () => {
    return ClassRelation;
  };

  /**
   * 获取列表
   * @param query 请求参数
   */
  async getList(query) {
    const { textbook_config_id, unit, period, similarSourceCode } = query;
    /**
     * 1.只传校本作业id 则只查询当前校本的所有相似题来源
     * 2.如果加上单元和课时，则查询对应单元课时下的相似题来源
     * 3.如果加上相似题来源code, 则查询该来源的可匹配单元和课时
     */
    const sourceQuery: any = {};
    if (textbook_config_id && !unit && !period) {
      sourceQuery.textbook_config_id = textbook_config_id;
    } else if (textbook_config_id) {
      sourceQuery.textbook_config_id = textbook_config_id;
      sourceQuery.ks_value = null;
    }
    // 如果相似题来源不存在，查询对应单元课时相似题来源，如果来源code存在，查询该来源的可匹配单元和课时
    if (!similarSourceCode) {
      sourceQuery.unit = unit;
    } else if (unit && period) {
      sourceQuery.unit = unit;
      sourceQuery.period = period;
    }
    // 如果相似题来源不存在，查询对应单元课时相似题来源，如果来源code存在，查询该来源的可匹配单元和课时
    if (!similarSourceCode) {
      const res = await ClassRelation.findAll({
        where: sourceQuery,
        order: [['createdAt', 'DESC']],
      });
      if (res.length > 0) {
        // 去除重复的相似题来源
        const uniqueSet = new Set(
          res.map(item =>
            JSON.stringify({
              title: item.similarSource,
              key: item.similarSourceCode,
            })
          )
        );
        const result = Array.from(uniqueSet).map(item => JSON.parse(item));
        return result;
      } else {
        sourceQuery.similarSouceCode = similarSourceCode;
        const res = await ClassRelation.findAll({
          where: sourceQuery,
          attributes: ['match_period'],
        });
        const result = [...new Set(res.map(item => item.match_period))];
        return result;
      }
    }
  }

  /**
   * 根据条件删除
   * @param where 条件
   * @param transaction 事务
   */
  async destroy(where, transaction = null) {
    await this.ctx.model.ClassRelation.destroy({
      where,
      transaction,
    });
  }
}
