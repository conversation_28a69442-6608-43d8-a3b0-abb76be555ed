import { Provide } from '@midwayjs/core';
import {
  TextbookCatalog,
  TextbookCatalogAttributes,
} from '../entity/textbook_catalog.entity';
import { BaseService } from '../common/BaseService';
import { Op } from 'sequelize';
import { uniq } from 'lodash';

@Provide()
export class TextbookCatalogService extends BaseService<TextbookCatalog> {
  constructor() {
    super('教材目录');
  }

  getModel() {
    return TextbookCatalog;
  }

  /**
   * 插入节点
   * @param id 参照节点id
   * @param name 新节点名称
   * @param action 操作类型，insert表示插入到参照节点前，append表示插入到参照节点后
   * @returns 新节点
   */
  async insert(id: number, title: string, action: 'insert' | 'append') {
    const catalogInfo = await TextbookCatalog.findOne({
      where: {
        id,
      },
    });
    if (!catalogInfo) {
      throw new Error('参照节点不存在');
    }
    const sort_order: number =
      action === 'append' ? catalogInfo.sort_order + 1 : catalogInfo.sort_order;
    const info: Omit<TextbookCatalogAttributes, 'id'> = {
      /** 教材名录ID */
      textbookChecklist_id: catalogInfo.textbookChecklist_id,
      /** 名称 */
      title,
      /** 父ID */
      parent_id: catalogInfo.parent_id,
      /** 排序 */
      sort_order,
    };
    // 查询相同排序有没有被占用
    const isExist = await TextbookCatalog.findOne({
      where: {
        textbookChecklist_id: catalogInfo.textbookChecklist_id,
        parent_id: catalogInfo.parent_id,
        sort_order,
      },
      attributes: ['id'],
    });
    if (isExist) {
      // 占用了，需要把后面的所有节点的排序都+1
      const todoList: TextbookCatalog[] = await TextbookCatalog.findAll({
        where: {
          textbookChecklist_id: catalogInfo.textbookChecklist_id,
          parent_id: catalogInfo.parent_id,
          sort_order: {
            [Op.gte]: sort_order,
          },
        },
      });
      for (const todo of todoList) {
        todo.sort_order = todo.sort_order + 1;
        await todo.save();
      }
    }
    const newCatalogInfo = await TextbookCatalog.create(info);
    return newCatalogInfo;
  }

  /**
   * 递归获取所有子节点id
   * @param parent_id 父节点
   * @param allNodes 所有节点数据
   * @param result 结果数组，用于存储所有子节点的ID
   */
  async getChildrenIds(parent_id, allNodes, result) {
    const children = allNodes.filter(node => node.parent_id === parent_id);
    for (const child of children) {
      result.push(child.id);
      await this.getChildrenIds(child.id, allNodes, result);
    }
  }

  /**
   * 获取指定父节点及其所有子节点的ID
   * @param id 当前节点id
   * @returns 包括父节点及所有子节点的id数组
   */
  async getIdsByParentId(id) {
    // 根据当前节点id获取当前节点信息
    const catalogInfo = await TextbookCatalog.findOne({
      where: {
        id,
      },
    });
    // 根据当前节点信息，获取教材名录id获取当前教材目录信息
    const allNodes = await TextbookCatalog.findAll({
      where: {
        textbookChecklist_id: catalogInfo.textbookChecklist_id,
      },
    });

    // 初始化结果数组，包含当前节点id
    const result: number[] = [id];

    // 递归获取所有子节点id信息
    await this.getChildrenIds(id, allNodes, result);
    return result;
  }

  /**
   * 根据父节点id获取所有子节点
   * @param parentIds 父节点id数组
   * @returns
   */
  async getAllChildren(parentIds: number[]): Promise<number[]> {
    if (!parentIds || parentIds.length === 0) return [];

    const allChildren = [...parentIds];
    let currentLevelParents = [...parentIds];

    while (currentLevelParents.length > 0) {
      try {
        // 批量查询当前层级的子节点
        const children = await TextbookCatalog.findAll({
          attributes: ['id'],
          where: {
            parent_id: currentLevelParents,
          },
          raw: true, // 提升性能
        });

        if (children.length === 0) break;

        // 提取子节点ID
        const childIds = children.map(c => c.id);
        allChildren.push(...childIds);

        // 准备下一轮查询
        currentLevelParents = childIds;
      } catch (error) {
        console.error('Error fetching children:', error);
        throw new Error('Failed to retrieve child nodes');
      }
    }

    return uniq(allChildren);
  }
}
