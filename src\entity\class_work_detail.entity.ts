import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import {
  ClassWork,
  ClassWorkQuestion,
  TextbookCatalog,
  ClassWorkStruct,
} from '.';
import { TEMPLATE_STATUS } from '../common/Constants';

export interface ClassWorkDetailAttributes {
  /** 主键 */
  id: number;
  /** 课时作业范本id */
  class_work_id: number;
  /** 教材目录id */
  textbookCatalog_id: number;
  /** 名称 */
  name?: string;
  /** 排序 */
  sort_order: number;
  /** 状态 */
  status: string;
  /** 创建人id */
  creator_id?: number;
  /** 创建人 */
  creator_name?: string;
  /** 作业时长 */
  duration?: number;
  classWork?: ClassWork;
  questions?: ClassWorkQuestion[];
  structs?: ClassWorkStruct[];
}

@Table({
  tableName: 'class_work_detail',
  timestamps: true,
  comment: '课时作业范本详情表',
})
export class ClassWorkDetail
  extends Model<ClassWorkDetailAttributes>
  implements ClassWorkDetailAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @ForeignKey(() => ClassWork)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '课时作业范本id',
  })
  class_work_id: number;

  @BelongsTo(() => ClassWork, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  classWork?: ClassWork;

  @ForeignKey(() => TextbookCatalog)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材目录id',
  })
  textbookCatalog_id: number;

  @BelongsTo(() => TextbookCatalog, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  textbookCatalog?: TextbookCatalog;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '名称',
  })
  name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '排序',
  })
  sort_order: number;

  @Column({
    type: DataType.ENUM(TEMPLATE_STATUS.DRAFT, TEMPLATE_STATUS.PUBLISHED),
    allowNull: false,
    defaultValue: TEMPLATE_STATUS.DRAFT,
    comment: '状态',
  })
  status: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '创建人id',
  })
  creator_id?: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '创建人',
  })
  creator_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '作业时长',
  })
  duration?: number;

  @HasMany(() => ClassWorkQuestion)
  questions?: ClassWorkQuestion[];

  @HasMany(() => ClassWorkStruct)
  structs?: ClassWorkStruct[];
}
