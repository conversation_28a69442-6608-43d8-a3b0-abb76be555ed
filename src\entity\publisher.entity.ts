import {
  Table,
  Column,
  Model,
  DataType,
  HasMany,
  Index,
} from 'sequelize-typescript';
import { Textbook } from '.';

export interface PublisherAttributes {
  /** 出版单位ID，主键 */
  id: number;
  /** 出版单位名称 */
  name: string;
  /** 地址 */
  address?: string;
  /** 联系人 */
  contact?: string;
  /** 联系电话 */
  phone?: string;
  /** 单位性质 */
  nature?: string;
  /** 描述 */
  description?: string;
  textbooks?: Textbook[];
}

@Table({ tableName: 'publishers', timestamps: false, comment: '出版单位表' })
export class Publisher
  extends Model<PublisherAttributes>
  implements PublisherAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '出版单位ID，主键',
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '出版单位名称',
  })
  @Index({ name: 'name', unique: true })
  name: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '地址',
  })
  address: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '联系人',
  })
  contact?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '联系电话',
  })
  phone?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '单位性质',
  })
  nature?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '描述',
  })
  description: string;

  @HasMany(() => Textbook, {
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  })
  textbooks: Textbook[];
}
