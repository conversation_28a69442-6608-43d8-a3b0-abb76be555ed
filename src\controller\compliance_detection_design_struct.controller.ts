import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ComplianceDetectionDesignStructService } from '../service/compliance_detection_design_struct.service';
import { CustomError } from '../error/custom.error';
import { ComplianceDetectionDesignDetail } from '../entity';

@Controller('/compliance_detection_design_struct')
export class ComPlianceDetectionDesignStructController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ComplianceDetectionDesignStructService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['sort_order', 'ASC']],
      include: [{ model: ComplianceDetectionDesignDetail }],
    });
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    // 获取最大排序索引
    const maxIndex = await this.service.getMaxIndex(info.designDetail_id);
    const res = await this.service.create({
      ...info,
      sort_order: maxIndex + 1,
    });
    return res;
  }

  @Post('/bulkCreate/:designDetail_id', { summary: '批量新增' })
  async bulkCreate(
    @Param('designDetail_id') designDetail_id: string,
    @Body() info: any
  ) {
    if (!designDetail_id) {
      throw new CustomError('缺少必要参数');
    }
    return this.service.bulkCreate(designDetail_id, info);
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Del('/bulkDelete', { summary: '全部清空' })
  async bulkDelete(@Body() body: any) {
    const { designDetail_id } = body;
    if (!designDetail_id) {
      throw new CustomError('缺少必要参数');
    }
    return this.service.bulkDelete(designDetail_id);
  }
}
