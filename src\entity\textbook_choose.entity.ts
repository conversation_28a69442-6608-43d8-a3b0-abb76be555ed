import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Subject, Textbook, TextbookChooseDetail } from '.';

export interface TextbookChooseAttributes {
  /** ID，主键 */
  id: number;
  /** 学年 */
  semester: string;
  /** 学科ID */
  subject_id: number;
  /** 适用年级，逗号分隔 */
  grade_list: string;
  /** 教材版本ID */
  textbook_id: number;
  subject?: Subject;
  textbook?: Textbook;
  items?: TextbookChooseDetail[];
}

@Table({
  tableName: 'textbook_choose',
  underscored: true,
  timestamps: false,
})
export class TextbookChoose
  extends Model<TextbookChooseAttributes>
  implements TextbookChooseAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    allowNull: false,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  semester: string;

  @ForeignKey(() => Subject)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  subject_id: number;

  @BelongsTo(() => Subject)
  subject: Subject;

  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  grade_list: string;

  @ForeignKey(() => Textbook)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  textbook_id: number;

  @BelongsTo(() => Textbook)
  textbook: Textbook;

  @HasMany(() => TextbookChooseDetail)
  items: TextbookChooseDetail[];
}
