# Dashboard API 文档

## 概述

Dashboard API 提供系统统计数据查询功能，包括企业数量、用户数量等统计信息。

## 基础信息

- **Base URL**: `http://localhost:3130`
- **Content-Type**: `application/json`

## 接口列表

### 1. 获取系统统计概览

**接口地址**: `GET /dashboard/overview`

**功能描述**: 获取系统中企业总数和用户总数的概览信息

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| province_code | string | 否 | 省份编码 |
| city_code | string | 否 | 城市编码 |
| area_code | string | 否 | 区域编码 |
| enterprise_type | string | 否 | 企业类型 |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "enterprise_count": 10,
    "user_count": 23,
    "timestamp": "2025-05-27T07:28:57.189Z"
  }
}
```

### 2. 获取企业总数

**接口地址**: `GET /dashboard/enterprises/count`

**功能描述**: 获取企业总数

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| province_code | string | 否 | 省份编码 |
| city_code | string | 否 | 城市编码 |
| area_code | string | 否 | 区域编码 |
| type | string | 否 | 企业类型 |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "count": 10,
    "timestamp": "2025-05-27T07:29:07.167Z"
  }
}
```

### 3. 获取用户总数

**接口地址**: `GET /dashboard/users/count`

**功能描述**: 获取用户总数

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| province_code | string | 否 | 省份编码 |
| city_code | string | 否 | 城市编码 |
| area_code | string | 否 | 区域编码 |
| enterprise_type | string | 否 | 企业类型 |
| grade_section_code | string | 否 | 学段编码 |
| subject_id | number | 否 | 学科ID |
| is_active | boolean | 否 | 是否激活 |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "count": 23,
    "timestamp": "2025-05-27T07:29:07.167Z"
  }
}
```

### 4. 获取企业统计数据

**接口地址**: `GET /dashboard/enterprises/statistics`

**功能描述**: 获取企业详细统计数据，包括按类型和地区分组的统计

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| province_code | string | 否 | 省份编码 |
| city_code | string | 否 | 城市编码 |
| area_code | string | 否 | 区域编码 |
| type | string | 否 | 企业类型 |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "total": 10,
    "by_type": [
      {"type": null, "count": 2},
      {"type": "SCHOOL", "count": 7},
      {"type": "SERVER", "count": 1}
    ],
    "by_area": [
      {"province_name": "陕西省", "city_name": "西安市", "area_name": "国际港务区", "count": 2},
      {"province_name": "陕西省", "city_name": "西安市", "area_name": "市辖区", "count": 2}
    ],
    "timestamp": "2025-05-27T07:29:07.167Z"
  }
}
```

### 5. 获取用户统计数据

**接口地址**: `GET /dashboard/users/statistics`

**功能描述**: 获取用户详细统计数据，包括按学段、学科、状态分组的统计

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| province_code | string | 否 | 省份编码 |
| city_code | string | 否 | 城市编码 |
| area_code | string | 否 | 区域编码 |
| enterprise_type | string | 否 | 企业类型 |
| grade_section_code | string | 否 | 学段编码 |
| subject_id | number | 否 | 学科ID |
| is_active | boolean | 否 | 是否激活 |

**响应示例**:
```json
{
  "errCode": 0,
  "msg": "OK",
  "data": {
    "total": 23,
    "by_grade_section": [
      {"grade_section_name": null, "count": 16},
      {"grade_section_name": "小学", "count": 5},
      {"grade_section_name": "高中", "count": 2}
    ],
    "by_subject": [
      {"subject_name": null, "count": 16},
      {"subject_name": "小学语文", "count": 1},
      {"subject_name": "数学", "count": 1}
    ],
    "by_status": [
      {"isActive": true, "count": 23}
    ],
    "timestamp": "2025-05-27T07:29:07.167Z"
  }
}
```

## 测试示例

```bash
# 获取系统概览
curl "http://localhost:3130/dashboard/overview"

# 获取企业总数
curl "http://localhost:3130/dashboard/enterprises/count"

# 获取用户总数
curl "http://localhost:3130/dashboard/users/count"

# 获取企业统计数据
curl "http://localhost:3130/dashboard/enterprises/statistics"

# 获取用户统计数据
curl "http://localhost:3130/dashboard/users/statistics"

# 带查询参数的请求
curl "http://localhost:3130/dashboard/overview?province_code=610000&enterprise_type=SCHOOL"
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 其他 | 具体错误信息见 msg 字段 |
