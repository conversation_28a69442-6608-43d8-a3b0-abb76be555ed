import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ComposerTestPaperService } from '../service/composer_test_paper.service';
import { CustomError } from '../error/custom.error';
import { split } from 'lodash';
import { ComposerTestPaperDetail } from '../entity';

@Controller('/composer_test_paper')
export class ComposerTestPaperController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ComposerTestPaperService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;
    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      include: [
        {
          model: ComposerTestPaperDetail,
          attributes: ['questionBankId', 'sourceTable', 'score'],
        },
      ],
    });
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: number) {
    if (!id) {
      throw new CustomError('未指定试卷id!');
    }
    const res = await this.service.findTestPaperWithDetail(id);
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: number, @Body() body: any) {
    if (!body.composerPaperId) {
      throw new CustomError('请选择组卷方案');
    }
    return await this.service.detailUpdate(id, body);
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }

  @Post('/count', { summary: '获取可生成试卷的数量' })
  async autoCreateTestPaperCount(@Body() body: any) {
    const { data, ...queryObj } = body;
    const count = await this.service.autoCount(data, queryObj);
    return count;
  }

  @Put('/detail_update/:id')
  async detailUpdate(@Body() info: any, @Param('id') id: number) {
    if (!info.composerPaperId) {
      throw new CustomError('请选择组卷方案');
    }
    return await this.service.detailUpdate(id, info);
  }

  @Del('/batch/:ids', { summary: '批量删除试卷' })
  async deleteByIds(@Param('ids') ids: string) {
    try {
      await this.service.bulkDestroy(split(ids, ','));
      return true;
    } catch (error) {
      throw new CustomError(error.message);
    }
  }

  @Del('/delete/:composerPaperId', { summary: '删除组卷方案下的试卷' })
  async deleteByComposerPaperId(
    @Param('composerPaperId') composerPaperId: number
  ) {
    await this.service.destroyByComposerPaperId(composerPaperId);
    return true;
  }

  @Post('/autoCreate', { summary: '自动组卷' })
  async autoComposerPaper(
    @Body()
    params: any
  ) {
    const startTime = Date.now();
    const info = { ...params, startTime };
    const res = await this.service.executeAutoComposerPaper(info);
    return res;
  }
}
