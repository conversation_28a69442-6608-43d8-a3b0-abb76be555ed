import { Table, Column, Model, DataType } from 'sequelize-typescript';

export interface DictionaryAttributes {
  /** 字典编码 */
  code: string;
  /** 字典类型 */
  type: string;
  /** 字典名称 */
  name: string;
  /** 别名 */
  alias?: string;
  /** 字典描述（可选） */
  description?: string;
  /** 排序 */
  sortOrder: number;
  /** 状态 */
  status: number;
}

@Table({ tableName: 'dictionaries', timestamps: true, comment: '字典表' })
export class Dictionary
  extends Model<DictionaryAttributes>
  implements DictionaryAttributes
{
  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    primaryKey: true,
    comment: '字典编码',
  })
  code: string;
  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '字典类型',
  })
  type: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '字典名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '字典别名',
  })
  alias: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '字典描述',
  })
  description: string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '排序',
  })
  sortOrder: number;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 1,
    comment: '状态',
  })
  status: number;
}
