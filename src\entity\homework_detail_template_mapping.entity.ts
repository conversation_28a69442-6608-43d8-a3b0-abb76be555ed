import { Column, DataType, Model, Table } from 'sequelize-typescript';

export interface HomeworkDetailTemplateMappingAttributes {
  id: number;
  templateDetailId: number;
  tempDetailQuestionId: string;
  schoolDetailId: number;
  schoolDetailQuestionId: string;
  type: string;
  createdAt: Date;
  updatedAt: Date;
}

@Table({
  tableName: 'homework_detail_template_mapping',
  timestamps: true,
  comment: '校本作业详情与范本作业详情映射表',
})
export class HomeworkDetailTemplateMapping
  extends Model<HomeworkDetailTemplateMappingAttributes>
  implements HomeworkDetailTemplateMappingAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'id',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '范本作业详情id',
  })
  templateDetailId: number;

  @Column({
    type: DataType.STRING(36),
    allowNull: true,
    comment: '范本详情题目id',
  })
  tempDetailQuestionId: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '校本作业详情id',
  })
  schoolDetailId: number;

  @Column({
    type: DataType.STRING(36),
    allowNull: true,
    comment: '校本作业详情题目id',
  })
  schoolDetailQuestionId: string;

  @Column({
    type: DataType.ENUM('小节', '大题', '小题'),
    allowNull: false,
    comment: '类型',
  })
  type: string;

  @Column({
    type: DataType.DATE,
    comment: '创建时间',
  })
  createdAt: Date;
  @Column({
    type: DataType.DATE,
    comment: '更新时间',
  })
  updatedAt: Date;
}
