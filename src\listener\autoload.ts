/*
 * @Description: 数据初始化
 * @Date: 2025-01-06 14:12:26
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-02-08 15:34:58
 */
import { App, Autoload, Init, Inject, Scope, ScopeEnum } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import { SystemService } from '../service/system.service';

@Autoload()
@Scope(ScopeEnum.Singleton)
export class AutoloadListener {
  @App('koa')
  app: koa.Application;

  @Inject()
  sysService: SystemService;

  @Init()
  async init() {
    // 只在主进程初始化
    const env = this.app.getEnv();
    console.log('env: ', env);
    if (
      typeof process.env.NODE_APP_INSTANCE === 'undefined' ||
      process.env.NODE_APP_INSTANCE === '0'
    ) {
      // 检查系统初始化状态
      await this.sysService.checkAndInitSystem();
    }
  }
}
