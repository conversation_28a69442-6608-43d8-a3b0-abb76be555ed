import { Inject, Logger, Provide } from '@midwayjs/core';
import { SchoolQuestions, SystemQuestions } from '../model';
import { Context } from '@midwayjs/koa';
import { InjectEntityModel } from '@midwayjs/typegoose';
import { Util } from '../common/Util';
import { orderBy, isEmpty, pick, assign } from 'lodash';
import { CustomError } from '../error/custom.error';
import {
  ExcludeSimilarQuestion,
  Semester,
  SimilarQuestion,
  HomeworkDetailTemplateMapping,
  TextbookConfig,
  QuestionRelation,
  ClassRelation,
} from '../entity';
import { Op } from 'sequelize';
import { Types } from 'mongoose';
import { ReturnModelType } from '@typegoose/typegoose';
import { MONGO_MODEL_KEY } from '../common/Constants';
import { QuestionService } from './question.service';
@Provide()
export class SystemQuestionService {
  @Inject()
  ctx: Context;

  @Logger()
  logger;

  @Inject()
  questionService: QuestionService;
  @InjectEntityModel(SystemQuestions)
  systemQuestions: ReturnModelType<typeof SystemQuestions>;

  @InjectEntityModel(SchoolQuestions)
  schoolQuestions: ReturnModelType<typeof SchoolQuestions>;
  /**
   * 查询列表
   * @param queryObj 请求参数
   * @param current 分页参数
   * @param pageSize 分页参数
   * @returns 数据对象
   */
  async index(queryObj, current, pageSize, order = { createdAt: -1 }) {
    return this.questionService.findQuestionsByFilter(
      queryObj,
      current,
      pageSize,
      order,
      MONGO_MODEL_KEY.SYSTEM
    );
  }

  // 获取相似题数量
  async similarQuestionCount(queryObj) {
    const { points, baseType, type, ...query } = queryObj;
    if (points && points !== '') {
      query['points.value'] = { $all: points.split('@') };
    }
    if (baseType) {
      query['baseType.name'] = baseType;
    }
    if (type && type !== '') {
      query['type.name'] = type;
    }

    const count = await this.systemQuestions.count(query);
    return count;
  }

  // 获取相似题详情
  async similarQuestionDetail(queryObj) {
    const { points, ...query } = queryObj;
    console.log('获取相似题===========================Start');

    query['points.value'] = { $all: points.split('@') };
    const list = await this.systemQuestions.aggregate([
      { $match: query },
      { $sample: { size: 3 } },
    ]);
    if (list.length < 3 && points && points !== '') {
      console.log(
        '获取相似题========完全匹配没有找到3个=================',
        list.length
      );
      console.log('获取相似题========知识点模糊匹配开始=================');
      const asdarr = points.split('@');
      delete query['points.value'];
      let childrenZsd = [];
      if (asdarr.length > 1) {
        childrenZsd = Util.getSplitZsd(asdarr).sort(
          (a, b) => b.length - a.length
        );
      }
      let zsdquery;
      if (childrenZsd.length) {
        zsdquery = {
          $or: childrenZsd.map(item => ({
            'points.value': { $all: item },
          })),
        };
      }
      const res = await this.getChildrenzsds(query.xk_value, asdarr);

      if (res.length) {
        zsdquery.$or.push({ 'points.value': { $in: res } });
      }
      // 移除——id
      if (list.length) {
        query._id = { $nin: list.map(item => item._id) };
      }
      const newlist = await this.systemQuestions.aggregate([
        { $match: { ...query, ...zsdquery } },
        { $sample: { size: 3 - list.length } },
      ]);
      if (newlist.length) {
        list.push(...newlist);
      }

      console.log(
        '获取相似题========知识点模糊匹配结束=================',
        newlist.length
      );
    }
    if (list.length < 3) {
      console.log(
        '获取相似题========题型完全匹配不筛选知识点=================start'
      );
      delete query['points.value'];
      // 移除——id
      if (list.length) {
        query._id = { $nin: list.map(item => item._id) };
      }
      const newlist = await this.systemQuestions.aggregate([
        { $match: query },
        { $sample: { size: 3 - list.length } },
      ]);
      if (newlist.length) {
        list.push(...newlist);
      }
      console.log(
        '获取相似题========题型完全匹配不筛选知识点=================end',
        newlist.length
      );
    }

    if (list.length < 3 && points && points !== '') {
      // const { tmlx_value, ...q } = queryObj;
      // const { xk_value } = q;
      //TODO:
      const xk_list = [];
      // config.xbzystlx.forEach(item => {
      //   if (item.code === xk_value) {
      //     item.arr.forEach(v => {
      //       if (v.some(c => c.code === tmlx_value)) {
      //         xk_list = v.map(c => c.code);
      //       }
      //     });
      //   }
      // });
      if (xk_list.length) {
        query.tmlx_value = { $in: xk_list };
      }
      console.log(
        '获取相似题========题目类型扩大范围知识点完全匹配开始================='
      );
      query['points.value'] = { $all: points.split('@') };
      // 移除——id
      if (list.length) {
        query._id = { $nin: list.map(item => item._id) };
      }
      const newlist = await this.systemQuestions.aggregate([
        { $match: query },
        { $sample: { size: 3 - list.length } },
      ]);
      if (newlist.length) {
        list.push(...newlist);
      }
      console.log(
        '获取相似题========题目类型扩大范围知识点完全匹配结束=================',
        list.length
      );
      if (list.length < 3 && points && points !== '') {
        console.log(
          '获取相似题========题目类型扩大范围知识点模糊匹配开始================='
        );
        const asdarr = points.split('@');
        delete query['points.value'];
        let childrenZsd = [];
        if (asdarr.length > 1) {
          childrenZsd = Util.getSplitZsd(asdarr).sort(
            (a, b) => b.length - a.length
          );
        }
        let zsdquery;
        if (childrenZsd.length) {
          zsdquery = {
            $or: childrenZsd.map(item => ({
              'points.value': { $all: item },
            })),
          };
        }
        const res = await this.getChildrenzsds(query.xk_value, asdarr);
        if (res.length) {
          zsdquery.$or.push({ 'points.value': { $in: res } });
        }
        // 移除——id
        if (list.length) {
          query._id = { $nin: list.map(item => item._id) };
        }
        const newlist = await this.systemQuestions.aggregate([
          { $match: { ...query, ...zsdquery } },
          { $sample: { size: 3 - list.length } },
        ]);
        if (newlist.length) {
          list.push(...newlist);
        }
        console.log(
          '获取相似题========题目类型扩大范围知识点模糊匹配结束=================',
          newlist.length
        );
      }
      if (list.length < 3 && points && points !== '') {
        console.log(
          '获取相似题========题目类型扩大范围不筛选知识点开始================='
        );
        if (xk_list.length) {
          query.tmlx_value = { $in: xk_list };
        }
        // 移除——id
        if (list.length) {
          query._id = { $nin: list.map(item => item._id) };
        }
        const newlist = await this.systemQuestions.aggregate([
          { $match: query },
          { $sample: { size: 3 - list.length } },
        ]);
        if (newlist.length) {
          list.push(...newlist);
        }
        console.log(
          '获取相似题========题目类型扩大范围不筛选知识点结束=================',
          list.length
        );
      }
    }

    return list;
  }
  //
  async getChildrenzsds(xk_value, zsds) {
    //TODO:
    // const zsdTree = [];
    // const zsdTree = await service.category.getByZsd('xbzy_zsd', xk_value);
    const arrSet = new Set();
    for (const zsd of zsds) {
      //TODO:
      // service.category.getZsdInfo(zsdTree, zsd, arrSet);
      console.log(zsd);
    }
    const arr = Array.from(arrSet);
    return arr;
  }

  /**
   * 根据id获取数据
   * @param {string} id 试题id
   */
  async show(id) {
    const mgQuestion = await this.systemQuestions.findById(id).exec();
    if (!mgQuestion) {
      throw new CustomError('未找到数据！');
    }
    // 如果是父子题 查找子题
    if (mgQuestion.isCompose) {
      const children = await this.systemQuestions
        .find({
          pid: mgQuestion._id,
        })
        .lean()
        .exec();
      return assign(mgQuestion, { children });
    }
    return mgQuestion;
  }

  /**
   * 创建试题
   * @param info 数据
   * @returns mgQuestion
   */
  async create(info) {
    const mgQuestion = await this.systemQuestions.create(info);
    return mgQuestion;
  }

  /**
   * 更新试题
   * @param {string} _id 试题id
   * @param {object} info 更新数据
   */
  async update(_id: string, info) {
    await this.show(_id);
    const res = await this.systemQuestions.updateOne(
      { _id },
      { ...info, updatedAt: new Date() }
    );
    return res;
  }

  /**
   * 删除试题
   * @param {string} _id 试题id
   */
  async destroy(_id: string) {
    await this.systemQuestions.deleteOne({ _id });
  }
  /**
   * 批量删除
   * @param {array[string]} ids 需要删除的数组
   */
  async destroyBulk(ids: string[]) {
    await this.systemQuestions.deleteMany({ _id: ids });
  }

  async findByCode(code) {
    const mgQuestion = await this.systemQuestions.findOne({ code }).exec();
    if (!mgQuestion) {
      throw new CustomError('未找到数据！');
    }
    return mgQuestion;
  }

  /**
   * 批量修改
   * @param ids 需要修改的试题id
   * @param data 修改的数据对象
   */
  async updateBulk(ids: string[], data) {
    const res = await this.systemQuestions
      .updateMany({ _id: { $in: ids } }, { $set: data })
      .exec();
    return res;
  }

  /**
   * 根据试题id数组获取多个试题
   * @param {array[string]} ids 试题id数组
   */
  async findAllByIds(ids) {
    const res = await this.systemQuestions.find({ _id: { $in: ids } });
    return res;
  }

  /**
   * 获取tmlx值
   *
   * @param query 题目筛选条件 必须包含课时条件
   * @return 返回包含tmlx_value和tmlx_text的数组
   */
  async getTmlx(query) {
    const {
      schema: { obj },
    } = this.systemQuestions;
    const filter = { ks_value: query.ks_value };
    const keys = Object.keys(obj).filter(key => key !== 'ks_value');
    for (const key in query) {
      if (Object.hasOwnProperty.call(query, key) && keys.includes(key)) {
        filter[key] = query[key];
      }
    }
    const res = await this.systemQuestions.aggregate([
      {
        $match: filter,
      },
      {
        $group: {
          _id: { tmlx_value: '$tmlx_value', tmlx_text: '$tmlx_text' },
        },
      },
      {
        $project: {
          _id: 0,
          tmlx_value: '$_id.tmlx_value',
          tmlx_text: '$_id.tmlx_text',
        },
      },
    ]);
    return res;
  }

  async findAll(queryObj) {
    const { points, source, ...query } = queryObj;

    if (points && points !== '') {
      query['points.value'] = { $all: points.split('@') };
    }
    if (source && source !== '') {
      query.source = source;
    }
    const tableName = Util.getQuesTableName(queryObj.status, queryObj.type);
    const list = await this[tableName].aggregate([
      { $match: query }, // 匹配查询条件
      { $sort: { createdAt: 1 } }, // 按照createdAt字段升序排序
    ]);
    return list;
  }

  async uploadbulk(other, file) {
    const tempFile = file;
    console.log(tempFile.name);
    // 单独处理问卷解析
    // 增加题目的类型 导入 内置
    const {
      nj_text,
      nj_value,
      jcbb_text,
      jcbb_value,
      xk_text,
      xk_value,
      dy_value,
      dy_text,
      ks_value,
      ks_text,
      c_text,
      c_value,
      source,
      addType,
    } = other;
    //TODO:
    const data: any = {};
    // const data = await service.toolsXml.analysis(tempFile.filepath);

    // 知识点树
    // TODO:
    const zsdTree = [];
    // const zsdTree = await service.category.getByZsd('xbzy_zsd', xk_value);
    // 当前学科的所有题型
    //TODO:
    const txList = [];
    // const txList = await service.category.getCodeSubjectValue(
    //   'xbzy_sttx',
    //   xk_value
    // );
    const tmlxlist = [];
    for (const type of data?.types) {
      const type_name = type.title;
      const tmlxObj: any = {
        tmlx_text: type_name, // 试题类型
        tmlx_value: txList.find(tx => tx.name === type_name)?.code || '', // 试题类型
        list: [],
      };
      const infoArr = [];
      for (const tm of type.tmList) {
        const content = tm.content;

        const optionsNumber = tm.xxs;
        const da = tm.other.da.replace('【答案】', '');
        let { ym, nd, tmfc, f_tg = '', zsd_other = '' } = tm.other;
        const { pid } = tm.other;
        const zsds = Array.from(
          new Set(
            (tm.other.zsd || '')
              .replace(/<[^>]*>/g, '')
              .replace('【知识点】', '')
              .split('；')
              .map(zsd => Util.getZsdFromMap(zsd))
              .flat()
              .filter(zsd => !!zsd)
          )
        ).map(zsd => {
          const info = Util.getZsdInfo(zsdTree, zsd);
          if (info) {
            return {
              zsd_text: info.title,
              zsd_value: info.value,
            };
          }
          return {
            zsd_text: zsd,
            zsd_value: '',
          };
        });

        ym = ym.replace(/<[^>]*>/g, '').replace('【页码】', '');
        nd = nd.replace(/<[^>]*>/g, '').replace('【难度】', '');
        tmfc = tmfc.replace(/<[^>]*>/g, '').replace('【题目分层】', '');
        const jx = tm.other.jx.replace('【解析】', '');
        f_tg = f_tg.replace('【父题干】', '');
        zsd_other = zsd_other
          .replace('【第二知识点】', '')
          .replace(/<[^>]*>/g, '');

        let tmfc_value = null;
        switch (tmfc) {
          case '基础':
            tmfc_value = 0;
            break;
          case '提升':
            tmfc_value = 1;
            break;
          case '扩展':
            tmfc_value = 2;
            break;
          default:
            break;
        }
        let nd_value = null;
        switch (nd) {
          case '容易':
            nd_value = 0;
            break;
          case '中等':
            nd_value = 1;
            break;
          case '困难':
            nd_value = 2;
            break;
          default:
            break;
        }
        const info = {
          name: content, // 题干
          nj_text, // '年级text'
          nj_value, // 年级Value
          jcbb_text, // 教材版本
          jcbb_value, // 教材版本value
          xk_text, // 学科text
          xk_value, // 学科value
          dy_value, // 单元value
          dy_text, // 单元value
          ks_text, // 课时
          ks_value, // 课时
          nd_text: nd, // 试题难度
          nd_value, // 试题难度
          tmfc_text: tmfc, // 题目分层
          tmfc_value, // 题目分层
          ym, // 页码
          tmlx_text: type_name, // 试题类型
          tmlx_value: txList.find(tx => tx.name === type_name)?.code || '', // 试题类型
          practive: da, // 答案
          c_text, // 册
          c_value, // 册
          analysis: jx, // 解析
          zsds, // 知识点
          optionsNumber, // 选项数量
          source,
          f_tg,
          zsd_other,
          pid,
          createType: addType, // 小节、题目增加类型
        };
        infoArr.push(info);
      }
      const insertResult = await this.systemQuestions.insertMany(infoArr); // 导入时增加到临时题目表中
      tmlxObj.list = insertResult;
      tmlxlist.push(tmlxObj);
    }
    return tmlxlist;
  }

  /**
   * 批量获取小节下的题目
   * 1. 仅包含小题
   * 2. 包含小题和父子题
   * @param {*} queryObj 查询条件
   * @return {*} 题目集合
   */
  async getIds(queryObj) {
    const query: any = {};
    const { questionIds, status, parentIds, type } = queryObj;
    try {
      const tempQuestionIds = questionIds
        .filter(i => !i.includes('-'))
        .map(item => new Types.ObjectId(item));
      const tableName = Util.getQuesTableName(status, type);
      if (status === '草稿') query.deleted = false;
      const hasParent = parentIds && parentIds.length > 0;
      if (hasParent) {
        query.$or = [
          { _id: { $in: tempQuestionIds } },
          { pid: { $in: parentIds } },
        ];
      } else {
        query._id = { $in: tempQuestionIds };
      }
      const pipe = [
        {
          $match: query,
        },
        {
          $project: {
            analysis: 1, //解析
            parentStem: 1, //父题干
            period: 1, //课时
            name: 1, //名称
            difficulty: 1, //试题难度
            pid: 1,
            answer: 1, //答案
            source: 1, //来源
            baseType: 1, //基础题型
            type: 1, //扩展题型
            points: 1, //知识点
            tags: 1, //类题标签
            createdAt: 1, //创建时间
            options: 1, //选项
          },
        },
        { $sort: { createdAt: 1 } }, // 按照createdAt字段升序排序
      ];
      this.logger.info(
        `Start to execute getIds, current filter is: ${JSON.stringify(pipe)}`
      );
      // TODO：暂时没有临时题库，所以小节状态为草稿时查询会出错。
      const tempRes = await this[tableName].aggregate(pipe);
      const list = tempRes.map(item => JSON.parse(JSON.stringify(item)));
      if (list.length > 0) {
        if (hasParent) {
          //  过滤掉不属于本小节的题目
          const tempList = list.filter(item =>
            questionIds.includes(item._id.toString())
          );
          const finalRes = tempList.reduce((acc, curr) => {
            const parentId = curr.pid;
            if (parentId) {
              const existingGroup = acc.find(
                group => group.parentId === parentId
              );
              if (existingGroup) {
                existingGroup.children.push(curr);
              } else {
                acc.push({ parentId, children: [curr] });
              }
            } else {
              // 如果没有parentId，直接添加到结果中
              acc.push(curr);
            }
            return acc;
          }, []);
          return finalRes;
        }
        return list;
      }
      return list;
    } catch (error) {
      console.error(`Failed to execute getIds,message is: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取可替换的试题
   * @param {*} queryObj 查询条件
   * @param {*} query
   */
  async getFilterIds(queryObj, query) {
    const { ids, num, type, ...queryInfo } = queryObj;
    const { offset, limit } = query;
    const tempIds = ids.filter(i => !i.includes('-'));
    let questionIds = tempIds;
    if (type === 'school') {
      // 校本作业应该先查询出原始的所有题目id
      const originalIds = await HomeworkDetailTemplateMapping.findAll({
        where: { schoolDetailQuestionId: tempIds },
        attributes: ['tempDetailQuestionId'],
      });
      questionIds = JSON.parse(JSON.stringify(originalIds)).map(
        d => d.tempDetailQuestionId
      );
    }
    if (questionIds && questionIds.length) {
      queryInfo._id = {
        $nin: questionIds.map(v => {
          return new Types.ObjectId(v);
        }),
      };
    }
    if (num) {
      // 查找出父题下的子题
      const pipe = [
        { $match: queryInfo },
        {
          $project: {
            analysis: 1, //解析
            parentStem: 1, //父题干
            period: 1, //课时
            name: 1, //名称
            difficulty: 1, //试题难度
            pid: 1,
            answer: 1, //答案
            source: 1, //来源
            baseType: 1, //基础题型
            type: 1, //扩展题型
            points: 1, //知识点
            tags: 1, //类题标签
          },
        },
        {
          $group: {
            _id: '$pid', // 根据pid字段分组
            children: { $push: '$$ROOT' }, // 将每个分组中的数据存储在一个名为data的数组中
          },
        },
        { $sample: { size: num } },
      ];
      const allChildren = await this.systemQuestions
        .aggregate(pipe)
        .allowDiskUse(true);
      const finalRes = [];
      if (allChildren.length) {
        // 构造数据结构，返回指定num个题目
        const childrenData = allChildren.map(a =>
          JSON.parse(JSON.stringify(a))
        );
        const hasParentCount = childrenData.filter(item => item._id).length;
        childrenData.forEach(item => {
          const { _id, children } = item;
          if (_id) {
            finalRes.push(item);
          } else {
            const randomArr = children.sort(() => Math.random() - 0.5);
            const needCount = num - hasParentCount;
            const arr = randomArr.slice(0, needCount);
            finalRes.push(...arr);
          }
        });
      }
      return { list: finalRes };
    }
    const { total, list } = await this.index(queryInfo, offset, limit);
    return { total, list };
  }

  /**
   * 查询题目信息，传入条件限制返回字段
   * @param {*} filter 查询条件
   * @param {*} projection 返回字段条件
   * @param {string} [type] 小节类型：textbook | school
   * @param {string} [status] 小节状态：草稿,已发布
   * @return {any[]} 题目信息集合
   */
  async findByFilter(filter, projection, type = 'textbook', status = '草稿') {
    try {
      const tableName = Util.getQuesTableName(status, type);
      return this[tableName]
        .find(filter, projection)
        .then(results => JSON.parse(JSON.stringify(results)));
    } catch (error) {
      this.logger.error(
        `Failed to execute findByFilter, message is: ${error.message}`
      );
      throw error;
    }
  }

  /**
   * 批量删除题目，仅删除导入的题目，默认仅草稿状态下的小节可以进行删除
   * @param {string[]} ids 题目id数组
   * @param {string} [type] 小节类型：：textbook | school
   * @return {boolean} 删除成功与否
   */
  async destroyByIds(ids, type = 'textbook') {
    try {
      if (type === 'school') {
        return this.schoolQuestions.deleteMany({ _id: { $in: ids } }).exec();
      }
      // return Tempthis.systemQuestions.deleteMany({ _id: { $in: ids } }).then(() => {
      return this.systemQuestions
        .deleteMany({
          _id: { $in: ids },
          createType: 'import',
        })
        .exec();
      // });
    } catch (error) {
      console.error(
        `Failed to execute destroyByIds ,message is: ${error.message}`
      );
      throw error;
    }
  }

  /**
   * 构造父子题 子题的数据结构
   * @param parentInfo 父题数据
   * @param children 子题数据
   * @returns
   */
  async buildChildren(parentInfo, children: any[]) {
    const commonInfo = pick(parentInfo, [
      'difficulty',
      'grade',
      'textbookVersion',
      'gradeSection',
      'subject',
      'unit',
      'period',
      'author',
      'source',
      'volume',
      'tier',
      'year',
      'cognitiveHierarchy',
      'coreQuality',
      'investigationAbility',
      'tableName',
    ]);
    const childQuestions = children.map(item => {
      item.pid = parentInfo._id;
      item.parentStem = parentInfo.name;
      return assign(item, commonInfo);
    });
    return childQuestions;
  }

  /**
   * 创建父子题 父子题分别为独立的题
   * @param info 父子题数据
   * @returns
   */
  async createAssociateQuestion(info: any) {
    const { children, ...data } = info;
    const pid = new Types.ObjectId()._id;
    const parentQuestion = {
      _id: pid,
      ...data,
    };
    const childQuestions = await this.buildChildren(parentQuestion, children);
    return await this.systemQuestions.insertMany([
      parentQuestion,
      ...childQuestions,
    ]);
  }

  /**
   * 编辑父子题
   * @param {Array[]} data 子题数据
   * @param {string} pid 父题id
   * @return {any} 更新结果
   */
  async updateAssociateQuestion(info, pid: string) {
    const { children, ...data } = info;
    await this.systemQuestions
      .deleteMany({ pid: new Types.ObjectId(pid) })
      .exec();
    const childQuestions = await this.buildChildren(data, children);
    await this.systemQuestions.updateOne(
      { _id: new Types.ObjectId(pid) },
      data
    );
    return await this.systemQuestions.insertMany(childQuestions);
  }

  /**
   * 删除父子题
   * @param pid 父题id
   */
  async deleteQuestionByPid(pid: string) {
    const questionId = new Types.ObjectId(pid);
    await this.systemQuestions
      .deleteMany({ $or: [{ pid: questionId }, { _id: questionId }] })
      .exec();
  }

  // /**
  //  * 创建父子题本质上是批量创建小题  父题通过f_tag及pid关联
  //  * @param {Array[object]} data 子题数据
  //  * @return {object} 创建结果
  //  */
  // async createAssociateQuestion(data) {
  //   if (!data || !data.length) return;
  //   const pid = randomUUID();
  //   const insertArr = data.map(item => {
  //     item.pid = pid;
  //     return item;
  //   });
  //   return await this.systemQuestions.insertMany(insertArr);
  // }

  // /**
  //  * 编辑父子题
  //  * @param {Array[]} data 子题数据
  //  * @param {string} pid 父题id
  //  * @return {any} 更新结果
  //  */
  // async updateAssociateQuestion(data, pid) {
  //   if (!data || !data.length) return;
  //   const insertArr = data.map(item => {
  //     item.pid = pid;
  //     return item;
  //   });
  //   // 查询子题
  //   const ques = await this.systemQuestions.find({ pid }).exec();
  //   // 获得删除的题目
  //   const delIds = ques
  //     .map(q => {
  //       const id = q._id.toString();
  //       const index = findIndex(insertArr, ['_id', id]);
  //       return index === -1 ? id : null;
  //     })
  //     .filter(i => i);
  //   if (delIds.length) {
  //     // 同步相似题关联关系
  //     await this.deleteAssociate(delIds);
  //     await this.systemQuestions
  //       .deleteMany({
  //         _id: { $in: delIds.map(d => new Types.ObjectId(d)) },
  //       })
  //       .exec();
  //   }
  //   const bulk = insertArr.map(data => {
  //     if (!data._id) {
  //       return {
  //         insertOne: {
  //           document: data,
  //         },
  //       };
  //     }
  //     const id = new Types.ObjectId(data._id);
  //     return {
  //       updateOne: {
  //         filter: { _id: id },
  //         update: { $set: data },
  //         upsert: true,
  //       },
  //     };
  //   });
  //   return await this.systemQuestions.bulkWrite(bulk);
  // }

  // /**
  //  * 根据父题id删除关联的子题
  //  * @param {string} pid 父题id
  //  */
  // async deleteQuestionByPid(pid) {
  //   // 删除关联的相似题
  //   // 获取父题的所有小题id
  //   const questions = await this.systemQuestions.find({ pid }, { _id: 1 });
  //   await this.systemQuestions.deleteMany({ pid }).exec();
  //   const ids = questions.map(q => q._id.toString());
  //   if (ids.length) {
  //     // 需要删相似题关联关系
  //     await this.deleteAssociate(ids);
  //   }
  // }

  /**
   *  关联相似题查询题目列表
   * @param {object} queryObj 查询条件
   * @param {*} current 当前页
   * @param {*} pageSize 每页条数
   * @return {object[]} 返回结果
   */
  async getQuestionList(queryObj, current, pageSize) {
    const { points, source, filterIds, ...query } = queryObj;

    let skip;
    let limit;
    if (current && pageSize) {
      skip = Util.toInt(current);
      limit = Util.toInt(pageSize);
    }
    if (points && points !== '') {
      query['points.value'] = { $all: points.split(',') };
    }
    if (source && source !== '') {
      query.source = source;
    }
    if (filterIds) {
      const ids = filterIds.split(',').map(id => new Types.ObjectId(id));
      query._id = { $nin: ids };
    }
    const pipe: any[] = [{ $match: query }, { $sort: { createdAt: 1 } }];
    if (skip) {
      pipe.push({ $skip: skip });
    }
    if (limit) {
      pipe.push({ $limit: limit });
    }
    this.logger.info(
      `Search question list, query filter is ${JSON.stringify(pipe)}`
    );
    const allRes = await this.systemQuestions.aggregate(pipe).exec(); // 获取所有满足条件 分组后的数据
    const count = await this.systemQuestions.countDocuments(query);
    return { count, list: allRes };
  }

  /**
   *  获取默认相似题
   *  相似题的父子题按照小题格式返回给前端
   * @param {string} questionId 题目id
   * @return {object[]} 相似题列表
   */
  async getDefaultSimilarQuestion(questionId) {
    // 默认相似题需要过滤掉已经手工关联过的题
    const similarQuery = {
      where: {
        [Op.or]: [{ questionId }, { similarQuestionId: questionId }],
      },
      distinct: true,
    };
    const similarQuestions = await SimilarQuestion.findAll(similarQuery);
    const similarIds = [];
    if (similarQuestions.length) {
      similarQuestions.forEach(item => {
        if (item.questionId !== questionId) similarIds.push(item.questionId);
        if (item.similarQuestionId !== questionId)
          similarIds.push(item.similarQuestionId);
      });
    }
    const singleQues: any = await this.systemQuestions.findOne({
      _id: questionId,
    }); // 获取题目信息
    const { grade, subject, textbookVersion, volume, period, points, source } =
      singleQues;
    const query: any = {
      grade,
      subject,
      textbookVersion,
      volume,
      period,
      source: { $ne: source },
    };
    if (points && points.length) {
      const pointsArr = points.map(v => v.value);
      query['points.value'] =
        pointsArr.length === 1 ? pointsArr[0] : { $all: pointsArr };
    } else {
      return [];
    }
    if (similarIds.length) query._id = { $nin: similarIds };
    this.logger.info(
      `Start getDefaultSimilarQuestion query filter is ${JSON.stringify(query)}`
    );
    const defaultSimilarQues = await this.systemQuestions.find(query).exec();
    const tempSimilarQues = defaultSimilarQues.map(d => d.toJSON());
    const result = orderBy(tempSimilarQues, ['createdAt'], ['asc']); // 时间排序 保证翻页顺序
    this.logger.info(
      `End getDefaultSimilarQuestion, result count is ${result.length}`
    );
    return result;
  }

  /**
   *  获取默认相似题
   * @param {string[]} questionIds 题目id数组
   * @param {number} returnCount 返回总数
   * @return {object[]} 相似题列表
   */
  async getDefaultSimilarQuestions(questionIds, returnCount) {
    const ids = questionIds.filter(q => q && !q.includes('-'));
    const singleQuestions: any[] = await this.systemQuestions.find({
      _id: { $in: ids },
    }); // 获取题目信息
    if (!singleQuestions.length) return [];
    const gradeArr = []; //年级
    const subjectArr = []; //学科
    const versionArr = []; //教材版本
    const volumeArr = []; //册次
    const periodArr = []; //课时
    const baseTypeArr = []; //基础题目类型
    const difficultyArr = []; //难度
    const sourceArr = []; //来源
    const pointArr = [];
    singleQuestions.forEach(question => {
      const {
        grade,
        subject,
        textbookVersion,
        volume,
        period,
        baseType,
        points,
        source,
        difficulty,
      } = question;
      if (gradeArr.indexOf(grade) === -1) {
        gradeArr.push(grade);
      }
      if (subjectArr.indexOf(subject) === -1) {
        subjectArr.push(subject);
      }
      if (versionArr.indexOf(textbookVersion) === -1) {
        versionArr.push(textbookVersion);
      }
      if (volumeArr.indexOf(volume) === -1) {
        volumeArr.push(volume);
      }
      if (periodArr.indexOf(period) === -1) {
        periodArr.push(period);
      }
      if (baseTypeArr.indexOf(baseType.name) === -1) {
        baseTypeArr.push(baseType.name);
      }
      if (sourceArr.indexOf(source) === -1) {
        sourceArr.push(source);
      }
      if (difficultyArr.indexOf(difficulty) === -1) {
        difficultyArr.push(difficulty);
      }
      pointArr.push(...points);
    });
    const query = {
      grade: gradeArr.length === 1 ? gradeArr[0] : { $in: gradeArr },
      subject: subjectArr.length === 1 ? subjectArr[0] : { $in: subjectArr },
      textbookVersion:
        versionArr.length === 1 ? versionArr[0] : { $in: versionArr },
      volume: volumeArr.length === 1 ? volumeArr[0] : { $in: volumeArr },
      period: periodArr.length === 1 ? periodArr[0] : { $in: periodArr },
      source:
        sourceArr.length === 1 ? { $ne: sourceArr[0] } : { $nin: sourceArr },
    };
    if (pointArr && pointArr.length) {
      const pointsArr = pointArr.map(v => v.value);
      query['points.value'] =
        pointsArr.length === 1 ? pointsArr[0] : { $all: pointsArr };
    }
    this.logger.info(
      `Start getDefaultSimilarQuestions query filter is ${query}`
    );
    const pipe = [
      {
        $match: query,
      },
      {
        $sample: { size: returnCount },
      },
    ];
    const defaultSimilarQues = await this.systemQuestions
      .aggregate(pipe)
      .exec();
    const tempSimilarQues = defaultSimilarQues.map(d => d);
    const result = orderBy(tempSimilarQues, ['createdAt'], ['asc']); // 时间排序 保证翻页顺序
    this.logger.info(
      `End getDefaultSimilarQuestions, result count is ${result.length}`
    );
    return result;
  }

  /**
   * 根据推题规则获取默认相似题
   * 1.找到推题规则，根据推题规则进行查询，如果没找到对应推题规则，采用之前的推题策略
   * 2.推题规则中，如果配置了仅需按照题型进行匹配，则按照题型进行匹配
   * 3.反之根据题型+知识点 或者 题型+标签 或者 题型+知识点+标签进行匹配
   * @param {*} questionIds 题目id数组
   * @param {*} count 推送相似题数量
   * @param {*} enterpriseCode 企业code
   * @param {*} semester 学年学期
   */
  async getDefaultSimilarQuestionsByRule(
    questionIds,
    count,
    enterpriseCode,
    semester
  ) {
    // 过滤无效的题目id
    const ids = questionIds.filter(q => q && !q.includes('-'));
    // 获取题目信息
    const singleQuestions: any[] = await this.systemQuestions.find({
      _id: { $in: ids },
    });
    if (!singleQuestions.length) return [];

    // 根据学年学期信息，获取对应的学年学期信息
    const { xmmc, xqbm } = semester;
    const semesterQuery: any = {};
    if (xmmc) {
      semesterQuery.year = xmmc;
    }
    if (xqbm) {
      semesterQuery.term = xqbm === '01' ? '1' : '2';
    }
    const semesterInfo = await Semester.findOne({
      where: semesterQuery,
    });

    // 定义一个数组，用于存放多个题目id找到的相似题
    const allResults = [];
    for (const question of singleQuestions) {
      try {
        // 从当前题目中取出必要的查询参数
        const {
          _id,
          grade,
          subject,
          textbookVersion,
          volume,
          unit,
          period,
          baseType,
          points,
          source,
          tags,
        } = question;
        // 1.组装查询参数,去查询对应的推题规则
        const configQuery: any = {}; // 用于存放查询校本作业配置表的查询参数
        // 学校code
        if (enterpriseCode) {
          configQuery.enterpriseCode = enterpriseCode;
        }
        // 学年学期
        if (semesterInfo) {
          configQuery.semesterCode = semesterInfo.code;
        }
        // 年级
        if (grade) {
          configQuery.gradeName = grade;
        }
        // 学科
        if (subject) {
          configQuery.subject = subject;
        }
        // 教材版本
        if (textbookVersion) {
          configQuery.textbook_version = textbookVersion;
        }
        // 册次
        if (volume) {
          configQuery.volume = volume;
        }
        // 校本作业名称(作业来源)
        if (source) {
          configQuery.source = source;
        }

        // 2.组装查询参数查询对应题型关系
        const typeQuery: any = {};
        // 题目类型
        if (baseType) {
          typeQuery.question_type = baseType.name;
        }

        // 3.组装查询参数查询对应课时关系
        const classQuery: any = {};
        // 单元
        if (unit) {
          classQuery.unit = unit;
        }
        // 课时 (单独处理英语学科以单元为单位匹配相似题的规则，避免添加课时参数，找不到对应推题规则)
        if (period && subject !== '英语') {
          classQuery.period = period;
        }

        // 查询对应推题规则
        const textbookConfig = await TextbookConfig.findOne({
          where: configQuery,
          include: [
            { model: QuestionRelation, where: typeQuery },
            { model: ClassRelation, where: classQuery },
          ],
        });

        // 定义公共查询相似题参数
        const query: any = {
          grade,
          subject,
          textbookVersion,
          volume,
          _id: { $ne: _id }, // 排除当前题目
        };
        // 如果找到对应推题规则,则根据推题规则推送相似题, 没有找到则根据之前的推题策略
        if (textbookConfig) {
          const { maxSimilar, questionRelations, classRelations } =
            textbookConfig;
          // 从课时关系中获取数据，组装查询参数
          let classHourquery;
          if (classRelations && classRelations.length > 0) {
            classHourquery = {
              $or: classRelations.map(item => {
                return {
                  source: item.similarSource,
                  unit: item.match_unit,
                  period: item.match_period,
                };
              }),
            };
          }
          // 从题型关系中获取数据，组装查询参数
          if (questionRelations && questionRelations.length > 0) {
            const { is_match, match_type } = questionRelations[0];
            // 添加可匹配题型查询参数
            if (!isEmpty(match_type)) {
              query.baseType = { $in: match_type.split(',') };
            }
            // 只需要按照题型进行匹配相似题,反之就要根据题型+知识点+标签等进行组合查询进行匹配
            if (is_match) {
              const pipe: any[] = [{ $match: { ...query, ...classHourquery } }];
              // 优先根据用户传递查询相似题数量查询，如果没有则根据最大相似题数量随机查询相似题
              if (count) {
                pipe.push({ $sample: { size: count } });
              } else if (maxSimilar) {
                pipe.push({ $sample: { size: maxSimilar } });
              }
              this.logger.info(
                `仅需按照题型匹配相似题查询参数为：${JSON.stringify(pipe)}`
              );
              const similarQuesByType = await this.systemQuestions
                .aggregate(pipe)
                .exec();
              this.logger.info(
                `仅需按照题型匹配相似题数量为：${similarQuesByType.length}`
              );
              allResults.push(...similarQuesByType);
              // 仅需按照题型匹配相似题后，直接返回，进行下一道题的相似题匹配
              continue;
            }

            // 1.如果添加了知识点，无标签，则需根据 题型+知识点 进行匹配
            if (!isEmpty(points) && isEmpty(tags)) {
              // 将知识点value提取到一个数组中
              const pointsArr = points.map(v => v.value);
              // 单个知识点时，绝对匹配，多个并列查询
              query['points.value'] =
                pointsArr.length === 1 ? pointsArr[0] : { $all: pointsArr };
              // 查询类题标签为空或不存在的数据
              const tagsQuery = {
                $or: [{ tags: { $exists: false } }, { tags: { $size: 0 } }],
              };
              query.$and = [tagsQuery, classHourquery];

              const pipe: any[] = [{ $match: query }];
              // 优先根据用户传递查询相似题数量查询，如果没有则根据最大相似题数量随机查询相似题
              if (count) {
                pipe.push({ $sample: { size: count } });
              } else if (maxSimilar) {
                pipe.push({ $sample: { size: maxSimilar } });
              }
              this.logger.info(
                `题型+知识点并列查询参数为：${JSON.stringify(pipe)}`
              );
              const newlist = await this.systemQuestions.aggregate(pipe).exec();
              this.logger.info(
                `题型+知识点并列查询结果数量为：${newlist.length}`
              );
              if (newlist.length > 0) {
                allResults.push(...newlist);
                continue;
              }

              // 如果并列查询没有结果，根据知识点递减进行包含查询
              for (let i = 1; i < pointsArr.length; i++) {
                const subpointsArr = pointsArr.slice(0, pointsArr.length - i);
                const tempQuery = {
                  ...query,
                  'points.value': { $in: subpointsArr },
                };
                const pipe: any[] = [
                  // { $match: { ...tempQuery, ...classHourquery } },
                  { $match: tempQuery },
                ];
                // 优先根据用户传递查询相似题数量查询，如果没有则根据最大相似题数量随机查询相似题
                if (count) {
                  pipe.push({ $sample: { size: count } });
                } else if (maxSimilar) {
                  pipe.push({ $sample: { size: maxSimilar } });
                }
                this.logger.info(
                  `题型+知识点递减查询参数为：${JSON.stringify(pipe)}`
                );
                const newlist = await this.systemQuestions
                  .aggregate(pipe)
                  .exec();
                this.logger.info(
                  `题型+知识点递减查询结果数量为：${newlist.length}`
                );
                if (newlist.length > 0) {
                  allResults.push(...newlist);
                  break;
                }
              }
            }

            // 2.如果添加了标签，无知识点时，则根据 题型+标签进行匹配
            if (!isEmpty(tags) && isEmpty(points)) {
              const tagsArr = tags.map(v => v.value);
              query['tags.value'] =
                tagsArr.length === 1 ? tagsArr[0] : { $all: tagsArr };
              // 使用$size确保标签数组长度一致
              query.tags = { $size: tagsArr.length };
              // 查询知识点为空的数据
              query.points = { $size: 0 };
              const pipe: any[] = [{ $match: { ...query, ...classHourquery } }];
              // 优先根据用户传递查询相似题数量查询，如果没有则根据最大相似题数量随机查询相似题
              if (count) {
                pipe.push({ $sample: { size: count } });
              } else if (maxSimilar) {
                pipe.push({ $sample: { size: maxSimilar } });
              }
              this.logger.info(`题型+标签查询参数为：${JSON.stringify(pipe)}`);
              const newlist = await this.systemQuestions.aggregate(pipe).exec();
              this.logger.info(`题型+标签查询结果数量为：${newlist.length}`);
              allResults.push(...newlist);
              continue;
            }

            // 3.如果同时添加了知识点和标签，则 根据题型+知识点+标签 进行匹配
            if (!isEmpty(points) && !isEmpty(tags)) {
              // 标签绝对匹配
              const tagsArr = tags.map(v => v.value);
              query['tags.value'] =
                tagsArr.length === 1 ? tagsArr[0] : { $all: tagsArr };
              // 使用$size确保标签数组长度一致
              query.tags = { $size: tagsArr.length };

              // 知识点先并列查询，后逐个递减查询
              const pointsArr = points.map(v => v.value);
              query['points.value'] =
                pointsArr.length === 1 ? pointsArr[0] : { $all: pointsArr };
              const pipe: any[] = [{ $match: { ...query, ...classHourquery } }];
              // 优先根据用户传递查询相似题数量查询，如果没有则根据最大相似题数量随机查询相似题
              if (count) {
                pipe.push({ $sample: { size: count } });
              } else if (maxSimilar) {
                pipe.push({ $sample: { size: maxSimilar } });
              }
              this.logger.info(
                `题型+标签+知识点并列查询参数为：${JSON.stringify(pipe)}`
              );
              const newlist = await this.systemQuestions.aggregate(pipe).exec();
              this.logger.info(
                `题型+标签+知识点并列查询结果数量为：${newlist.length}`
              );
              if (newlist.length > 0) {
                allResults.push(...newlist);
                continue;
              }

              // 如果并列查询没有结果，根据知识点递减进行包含查询
              for (let i = 1; i < pointsArr.length; i++) {
                const subpointsArr = pointsArr.slice(0, pointsArr.length - i);
                const tempQuery = {
                  ...query,
                  'points.value': { $in: subpointsArr },
                };

                const pipe: any[] = [
                  { $match: { ...tempQuery, ...classHourquery } },
                ];
                // 优先根据用户传递查询相似题数量查询，如果没有则根据最大相似题数量随机查询相似题
                if (count) {
                  pipe.push({ $sample: { size: count } });
                } else if (maxSimilar) {
                  pipe.push({ $sample: { size: maxSimilar } });
                }

                this.logger.info(
                  `题型+标签＋知识点递减查询参数为：${JSON.stringify(pipe)}`
                );
                const newlist = await this.systemQuestions
                  .aggregate(pipe)
                  .exec();
                this.logger.info(
                  `题型+标签+知识点递减查询结果数量为：${newlist.length}`
                );
                if (newlist.length > 0) {
                  allResults.push(...newlist);
                  break;
                }
              }
            }
          }
        } else {
          // 使用之前的推题策略
          if (period) {
            query.period = period;
          }
          if (source) {
            query.source = { $ne: source };
          }
          // 如果知识点存在，则根据单个知识点绝对匹配，多个知识点并列查询，没有知识点不推送
          if (points && points.length) {
            const pointsArr = points.map(v => v.value);
            query['points.value'] =
              pointsArr.length === 1 ? pointsArr[0] : { $all: pointsArr };
          } else {
            return []; // 没有知识点不推送
          }
          this.logger.info(
            `之前的推题策略，查询参数为：${JSON.stringify(query)}`
          );
          const defaultSimilarQues = await this.systemQuestions
            .find(query)
            .exec();
          const tempSimilarQues = defaultSimilarQues.map(d => d.toJSON());
          const result = orderBy(tempSimilarQues, ['createdAt'], ['asc']); // 时间排序 保证翻页顺序
          this.logger.info(
            `之前的推题策略，查询相似题数量为：${result.length}`
          );
          allResults.push(...result);
        }
      } catch (error) {
        this.logger.error(
          `getDefaultSimilarQuestionsByRule error: ${question._id}, ${error}`
        );
        continue;
      }
    }
    return allResults;
  }

  /**
   * 查询题目相似题数量
   * @param {object[]} list 初始题目集合
   * @param {boolean} calculate 是否计算自动相似题数量
   * @return {object[]} newList 新结果数组
   */
  async calculateCount(list, calculate) {
    if (isEmpty(list)) return list;
    const newList = [];
    for (const item of list) {
      const newItem = item;
      if (item.children && item.children.length > 0) {
        // 查询父子题 子题的相似题
        const newChildren = [];
        for await (const child of item.children) {
          const obj = pick(child, [
            '_id',
            'grade',
            'subject',
            'textbookVersion',
            'volume',
            'period',
            'baseType',
            'source',
            'difficulty',
            'points',
          ]);
          child.similarCount = await this.getCount(obj, calculate);
          newChildren.push(child);
        }
        newItem.children = newChildren;
      } else {
        const obj = pick(item, [
          '_id',
          'grade',
          'subject',
          'textbookVersion',
          'volume',
          'period',
          'baseType',
          'source',
          'difficulty',
          'points',
        ]);
        newItem.similarCount = await this.getCount(obj, calculate);
      }
      newList.push({ ...newItem });
    }
    return newList;
  }

  /**
   * 统计条件题目数量
   * @param {*} item 源题目数据
   * @param {boolean} calculate 是否计算默认相似题数量
   * @return {number} 条数
   */
  async getCount(item, calculate) {
    let similarCount = 0;
    // 获取手动相似题数量
    const questionId = item._id.toString();
    const similarQuery = {
      where: {
        [Op.or]: [{ questionId }, { similarQuestionId: questionId }],
      },
      distinct: true,
    };
    const count = await SimilarQuestion.count(similarQuery);
    similarCount += count;
    this.logger.info(
      `Start count question list, query id is ${questionId}, count is ${count}`
    );
    if (calculate) {
      const unlinkSimilarQuery = {
        where: {
          [Op.or]: [{ questionId }, { excludeSimilarQuestionId: questionId }],
        },
        distinct: true,
      };
      const unlinkCount = await ExcludeSimilarQuestion.count(
        unlinkSimilarQuery
      );
      // 获取自动相似题数量
      const {
        grade,
        subject,
        textbookVersion,
        volume,
        period,
        source,
        points,
      } = item;
      const query = {
        grade,
        subject,
        textbookVersion,
        volume,
        period,
        source: { $ne: source },
      };
      if (points && points.length) {
        const arr = points.map(v => v.value);
        query['points.value'] = arr.length === 1 ? arr[0] : { $all: arr };
      }
      const defaultCount = points.length
        ? await this.systemQuestions.countDocuments(query)
        : 0;
      similarCount = similarCount + (defaultCount - unlinkCount); // 减去排除的自动相似题数量
      this.logger.info(
        `Start count default question list, query filter is ${JSON.stringify(
          query
        )}, default count is ${defaultCount}`
      );
    }
    return similarCount;
  }

  /**
   * 查询题目相似题数量  改造
   * @param {object[]} list 初始题目集合
   * @param {boolean} calculate 是否计算自动相似题数量
   * @param {string} username 当前用户账号，根据账号区分查询哪个学校相似题数量
   * @return {object[]} newList 新结果数组
   */
  async calculateCountChange(list, calculate, username) {
    if (isEmpty(list)) return list;
    const newList = [];
    for (const item of list) {
      const newItem = item;
      if (item.children && item.children.length > 0) {
        // 查询父子题 子题的相似题
        const newChildren = [];
        for await (const child of item.children) {
          child.similarCount = await this.getCountChange(
            child,
            calculate,
            username
          );
          newChildren.push(child);
        }
        newItem.children = newChildren;
      } else {
        newItem.similarCount = await this.getCountChange(
          item,
          calculate,
          username
        );
      }
      newList.push({ ...newItem });
    }
    return newList;
  }

  /**
   * 统计条件题目数量 改造
   * @param {*} item 源题目数据
   * @param {*} calculate 是否默认计算相似题数量
   * @param {*} username 当前用户账号，根据账号区分查询哪个学校相似题数量
   * @return {number} 条数
   */
  async getCountChange(item, calculate, username) {
    let similarCount = 0;
    // 获取手动相似题数量
    const questionId = item._id.toString();
    const similarQuery = {
      where: {
        [Op.or]: [{ questionId }, { similarQuestionId: questionId }],
      },
      distinct: true,
    };
    const count = await SimilarQuestion.count(similarQuery);
    similarCount += count;
    this.logger.info(
      `Start count question list, query id is ${questionId}, count is ${count}`
    );

    if (calculate) {
      // 如果手动相似题数量存在，则相似题数量是手动相似题的两倍，因为系统推送的相似题优先推送也是手动相似题
      if (similarCount > 0) {
        similarCount += similarCount;
        return similarCount;
      }
      // TODO：默认查询2024-2025学年第二学期
      const semesterInfo = await Semester.findOne({
        where: {
          year: '2024-2025',
          term: '2',
        },
      });
      // 获取自动相似题数量
      const {
        _id,
        grade,
        subject,
        textbookVersion,
        volume,
        unit,
        period,
        baseType,
        points,
        source,
        tags,
      } = item;

      // 组装查询参数，去查询湖居笔记下的推题规则
      const configQuery: any = {};
      // TODO：学校code默认为湖居笔记小学，根据账号区分查询哪个学校相似题数量
      switch (username) {
        // 湖居笔记小学
        case '15231hjbj':
          configQuery.enterpriseCode = '6101162112161004730';
          break;
        // 西安长安绿地城小学
        case '15231ldc':
          configQuery.enterpriseCode = '6101162112161004343';
          break;
        // 西安市莲湖区机场小学
        case '15231jcxx':
          configQuery.enterpriseCode = '6101042112161007913';
          break;
        // 西安长安悦美小学
        case '15231ym':
          configQuery.enterpriseCode = '6101162112161003110';
          break;
        // 浐灞第十七小学
        case '15231cb':
          configQuery.enterpriseCode = '6101122112161005760';
          break;
        default:
          configQuery.enterpriseCode = '6101162112161004730';
          break;
      }
      // 学年学期
      if (semesterInfo) {
        configQuery.semesterCode = semesterInfo.code;
      }
      // 年级
      if (grade) {
        configQuery.gradeName = grade;
      }
      // 学科
      if (subject) {
        configQuery.subject = subject;
      }
      // 教材版本
      if (textbookVersion) {
        configQuery.textbook_version = textbookVersion;
      }
      // 册次
      if (volume) {
        configQuery.volume = volume;
      }
      // 校本作业名称(作业来源)
      if (source) {
        configQuery.source = source;
      }
      // 2.组装查询参数查询对应题型关系
      const typeQuery: any = {};
      // 题目类型
      if (baseType) {
        typeQuery.question_type = baseType.name;
      }

      // 3.组装查询参数查询对应课时关系
      const classQuery: any = {};
      // 单元
      if (unit) {
        classQuery.unit = unit;
      }
      // 课时 (单独处理英语学科以单元为单位匹配相似题的规则，避免添加课时参数，找不到对应推题规则)
      if (period && subject !== '英语') {
        classQuery.period = period;
      }

      // 查询对应推题规则
      const textbookConfig = await TextbookConfig.findOne({
        where: configQuery,
        include: [
          { model: QuestionRelation, where: typeQuery },
          { model: ClassRelation, where: classQuery },
        ],
      });

      // 定义公共查询相似题参数
      const query: any = {
        grade,
        subject,
        textbookVersion,
        volume,
        _id: { $ne: _id }, // 排除当前题目
      };
      if (textbookConfig) {
        const { maxSimilar, questionRelations, classRelations } =
          textbookConfig;
        // 从课时关系中获取数据，组装查询参数
        let classHourquery;
        if (classRelations && classRelations.length > 0) {
          classHourquery = {
            $or: classRelations.map(item => {
              return {
                source: item.similarSource,
                unit: item.match_unit,
                period: item.match_period,
              };
            }),
          };
        }
        // 从题型关系中获取数据，组装查询参数
        if (questionRelations && questionRelations.length > 0) {
          const { is_match, match_type } = questionRelations[0];
          // 添加可匹配题型查询参数
          if (!isEmpty(match_type)) {
            query.baseType = { $in: match_type.split(',') };
          }
          // 仅需按照题型匹配,查询相似题数量
          if (is_match) {
            const pipe: any[] = [{ $match: { ...query, ...classHourquery } }];
            if (maxSimilar) {
              pipe.push({ $sample: { size: maxSimilar } });
            }
            const similarQuesByType = await this.systemQuestions
              .aggregate(pipe)
              .exec();
            similarCount = similarCount + similarQuesByType.length;
            similarCount = similarCount < 0 ? 0 : similarCount;
            return similarCount;
          } else if (!isEmpty(points) && isEmpty(tags)) {
            // 1.如果添加了知识点，无标签，根据 题型+知识点 获取可匹配相似题数量
            // 将知识点value提取到一个数组中
            const pointsArr = points.map(v => v.value);
            query['points.value'] =
              pointsArr.length === 1 ? pointsArr[0] : { $all: pointsArr };
            // 查询类题标签为空或不存在的数据
            const tagsQuery = {
              $or: [
                { zsd_other: { $exists: false } },
                { zsd_other: { $size: 0 } },
              ],
            };
            query.$and = [tagsQuery, classHourquery];

            const pipe: any[] = [{ $match: query }];

            if (maxSimilar) {
              pipe.push({ $sample: { size: maxSimilar } });
            }
            const newlist = await this.systemQuestions.aggregate(pipe).exec();
            if (newlist.length > 0) {
              similarCount = similarCount + newlist.length;
              similarCount = similarCount < 0 ? 0 : similarCount;
              return similarCount;
            }

            // 如果并列查询没有结果，根据知识点递减进行包含查询
            for (let i = 1; i < pointsArr.length; i++) {
              const subpointsArr = pointsArr.slice(0, pointsArr.length - i);
              const tempQuery = {
                ...query,
                'points.value': { $in: subpointsArr },
              };
              const pipe: any[] = [
                // { $match: { ...tempQuery, ...classHourquery } },
                { $match: tempQuery },
              ];
              // 优先根据用户传递查询相似题数量查询，如果没有则根据最大相似题数量随机查询相似题
              if (count) {
                pipe.push({ $sample: { size: count } });
              } else if (maxSimilar) {
                pipe.push({ $sample: { size: maxSimilar } });
              }
              const newlist = await this.systemQuestions.aggregate(pipe).exec();
              if (newlist.length > 0) {
                similarCount = similarCount + newlist.length;
                similarCount = similarCount < 0 ? 0 : similarCount;
                return similarCount;
              }
            }
          } else if (!isEmpty(tags) && isEmpty(points)) {
            // 2.如果添加了标签，无知识点时，根据 题型+标签进行匹配
            const tagsArr = tags.map(v => v.value);
            query['tags.value'] =
              tagsArr.length === 1 ? tagsArr[0] : { $all: tagsArr };
            // 使用$size确保标签数组长度一致
            query.tags = { $size: tagsArr.length };
            // 查询知识点为空的数据
            query.points = { $size: 0 };
            const pipe: any[] = [{ $match: { ...query, ...classHourquery } }];
            if (maxSimilar) {
              pipe.push({ $sample: { size: maxSimilar } });
            }
            const newlist = await this.systemQuestions.aggregate(pipe).exec();
            similarCount = similarCount + newlist.length;
            similarCount = similarCount < 0 ? 0 : similarCount;
            return similarCount;
          } else if (!isEmpty(points) && !isEmpty(tags)) {
            // 3.如果添加了知识点和标签，根据 题型+知识点+标签进行匹配
            // 标签绝对匹配
            const tagsArr = tags.map(v => v.value);
            query['tags.value'] =
              tagsArr.length === 1 ? tagsArr[0] : { $all: tagsArr };
            // 使用$size确保标签数组长度一致
            query.tags = { $size: tagsArr.length };

            // 知识点先并列查询，后逐个递减查询
            const pointsArr = points.map(v => v.value);
            query['points.value'] =
              pointsArr.length === 1 ? pointsArr[0] : { $all: pointsArr };
            const pipe: any[] = [{ $match: { ...query, ...classHourquery } }];
            if (maxSimilar) {
              pipe.push({ $sample: { size: maxSimilar } });
            }
            const newlist = await this.systemQuestions.aggregate(pipe).exec();
            if (newlist.length > 0) {
              similarCount = similarCount + newlist.length;
              similarCount = similarCount < 0 ? 0 : similarCount;
              return similarCount;
            }

            // 如果并列查询没有结果，根据知识点递减进行包含查询
            for (let i = 1; i < pointsArr.length; i++) {
              const subpointsArr = pointsArr.slice(0, pointsArr.length - i);
              const tempQuery = {
                ...query,
                'points.value': { $in: subpointsArr },
              };

              const pipe: any[] = [
                { $match: { ...tempQuery, ...classHourquery } },
              ];
              if (count) {
                pipe.push({ $sample: { size: count } });
              } else if (maxSimilar) {
                pipe.push({ $sample: { size: maxSimilar } });
              }
              const newlist = await this.systemQuestions.aggregate(pipe).exec();
              if (newlist.length > 0) {
                similarCount = similarCount + newlist.length;
                similarCount = similarCount < 0 ? 0 : similarCount;
                return similarCount;
              }
            }
          }
        }
      } else {
        if (period) {
          query.ks_value = period;
        }
        if (source) {
          query.source = { $ne: source };
        }
        if (points && points.length) {
          const pointsArr = points.map(v => v.zsd_value);
          query['points.value'] =
            pointsArr.length === 1 ? pointsArr[0] : { $all: pointsArr };
        }
        const defaultCount = points.length
          ? await this.systemQuestions.count(query)
          : 0;
        similarCount = similarCount + defaultCount; // 减去排除的自动相似题数量
        similarCount = similarCount < 0 ? 0 : similarCount;
        return similarCount;
      }
    }
  }

  /**
   * 删除题时需要同步删除关联关系
   * @param {string[]} delIds 删除的题目id数组
   */
  async deleteAssociate(delIds) {
    // 手动相似题的关联关系
    await SimilarQuestion.destroy({
      where: {
        [Op.or]: [{ questionId: delIds }, { similarQuestionId: delIds }],
      },
    });
    // 排除的相似题关联关系
    await ExcludeSimilarQuestion.destroy({
      where: {
        [Op.or]: [{ questionId: delIds }, { excludeSimilarQuestionId: delIds }],
      },
    });
  }

  /**
   * 开放获取题目信息
   * @param {string} id  题目id
   */
  async openGetQuestionInfo(id) {
    const mgQuestion = await this.systemQuestions.findById(id).exec();
    if (!mgQuestion) {
      throw new Error('题目不存在!');
    }
    return mgQuestion;
  }
}
