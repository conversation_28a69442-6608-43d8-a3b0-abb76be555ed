import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { InjectEntityModel } from '@midwayjs/typegoose';
import { ReturnModelType } from '@typegoose/typegoose';
import { TextbookTempDetailService } from '../service/textbook_temp_detail.service';
import { TextbookTempService } from '../service/textbook_temp.service';
import { TextbookTempDetail } from '../entity';
import { SystemQuestions } from '../model';

@Controller('/textbook_temp_detail')
export class TextbookTempDetailController {
  @Inject()
  ctx: Context;

  @Inject()
  service: TextbookTempDetailService;

  @Inject()
  textbookTempService: TextbookTempService;

  @InjectEntityModel(SystemQuestions)
  systemQuestions: ReturnModelType<typeof SystemQuestions>;

  @Get('/', { summary: '获取教材模版详情列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
    });
  }

  @Get('/:id', { summary: '获取单个教材模版详情' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定教材作业模版详情');
    }
    return res;
  }

  @Get('/detail/:id')
  async detailChildren(@Param('id') id: string) {
    const res = await this.service.getDetailChildren(id);
    if (!res) {
      throw new CustomError('未找到指定教材作业模版详情');
    }
    return res;
  }
  @Post('/', { summary: '创建教材模版详情' })
  async create(@Body() body: any) {
    const { addType, ...info } = body;
    if (!addType) {
      throw new CustomError('未找到创建类型');
    }
    if (!info.textbook_temp_id) {
      throw new CustomError('缺少教材模版id');
    }
    if (!info.textbook_catalog_id) {
      throw new CustomError('缺少教材目录id');
    }
    const transaction = await TextbookTempDetail.sequelize.transaction();
    try {
      const maxorderindex = await this.service.getMaxOrderIndex(
        info.textbook_temp_id,
        info.textbook_catalog_id,
        '小节'
      );

      const res = await this.service.create(
        { ...info, orderIndex: maxorderindex + 1 },
        transaction
      );

      if (addType === 'auto') {
        // 获取教材作业模版
        const textbookTemp = await this.textbookTempService.findById(
          info.textbook_temp_id
        );
        if (!textbookTemp) {
          throw new CustomError('测评作业不存在');
        }
        // 批量创建题目
        await this.service.createBulk(
          JSON.parse(JSON.stringify(textbookTemp)),
          JSON.parse(JSON.stringify(res)),
          transaction
        );
      } else {
        // TODO：文件导入逻辑处理
        const tempFile = body.files[0];
        if (!tempFile) {
          throw new CustomError('未找到文件');
        }

        const textbookTemp = await this.textbookTempService.findById(
          info.textbook_temp_id
        );
        if (!textbookTemp) {
          throw new CustomError('测评作业不存在');
        }
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw new CustomError('创建失败');
    }
  }

  @Put('/:id', { summary: '更新教材作业模版详情' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除教材作业模版详情' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }
  @Get('/:textbook_temp_id/:textbook_catalog_id')
  async getList(
    @Param('textbook_temp_id') textbook_temp_id: string,
    @Param('textbook_catalog_id') textbook_catalog_id: string,
    @Query() query: any
  ) {
    if (!textbook_temp_id) {
      throw new CustomError('缺少教材模版id');
    }

    if (!textbook_catalog_id) {
      throw new CustomError('缺少教材目录id');
    }

    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return await this.service.findAll({
      query: {
        ...queryInfo,
        type: '小节',
        textbook_temp_id,
        textbook_catalog_id,
      },
      offset,
      limit,
    });
  }

  @Post('/bulkCreate/:id', { summary: '批量创建教材作业模版详情' })
  async bulkCreate(@Param('id') id: string, @Body() body: any) {
    const { data, ...info } = body;

    const textbookTempDetail = await this.service.findById(id);
    if (!textbookTempDetail && textbookTempDetail.type !== '小节') {
      throw new CustomError('测评作业不存在');
    }

    const transaction = await TextbookTempDetail.sequelize.transaction();
    try {
      // 删除之前创建的题目
      const deleteList = await this.service.findAll({
        query: { parent_id: textbookTempDetail.id },
      });

      let i = 0;
      const questionBankIds = [];
      for (const item of data) {
        const { children, ...info } = item;
        delete info.id;
        if (info.question_bank_id) questionBankIds.push(info.question_banks_id);
        const bigQues = await this.service.create(
          {
            ...info,
            type: '大题',
            orderIndex: i++,
            textbook_catalog_id: textbookTempDetail.textbook_catalog_id,
            textbook_temp_id: textbookTempDetail.textbook_temp_id,
            parent_id: id,
          },
          transaction
        );

        const smallQues = children.map((v, index) => {
          delete v.id;
          if (v.question_bank_id) questionBankIds.push(v.question_bank_id);
          return {
            ...v,
            type: '小题',
            orderIndex: index,
            textbook_catalog_id: textbookTempDetail.textbook_catalog_id,
            textbook_temp_id: textbookTempDetail.textbook_temp_id,
            parent_id: bigQues.id,
          };
        });
        await this.service.batchCreate(smallQues, transaction);
      }

      // TODO:从题库中获取数据，写入临时表
      if (questionBankIds.length > 0) {
        const questions = await this.systemQuestions.find({
          _id: { $in: questionBankIds },
        });
        const bulk = questions.map(question => {
          const data = JSON.parse(JSON.stringify(question));
          const id = new Object(data._id);
          data.deleted = false;
          return {
            updateOne: {
              filter: { _id: id },
              update: { $set: data },
              upsert: true,
            },
          };
        });
        console.log('获取的试题', bulk);
        // 写入临时表
      }
      await textbookTempDetail.update({ ...info }, { transaction });
      // 删除之前创建的题目
      if (deleteList.list.length > 0) {
        await this.service.delete({
          where: { id: deleteList.list.map(v => v.id) },
          transaction,
        });
      }

      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError('批量创建失败');
    }
  }

  @Post('/publish', { summary: '发布/撤销小节' })
  async updatePublish(@Query() query: any, @Body() body: any) {
    const { id } = query;
    await this.service.updatePublish(body, id);
    return true;
  }
}
