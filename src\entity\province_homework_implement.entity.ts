import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';

export interface ProvinceHomeworkImplementAttributes {
  /** 主键ID */
  id: number;
  /** 学科id */
  subject_id?: number;
  /** 学科名称 */
  subject_name?: string;
  /** 学段 */
  grade_section_code?: string;
  /** 学段名称 */
  grade_section_name?: string;
  /** 课程实施要点 */
  implementation_points?: string;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({
  tableName: 'province_homework_implement',
  timestamps: true,
  comment: '课程实施要点表',
})
export class ProvinceHomeworkImplement
  extends Model<ProvinceHomeworkImplementAttributes>
  implements ProvinceHomeworkImplementAttributes
{
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '学科id',
  })
  subject_id?: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '学科名称',
  })
  subject_name?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学段',
  })
  grade_section_code?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学段名称',
  })
  grade_section_name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程实施要点',
  })
  implementation_points?: string;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    comment: '创建时间',
  })
  createdAt?: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    comment: '更新时间',
  })
  updatedAt?: Date;
}
