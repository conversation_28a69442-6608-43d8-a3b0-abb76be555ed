import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Textbook, Enterprise, TextbookTempDetail } from '.';

export interface TextbookTempAttributes {
  /** 教材作业模版id */
  id: number;
  /** 模版名称 */
  name?: string;
  /** 教材id */
  textbook_id?: number;
  /** 来源 */
  source?: string;
  /** 企业id */
  enterpriseId?: number;
  /** 原始文件url */
  originalFileURL?: string;
  /** 入库文件url */
  fileURL?: string;
  details?: TextbookTempDetail[];
}

@Table({
  tableName: 'textbook_temp',
  timestamps: true,
  comment: '教材作业模版表',
})
export class TextbookTemp
  extends Model<TextbookTempAttributes>
  implements TextbookTempAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '教材作业模版id',
  })
  id: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '模版名称',
  })
  name: string;

  @ForeignKey(() => Textbook)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '教材id',
  })
  textbook_id: number;

  @BelongsTo(() => Textbook, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  textbook: Textbook;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '来源',
  })
  source: string;

  @ForeignKey(() => Enterprise)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '企业id',
  })
  enterpriseId: number;

  @BelongsTo(() => Enterprise, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  enterprise: Enterprise;

  @Column({
    type: DataType.STRING(256),
    allowNull: true,
    comment: '原始文件url',
  })
  originalFileURL: string;

  @Column({
    type: DataType.STRING(256),
    allowNull: true,
    comment: '入库文件url',
  })
  fileURL: string;

  @HasMany(() => TextbookTempDetail, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  details?: TextbookTempDetail[];
}
