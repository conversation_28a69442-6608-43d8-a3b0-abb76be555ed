import {
  Table,
  Column,
  Model,
  DataType,
  // ForeignKey,
  // BelongsTo,
} from 'sequelize-typescript';
// import { PlanCourseSubject } from './plan_course_subject.entity'; // 假设存在一个 Course 实体类
// import { PlanCourseSystem } from './plan_course_system.entity'; // 假设存在一个 CourseCategory 实体类
// import { Enterprise } from './enterprise.entity'; // 假设存在一个 Enterprise 实体类

export interface StandardCourseContentAttributes {
  /** 主键ID */
  id: number;
  /** 名称 */
  name?: string;
  /** 描述 */
  describe?: string;
  /** 解析 */
  analy?: string;
  // /** 父级内容ID（树形结构） */
  // parent_id?: number;
  // /** 课程id */
  // course_id?: number;
  // /** 课程名称 */
  // course_name?: string;
  // /** 课程类别 */
  // course_category?: string;
  // /** 课程类别（下拉选择） */
  // course_category_id?: number;
  // /** 课程内容 */
  // course_content?: string;
  // /** 课程内容组织方式与呈现 */
  // content_organization?: string;
  // /** 学段 */
  // grade_section_code?: string;
  // /** 学段名称 */
  // grade_section_name?: string;
  // /** 学习任务群 */
  // task_group?: string;
  // /** 学校id */
  // enterprise_id?: number;
  // course?: PlanCourseSubject;
  // courseSystem?: PlanCourseSystem;
  // enterprise?: Enterprise;
}

@Table({
  tableName: 'standard_course_content',
  timestamps: true,
  comment: '标准课程内容表',
})
export class StandardCourseContent
  extends Model<StandardCourseContentAttributes>
  implements StandardCourseContentAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '名称',
  })
  name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '描述',
  })
  describe?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '解析',
  })
  analy?: string;

  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '父级内容ID（树形结构）',
  // })
  // parent_id?: number;

  // @ForeignKey(() => PlanCourseSubject)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '课程id',
  // })
  // course_id?: number;

  // @Column({
  //   type: DataType.STRING(64),
  //   allowNull: true,
  //   comment: '课程名称',
  // })
  // course_name?: string;

  // @Column({
  //   type: DataType.STRING(255),
  //   allowNull: true,
  //   comment: '课程类别',
  // })
  // course_category?: string;

  // @ForeignKey(() => PlanCourseSystem)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '课程类别（下拉选择）',
  // })
  // course_category_id?: number;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '课程内容',
  // })
  // course_content?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '课程内容组织方式与呈现',
  // })
  // content_organization?: string;

  // @Column({
  //   type: DataType.STRING(64),
  //   allowNull: true,
  //   comment: '学段',
  // })
  // grade_section_code?: string;

  // @Column({
  //   type: DataType.STRING(64),
  //   allowNull: true,
  //   comment: '学段名称',
  // })
  // grade_section_name?: string;

  // @Column({
  //   type: DataType.STRING(255),
  //   allowNull: true,
  //   comment: '学习任务群',
  // })
  // task_group?: string;

  // @ForeignKey(() => Enterprise)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '学校id',
  // })
  // enterprise_id?: number;

  // @BelongsTo(() => PlanCourseSubject, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // course?: PlanCourseSubject;

  // @BelongsTo(() => PlanCourseSystem, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // courseSystem?: PlanCourseSystem;

  // @BelongsTo(() => Enterprise, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // enterprise?: Enterprise;
}
