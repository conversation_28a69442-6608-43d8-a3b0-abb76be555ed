/*
 * @Description: 数据库异常过滤器
 * @Date: 2025-01-15 14:06:24
 * @LastEditors: 朱鹏亮 <EMAIL>
 * @LastEditTime: 2025-02-27 09:17:35
 */

import { Catch, Logger } from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';
import { Context } from '@midwayjs/koa';
import { DatabaseError, ValidationError } from 'sequelize';
import { ErrorCode } from '../common/ErrorCode';

@Catch([ValidationError, DatabaseError], {
  matchPrototype: true,
})
export class DatabaseFilter {
  @Logger()
  logger: ILogger;

  async catch(err: ValidationError, ctx: Context) {
    // 数据库异常会到这里
    console.log('SQL数据错误');
    ctx.logger.error(err);
    this.logger.error({
      source: 'debug',
      message: '数据库异常',
      details: JSON.stringify(err),
    });
    const firstErr = err.errors[0];
    switch (firstErr.type.toLowerCase()) {
      case 'unique violation':
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: firstErr.message.includes('must be unique')
            ? `${firstErr.value}已存在，操作失败！`
            : firstErr.message,
        };
      case 'notnull violation':
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: `${firstErr.path}不能为空，操作失败!`,
        };
      case 'foreignkey constraint':
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: `${firstErr.path}不存在，操作失败!`,
        };
      case 'data type mismatch':
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: `${firstErr.path}数据类型不匹配，操作失败!`,
        };
      case 'length violation':
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: `${firstErr.path}长度不匹配，操作失败!`,
        };
      case 'string violation':
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: `${firstErr.path}格式不匹配，操作失败!`,
        };
      default:
        console.log('数据库校验错误:', firstErr.type.toLowerCase());
        return {
          errCode: ErrorCode.BIZ_ERROR,
          msg: '数据库校验错误，操作失败!',
        };
    }
  }
}
