import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { QuestionRelationService } from '../service/question_relation.service';
import { CustomError } from '../error/custom.error';
import { QuestionRelation } from '../entity';

@Controller('/question_relation')
export class QuestionRelationController {
  @Inject()
  ctx: Context;

  @Inject()
  service: QuestionRelationService;

  @Get('/', { summary: '查询题型关系列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({ query: queryInfo, offset, limit });
  }

  @Get('/:id', { summary: '按ID查询单个题型关系' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑题型关系' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增题型关系' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Post('/bulkCreate/:textbook_config_id', { summary: '批量创建题型关系' })
  async bulkCreate(
    @Param('textbook_config_id') textbook_config_id: string,
    @Body() body: any
  ) {
    const transaction = await QuestionRelation.sequelize.transaction();
    try {
      // 删除当前校本作业配置下的题型关系
      await QuestionRelation.destroy({
        where: { textbook_config_id },
        transaction,
      });
      // 批量创建题型关系
      const res = await this.service.bulkCreate(body, transaction);
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError('创建失败');
    }
  }

  @Put('/:id', { summary: '更新题型关系' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除题型关系' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }
}
