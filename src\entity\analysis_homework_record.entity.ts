import { Table, Column, Model, DataType } from 'sequelize-typescript';

export interface AnalysisHomeworkRecord {
  /** id */
  id: number;
  /** 学期编码 */
  semester_code?: string;
  /** 学期名称 */
  semester_name?: string;
  /** 月份 */
  month?: string;
  /** 省份编码 */
  province_code?: string;
  /** 省份名称 */
  province_name?: string;
  /** 市编码 */
  city_code?: string;
  /** 市名称 */
  city_name?: string;
  /** 地区编码 */
  area_code?: string;
  /** 地区名称 */
  area_name?: string;
  /** 学制编码 */
  school_system_code?: string;
  /** 学制名称 */
  school_system_name?: string;
  /** 学段编码 */
  grade_section_code?: string;
  /** 学段名称 */
  grade_section_name?: string;
  /** 企业编码 */
  enterprise_code?: string;
  /** 企业名称 */
  enterprise_name?: string;
  /** 年级编码 */
  grade_code?: string;
  /** 年级名称 */
  grade_name?: string;
  /** 科目id */
  subject_id?: number;
  /** 科目名称 */
  subject?: string;
  /** 作业数量 */
  homework_number?: number;
  /** 平均时长 */
  average_time?: string;
  /** 建议时长 */
  suggested_time?: string;
  /** 试题数量 */
  question_number?: number;
}

@Table({
  tableName: 'analysis_homework_record',
  timestamps: true,
  comment: '作业分析记录表',
})
export class AnalysisHomeworkRecord
  extends Model<AnalysisHomeworkRecord>
  implements AnalysisHomeworkRecord
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'id，主键',
  })
  id: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '学期编码',
  })
  semester_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '学期名称',
  })
  semester_name?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '月份',
  })
  month?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '省份编码',
  })
  province_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '省份名称',
  })
  province_name?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '市编码',
  })
  city_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '市名称',
  })
  city_name?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '地区编码',
  })
  area_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '地区名称',
  })
  area_name?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学制code',
  })
  school_system_code?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学制名称',
  })
  school_system_name?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学段编码',
  })
  grade_section_code?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学段名称',
  })
  grade_section_name?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '企业编码',
  })
  enterprise_code?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '企业名称',
  })
  enterprise_name?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '年级编码',
  })
  grade_code?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '年级名称',
  })
  grade_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '科目id',
  })
  subject_id?: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '科目名称',
  })
  subject?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '作业数量',
  })
  homework_number?: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '平均时长',
  })
  average_time?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '建议时长',
  })
  suggested_time?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '试题数量',
  })
  question_number?: number;
}
