import { Inject, Provide } from '@midwayjs/core';
import { LessonWorkDesignQuestion, LessonWorkDesignStruct } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';

@Provide()
export class LessonWorkDesignStructService extends BaseService<LessonWorkDesignStruct> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课时作业设计结构');
  }

  getModel = () => {
    return LessonWorkDesignStruct;
  };

  /**
   * 获取最大排序索引
   * @param designDetail_id 课时作业设计id
   * @returns 最大排序索引
   */
  async getMaxIndex(designDetail_id) {
    const maxIndex = await LessonWorkDesignStruct.max('sort_order', {
      where: {
        designDetail_id,
      },
    });
    return (maxIndex as number) || 0;
  }

  /**
   * 批量创建
   * @param designDetail_id 课时作业设计详情id
   * @param body 课时作业设计结构
   */
  async bulkCreate(designDetail_id, body) {
    const transaction = await LessonWorkDesignStruct.sequelize.transaction();
    try {
      // 删除对应课时作业结构
      await LessonWorkDesignStruct.destroy({
        where: { designDetail_id },
        transaction,
      });
      // 批量创建课时作业结构
      const data = body.map((item, index) => {
        return {
          ...item,
          designDetail_id,
          sort_order: index + 1,
        };
      });
      const res = await LessonWorkDesignStruct.bulkCreate(data, {
        transaction,
      });
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 全部清空
   * @param designDetail_id 课时作业设计详情id
   */
  async bulkDelete(designDetail_id) {
    const transaction = await LessonWorkDesignStruct.sequelize.transaction();
    try {
      // 批量清除对应课时作业结构
      await LessonWorkDesignStruct.destroy({
        where: { designDetail_id },
        transaction,
      });
      // 批量清除对应课时作业下的题目
      await LessonWorkDesignQuestion.destroy({
        where: { designDetail_id },
        transaction,
      });
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }
}
