import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { LessonWorkDesignDetail } from '.';

export interface LessonWorkDesignStructAttributes {
  /** 主键 */
  id: string;
  /** 课时作业设计详情id */
  designDetail_id: number;
  /** 结构名称 */
  name: string;
  /** 题目ids */
  questionIds: string[];
  /** 排序 */
  sort_order: number;
  designDetail?: LessonWorkDesignDetail;
}

@Table({
  tableName: 'lesson_work_design_struct',
  timestamps: true,
  comment: '课时作业设计结构表',
})
export class LessonWorkDesignStruct
  extends Model<LessonWorkDesignStructAttributes>
  implements LessonWorkDesignStructAttributes
{
  @Column({
    type: DataType.STRING(64),
    primaryKey: true,
    comment: '主键',
  })
  id: string;

  @ForeignKey(() => LessonWorkDesignDetail)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '课时作业设计详情id',
  })
  designDetail_id: number;

  @BelongsTo(() => LessonWorkDesignDetail, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  designDetail?: LessonWorkDesignDetail;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '结构名称',
  })
  name: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    comment: '题目ids',
    get() {
      const rawValue = this.getDataValue('questionIds');
      return rawValue ? rawValue.split(',') : [];
    },
    set(value: string[]) {
      this.setDataValue('questionIds', value.join(','));
    },
  })
  questionIds: string[];

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '排序',
  })
  sort_order: number;
}
