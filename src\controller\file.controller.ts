import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  Inject,
  Param,
  Post,
  Config,
  createMiddleware,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import {
  UploadMiddleware,
  UploadFileInfo,
  UploadStreamFileInfo,
} from '@midwayjs/busboy';
import { sep } from 'path';
import { FileService } from '../service/file.service';
import { CustomError } from '../error/custom.error';
import { split } from 'lodash';

@Controller('/file')
export class FileController {
  @Inject()
  ctx: Context;

  @Config('uploadFilePath')
  uploadFilePath: string;

  @Inject()
  service: FileService;
  /**
   * 上传单个文件 临时
   * @param files
   * @returns 文件临时路径
   */
  @Post('/upload_single/temp', {
    summary: '上传单个临时文件, 返回的是文件的临时路径',
    middleware: [UploadMiddleware],
  })
  async importSingleFile(@Files() files: Array<UploadFileInfo>) {
    return files[0].data;
  }

  @Post('/upload_single/local', {
    summary: '本地上传文件',
    middleware: [
      createMiddleware(UploadMiddleware, {
        mode: 'stream',
        limits: { fileSize: 1024 * 1024 * 50 },
      }),
    ],
  })
  async saveSingleFile(@Files() files: Array<UploadStreamFileInfo>) {
    if (!files || !files.length) {
      throw new CustomError('上传文件失败,请稍后重试！');
    }
    // const rootPath = split(__dirname, sep)[0];
    // const filePath = `${rootPath}${sep}${this.uploadFilePath}`;
    await this.service.saveFile(files[0], this.uploadFilePath);
    return `/statics/${files[0].filename}`;
  }

  @Del('/single', { summary: '删除单个文件' })
  async deleteSingleFile(@Param() fileName: string) {
    const rootPath = split(__dirname, sep)[0];
    const filePath = `${rootPath}${sep}${this.uploadFilePath}${fileName}}`;
    await this.service.deleteFile(filePath);
    return true;
  }
}
