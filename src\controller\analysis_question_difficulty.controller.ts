import { Controller, Get, Inject, Query } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { AnalysisQuestionDifficultyService } from '../service/analysis_question_difficulty.service';

@Controller('/analysis_question_difficulty')
export class AnalysisQuestionDifficultyController {
  @Inject()
  ctx: Context;

  @Inject()
  service: AnalysisQuestionDifficultyService;

  @Get('/statistic', { summary: '统计' })
  async statistic(@Query() query: any) {
    return this.service.statistic(query);
  }

  @Get('/calculate', { summary: '计算试题难度' })
  async calculateDifficulty() {
    return this.service.calculateDifficulty();
  }
}
