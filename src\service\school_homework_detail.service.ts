import { Inject, Logger, Provide } from '@midwayjs/core';
import {
  HomeworkDetailTemplateMapping,
  // SchoolHomework,
  SchoolHomeworkDetail,
  // TextbookTempDetail,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { flattenDeep } from 'lodash';
import { SystemQuestionService } from './system_question.service';

@Provide()
export class SchoolHomeworkDetailService extends BaseService<SchoolHomeworkDetail> {
  @Inject()
  ctx: Context;

  @Logger()
  logger;

  @Inject()
  systemQuestionService: SystemQuestionService;

  constructor() {
    super('校本作业详情');
  }
  getModel = () => {
    return SchoolHomeworkDetail;
  };

  /**
   * 查询列表
   *
   * @param {*} query 查询条件
   * @param {*} offset 分页，跳过
   * @param {*} limit 分页，截取
   * @return {*} res
   * @memberof SchoolHomeworkDetailService
   */
  async getList(query, offset, limit) {
    const queryOption: any = {};
    const result: any = {};

    const hasPaging = offset !== undefined || limit !== undefined;
    if (hasPaging) {
      queryOption.offset = Number(offset) || 0;
      queryOption.limit = Number(limit) || 10;
    }

    const where = {
      ...query,
      // 自定义查询参数参数
      status: '已发布',
    };

    queryOption.where = where;

    // 其他参数
    // queryOption.order = [['createdAt', 'desc']];

    const res = await SchoolHomeworkDetail.findAll(queryOption);
    result.list = res;

    if (hasPaging) {
      // 获取总数
      const total = await SchoolHomeworkDetail.count({
        where,
        distinct: true,
      });
      result.total = total || 0;
    }
    return result;
  }

  /**
   * 查询单个
   *
   * @param {*} id id
   * @return {*} res
   * @memberof SchoolHomeworkDetailService
   */
  async get(id) {
    const result = await SchoolHomeworkDetail.findOne({
      where: {
        id,
      },
    });
    return result;
  }

  /**
   * 创建
   *
   * @param {*} data 数据
   * @return {*} res
   * @memberof SchoolHomeworkDetailService
   */
  async create(data) {
    const res = await SchoolHomeworkDetail.create(data);
    return res;
  }

  /**
   * 更新
   *
   * @param {*} data 数据
   * @param {*} id id
   * @memberof SchoolHomeworkDetailService
   */
  async update(data, id) {
    await SchoolHomeworkDetail.update(data, {
      where: {
        id,
      },
    });
  }

  /**
   * 按条件更新
   *
   * @param {*} data 数据
   * @param {*} [where={}] 匹配条件
   * @memberof SchoolHomeworkDetailService
   */
  async edit(data, where = {}) {
    await SchoolHomeworkDetail.update(data, { where });
  }

  /**
   * 删除
   *
   * @param {*} id id
   * @memberof SchoolHomeworkDetailService
   */
  async destroy(id) {
    // 删除校本作业的题目时，需要删除关联的题目映射数据
    try {
      // 查询校本小节下的所有小题信息
      const dataChildren = await this.getDetailChildren(id);
      const childrenInfos = flattenDeep(
        dataChildren.map(v => v.toJSON().children)
      );
      const questionIds = childrenInfos
        .filter(v => v.questionId)
        .map(v => v.questionId);
      if (questionIds.length > 0) {
        // 删除校本题库中的题目
        await this.systemQuestionService.destroyByIds(questionIds, 'school');
        await HomeworkDetailTemplateMapping.destroy({
          where: { schoolDetailId: id },
        });
      }
      // 删除校本小节映射数据
      const mappingIds = childrenInfos.map(c => c.dataValues.id);
      await HomeworkDetailTemplateMapping.destroy({
        where: { schoolDetailId: mappingIds },
      });
      // 删除校本小节
      await SchoolHomeworkDetail.destroy({
        where: {
          id,
        },
      });
    } catch (error) {
      this.logger.error(
        `Failed to execute destroy schoolWorkDetail, message is: ${error.message}`
      );
      throw error;
    }
  }

  async getDetailChildren(id) {
    const result = await SchoolHomeworkDetail.findAll({
      where: {
        pid: id,
      },
      include: [{ model: SchoolHomeworkDetail, as: 'children' }],
      order: [['orderIndex', 'asc']],
    });
    return result;
  }

  async bulkCreate(param, id) {
    // const { data, textbook_temp_id } = param;
    const schoolWorkDetail = await this.get(id);
    if (!schoolWorkDetail && schoolWorkDetail.type !== '小节') {
      throw new CustomError('测评作业不存在');
    }
    let transaction;
    try {
      transaction = await SchoolHomeworkDetail.sequelize.transaction();
      // // 删除之前创建的题目
      // const deleteList = await this.getDetailChildren(schoolWorkDetail.id);
      // const delIds = [];
      // const allIds = [];
      // deleteList.forEach(d => {
      //   const { id, children } = d.toJSON();
      //   delIds.push(id);
      //   allIds.push(...children.map(v => v.id));
      // });
      // const tempMappingData = await HomeworkDetailTemplateMapping.findAll({
      //   where: { schoolDetailId: allIds },
      // }); // 当前校本详情下所有映射数据
      // const mappingData = tempMappingData.map(v => v.dataValues);
      // const textbookTempDetails = await TextbookTempDetail.findAll({
      //   where: { textbook_temp_id, type: ['大题', '小题'] },
      // }); // 校本作业关联的范本中的所有数据
      // let insertMappingData = []; // 需要新增的映射数据
      // const questionBankIds = []; // 修改后的所有题目的所有源题id数组
      // const newQuestionIds = []; // 变动的题目id数组
      // const oldQuestionIds = mappingData
      //   .filter(m => m.type === '小题')
      //   .map(v => v.schoolDetailQuestionId); // 旧映射数据中的所有小题数组
      // let i = 0;
      // for (const tmlx of data) {
      //   const { children, ...info } = tmlx;
      //   delete info.id;
      //   //TODO:验证
      //   const findRes = false;
      //   // const findRes = textbookTempDetails.find(v => v.code === info.code); // 找到大题是否在范本中有
      //   const dt = await this.create({
      //     ...info,
      //     type: '大题',
      //     orderIndex: i++,
      //     textbookCatalogId: schoolWorkDetail.textbookCatalogId,
      //     schoolHomeworkId: schoolWorkDetail.schoolHomeworkId,
      //     pid: id,
      //   });
      //   const dtMapping = {
      //     school_detail_id: dt.id,
      //     type: '大题',
      //   };
      //   if (findRes) dtMapping.temp_detail_id = findRes.id;
      //   insertMappingData.push(dtMapping);
      //   const xt = children.map((v, index) => {
      //     delete v.id;
      //     let questionId = v.question_bank_id;
      //     const oldMapping = _findOldMapping(questionId, mappingData);
      //     if (!oldMapping) {
      //       // 说明该题目变动
      //       questionId = new ObjectId().toString();
      //       const obj = {
      //         oldId: v.question_bank_id,
      //         newId: questionId,
      //       };
      //       newQuestionIds.push(obj); // 新增的小题id数组
      //       questionBankIds.push(v.question_bank_id);
      //       v.question_bank_id = questionId;
      //     } else {
      //       questionBankIds.push(oldMapping.temp_detail_question_id);
      //     }
      //     return {
      //       ...v,
      //       type: '小题',
      //       orderIndex: index,
      //       textbookCatalogId: schoolWorkDetail.textbookCatalogId,
      //       schoolHomeworkId: schoolWorkDetail.schoolHomeworkId,
      //       pid: dt.id,
      //     };
      //   });
      //   const xtRes = await SchoolHomeworkDetail.bulkCreate(xt, transaction);
      //   const xtMappingArr = xtRes.map(xt => {
      //     const xtMapping = {
      //       school_detail_id: xt.dataValues.id,
      //       school_detail_question_id: xt.dataValues.questionId,
      //       type: '小题',
      //     };
      //     if (!newQuestionIds.find(n => n.newId === xt.dataValues.questionId)) {
      //       // 表示该小题未变动
      //       const oldXTMapping = _findOldMapping(xt.questionId, mappingData);
      //       xtMapping.temp_detail_id = oldXTMapping.temp_detail_id;
      //       xtMapping.temp_detail_question_id =
      //         oldXTMapping.temp_detail_question_id;
      //     } else {
      //       xtMapping.temp_detail_question_id = newQuestionIds.find(
      //         n => n.newId === xt.dataValues.question_bank_id
      //       ).oldId;
      //     }
      //     return xtMapping;
      //   });
      //   insertMappingData.push(...xtMappingArr);
      // }
      // insertMappingData = insertMappingData.flat();
      // if (insertMappingData.length > 0) {
      //   // 插入新的映射表数据
      //   await HomeworkDetailTemplateMapping.bulkCreate(insertMappingData, {
      //     transaction,
      //   });
      // }
      // if (questionBankIds.length > 0) {
      //   if (oldQuestionIds.length > 0) {
      //     await model.SchoolMgQuestion.deleteMany({
      //       _id: { $in: oldQuestionIds },
      //     }); // 删除旧题
      //   }
      //   const questions = await model.MgQuestion.find({
      //     _id: { $in: questionBankIds },
      //   }).exec();
      //   const bulk = questions.map(question => {
      //     const data = JSON.parse(JSON.stringify(question));
      //     const mapping = insertMappingData.find(
      //       v => v.temp_detail_question_id === data._id
      //     );
      //     const tempId = mapping
      //       ? mapping.school_detail_question_id
      //       : newQuestionIds.find(v => v.oldId === data._id).newId;
      //     const id = new ObjectId(tempId);
      //     data._id = id;
      //     data.deleted = false;
      //     return {
      //       updateOne: {
      //         filter: { _id: id },
      //         update: { $set: data },
      //         upsert: true,
      //       },
      //     };
      //   });
      //   await model.SchoolMgQuestion.bulkWrite(bulk);
      // }
      // if (deleteList.length > 0) {
      //   await SchoolHomeworkDetail.destroy({
      //     where: { id: deleteList.map(v => v.id) },
      //     transaction,
      //   });
      // }
      // if (mappingData.length > 0) {
      //   // 删除旧的映射表数据
      //   await HomeworkDetailTemplateMapping.destroy({
      //     where: { id: mappingData.map(m => m.id) },
      //     transaction,
      //   });
      // }
      //TODO:重新检查 重构逻辑
      console.log(param);
      await transaction.commit();
    } catch (error) {
      if (transaction) await transaction.rollback();
      this.logger.error(
        `Failed to updateSchoolWorkDetail, error is:${error.message}`
      );
      throw new CustomError('批量创建失败');
    }
  }
}
