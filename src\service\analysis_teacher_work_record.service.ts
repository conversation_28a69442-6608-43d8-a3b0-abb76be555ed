import { Inject, Provide } from '@midwayjs/core';
import { AnalysisTeacherWorkRecord } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { col, fn } from 'sequelize';

@Provide()
export class AnalysisTeacherWorkRecordService extends BaseService<AnalysisTeacherWorkRecord> {
  @Inject()
  ctx: Context;

  constructor() {
    super('教师作业分析');
  }
  getModel = () => {
    return AnalysisTeacherWorkRecord;
  };

  /**
   * 统计各教师作业情况
   * @param query 请求参数
   */
  async statistic(query: any) {
    const { semester_code, eneterprise_code, grade_code, subject_id } = query;
    const queryOption = {};
    if (semester_code) {
      queryOption['semester_code'] = semester_code;
    }
    if (eneterprise_code) {
      queryOption['enterprise_code'] = eneterprise_code;
    }
    if (grade_code) {
      queryOption['grade_code'] = grade_code;
    }
    if (subject_id) {
      queryOption['subject_id'] = subject_id;
    }

    const res = await AnalysisTeacherWorkRecord.findAll({
      where: queryOption,
      attributes: [
        'teacher_id',
        'teacher_name',
        'subject_id',
        'subject',
        'suggested_time',
        [fn('SUM', col('question_number')), 'total_question_number'],
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        [fn('AVG', col('average_time')), 'total_average_time'],
      ],
      group: [
        'teacher_id',
        'teacher_name',
        'subject_id',
        'subject',
        'suggested_time',
      ],
    });
    return res;
  }
}
