import { Inject, Provide, Logger } from '@midwayjs/core';
import { SchoolQuestions } from '../model';
import { Context } from '@midwayjs/koa';
import { InjectEntityModel } from '@midwayjs/typegoose';
import { ReturnModelType } from '@typegoose/typegoose';
import { pick, assign } from 'lodash';
import { Types } from 'mongoose';
import { QuestionService } from './question.service';
import { MONGO_MODEL_KEY } from '../common/Constants';

@Provide()
export class SchoolQuestionService {
  @Inject()
  ctx: Context;

  @Logger()
  logger;

  @Inject()
  questionService: QuestionService;

  @InjectEntityModel(SchoolQuestions)
  schoolQuestions: ReturnModelType<typeof SchoolQuestions>;

  /**
   * 查询列表
   *
   * @param {*} queryObj 查询条件
   * @param {*} current 当前页码
   * @param {*} pageSize 每页条数
   * @memberof SchoolQuestionService
   */
  async index(queryObj, current, pageSize, order = { createdAt: -1 }) {
    return await this.questionService.findQuestionsByFilter(
      queryObj,
      current,
      pageSize,
      order,
      MONGO_MODEL_KEY.SCHOOL
    );
  }

  /**
   * 查询单个
   * @param {*} id
   * @return {*} 数据对象
   * @memberof SchoolQuestionService
   */
  async show(id) {
    return await this.questionService.findById(id, MONGO_MODEL_KEY.SCHOOL);
    // const schoolQuestion = await this.schoolQuestions.findById(id).exec();
    // if (!schoolQuestion) {
    //   throw new CustomError('未找到数据');
    // }
    // // 如果是父子题 查找子题
    // if (schoolQuestion.isCompose) {
    //   const children = await this.schoolQuestions.find({
    //     pid: schoolQuestion._id,
    //   });
    //   return assign(schoolQuestion.toJSON(), { children });
    // }
    // return schoolQuestion;
  }

  /**
   * 创建
   * @param {*} info
   * @return {*}
   * @memberof SchoolQuestionService
   */
  async create(info) {
    return await this.questionService.create(info, MONGO_MODEL_KEY.SCHOOL);
    // const res = await this.schoolQuestions.create(info);
    // return res;
  }

  /**
   * 更新试题
   * @param {*} _id 试题id
   * @param {*} info 更新对象
   * @memberof SchoolQuestionService
   */
  async update(_id, info) {
    return await this.questionService.update(_id, info, MONGO_MODEL_KEY.SCHOOL);
    // await this.show(_id);
    // await this.schoolQuestions.updateOne(
    //   { _id },
    //   { ...info, updatedAt: new Date() }
    // );
  }

  /**
   * 删除单个
   * @param {*} _id
   * @memberof SchoolQuestionService
   */
  async destroy(_id) {
    await this.questionService.destroy(_id, MONGO_MODEL_KEY.SCHOOL);
    // await this.schoolQuestions.deleteOne({ _id });
  }

  /**
   * 批量删除
   * @param {*} ids id数组
   * @memberof SchoolQuestionService
   */
  async destroybulk(ids) {
    await this.questionService.destroyBulk(ids, MONGO_MODEL_KEY.SCHOOL);
    // await this.schoolQuestions.deleteMany({ _id: ids });
  }

  /**
   * 批量更新
   * @param {*} ids id数组
   * @param {*} data 更新对象
   * @return {*} 更新结果
   * @memberof SchoolQuestionService
   */
  async updatebulk(ids, data) {
    return await this.questionService.updateBulk(
      ids,
      data,
      MONGO_MODEL_KEY.SCHOOL
    );
    // const res = await this.schoolQuestions
    //   .updateMany({ _id: { $in: ids } }, { $set: data })
    //   .exec();
    // return res;
  }

  /**
   * 构造父子题 子题数据结构
   * @param parentInfo 父题数据
   * @param children 子题数据
   * @returns
   */
  async buildChildren(parentInfo, children: any[]) {
    const commonInfo = pick(parentInfo, [
      'difficulty', //难度
      'grade', //年级
      'textbookVersion', //教材版本
      'gradeSection', //学段
      'subject', //学科
      'unit', //单元
      'period', //课时
      'author', //作者
      'source', //来源
      'volume', //册次
      'tier', //层次
      'year', //年份
      'cognitiveHierarchy', //认知层次
      'coreQuality', //核心素养
      'investigationAbility', //考察能力
      'createType', //题目创建类型
      'enterpriseCode', //企业编码
      'sourceTable', //来源试题表
      'sourceId', //来源试题id
      'classification', //分类
      'tableName', //试题所在表名称
    ]);
    const childQuestions = children.map(item => {
      item.pid = parentInfo._id;
      item.parentStem = parentInfo.name;
      return assign(item, commonInfo);
    });
    return childQuestions;
  }

  /**
   * 创建父子题 父子题分别为独立的题
   * @param {*} info 父子题数据
   * @return {*} 创建结果
   * @memberof SchoolQuestionService
   */
  async createAssociateQuestion(data) {
    const { children, ...info } = data;
    const pid = new Types.ObjectId()._id;
    const parentQuestion = {
      _id: pid,
      ...info,
    };
    const childQuestions = await this.buildChildren(parentQuestion, children);
    return await this.schoolQuestions.insertMany([
      parentQuestion,
      ...childQuestions,
    ]);
  }

  /**
   * 编辑父子题
   * @param {*} data 子题数据数组
   * @param {*} pid 父题id
   * @return {*} 更新结果
   * @memberof SchoolQuestionService
   */
  async updateAssociateQuestion(data, pid: string) {
    const { children, ...info } = data;
    await this.schoolQuestions
      .deleteMany({ pid: new Types.ObjectId(pid) })
      .exec();

    const childQuestions = await this.buildChildren(info, children);
    await this.schoolQuestions.updateOne(
      { _id: new Types.ObjectId(pid) },
      data
    );
    return await this.schoolQuestions.insertMany(childQuestions);
  }

  /**
   * 根据父题id删除关联的子题
   * @param {*} pid 父题id
   * @memberof SchoolQuestionService
   */
  async deleteQuestionByPid(pid: string) {
    const questionId = new Types.ObjectId(pid);
    await this.schoolQuestions
      .deleteMany({ $or: [{ pid: questionId }, { _id: questionId }] })
      .exec();
  }

  /**
   *
   * @param queryObj
   * @returns
   */
  async findQuestionsForStatic(queryObj) {
    const pipe = [
      {
        $match: {
          ...queryObj,
          pid: null,
        },
      },
      {
        $project: {
          enterpriseCode: 1,
          subject: {
            id: '$subject.id',
            name: '$subject.name',
          },
          gradeSection: 1,
          difficulty: {
            name: '$difficulty.name',
            code: '$difficulty.code',
          },
        },
      },
      {
        $group: {
          _id: {
            enterpriseCode: '$enterpriseCode',
            subject: '$subject',
            difficulty: '$difficulty',
          },
          count: {
            $sum: 1,
          },
          gradeSection: {
            $first: '$gradeSection',
          },
        },
      },
      {
        $project: {
          _id: 0,
          enterpriseCode: '$_id.enterpriseCode',
          subjectId: '$_id.subject.id',
          subjectName: '$_id.subject.name',
          gradeSectionCode: '$gradeSection.code',
          gradeSectionName: '$gradeSection.name',
          difficultyCode: '$_id.difficulty.code',
          difficultyName: '$_id.difficulty.name',
          count: 1,
        },
      },
    ];
    const res = await this.schoolQuestions.aggregate(pipe).exec();
    return res;
  }
}
