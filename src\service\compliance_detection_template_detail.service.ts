import { Inject, Logger, Provide } from '@midwayjs/core';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import {
  ComplianceDetectionTemplateDetail,
  ComplianceDetectionTemplateQuestion,
  ComplianceDetectionTemplateStruct,
} from '../entity';
import { CustomError } from '../error/custom.error';
import { ComplianceDetectionTemplateService } from './compliance_detection_template.service';
import { randomUUID } from '@midwayjs/core/dist/util/uuid';
import { QuestionService } from './question.service';

@Provide()
export class ComplianceDetectionTemplateDetailService extends BaseService<ComplianceDetectionTemplateDetail> {
  @Inject()
  ctx: Context;

  @Inject()
  templateService: ComplianceDetectionTemplateService;
  @Inject()
  questionService: QuestionService;
  @Logger()
  logger;

  constructor() {
    super('达标检测范本详情');
  }

  getModel = () => {
    return ComplianceDetectionTemplateDetail;
  };

  /**
   * 创建达标范本详情
   * @param info
   * @param questions
   * @returns
   */
  async create(body: any) {
    const { questions, structs, ...info } = body;
    const transaction =
      await ComplianceDetectionTemplateDetail.sequelize.transaction();
    try {
      // 创建作业
      const res = await ComplianceDetectionTemplateDetail.create(info, {
        transaction,
      });
      // 如果创建作业时有题目信息，则直接入库
      if (questions && questions.length > 0) {
        const questionArr = questions.map((item, index) => {
          return {
            ...item,
            detail_id: res.id,
            sort_order: index + 1,
          };
        });
        await ComplianceDetectionTemplateQuestion.bulkCreate(questionArr, {
          transaction,
        });
      }
      // 如果创建作业时有结构信息，则直接入库
      if (structs && structs.length > 0) {
        const structArr = structs.map((item, index) => {
          return {
            ...item,
            detail_id: res.id,
            sort_order: index + 1,
          };
        });
        await ComplianceDetectionTemplateStruct.bulkCreate(structArr, {
          transaction,
        });
      }
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 获取单个
   * @param id id
   */
  async show(id: number) {
    return await ComplianceDetectionTemplateDetail.findOne({
      where: { id },
      include: [{ model: ComplianceDetectionTemplateQuestion }],
    });
  }

  /**
   * 导入达标范本
   * @param filePath 文件路径
   * @param questionInfo 试题参数
   * @param detailId 达标详情id
   * @returns
   */
  async importComplianceDetails(
    filePath: string,
    questionInfo: any,
    detailId: number
  ) {
    const transaction =
      await ComplianceDetectionTemplateDetail.sequelize.transaction();
    // 创建作业详情
    try {
      // 先导入试题
      const questions = await this.questionService.importQuestions(
        filePath,
        questionInfo
      );
      // 需要先删除之前关联的达标试题、达标结构
      await ComplianceDetectionTemplateQuestion.destroy({
        where: {
          detail_id: detailId,
        },
        transaction,
      });
      await ComplianceDetectionTemplateStruct.destroy({
        where: {
          detail_id: detailId,
        },
        transaction,
      });
      // 达标作业详情试题
      // 达标作业详情结构
      if (questions && questions.length > 0) {
        const designQuestions = [];
        const questionIds = [];
        questions.forEach((item, index) => {
          questionIds.push(item._id);
          designQuestions.push({
            detail_id: detailId,
            sort_order: index + 1,
            question_id: item._id,
            source_table: item.tableName,
          });
        });
        const defaultStruct = {
          id: `hidden_${randomUUID()}`,
          name: '暂无分组',
          detail_id: detailId,
          sort_order: 1,
          questionIds: questionIds,
        };
        await ComplianceDetectionTemplateQuestion.bulkCreate(designQuestions, {
          transaction,
        });
        await ComplianceDetectionTemplateStruct.create(defaultStruct, {
          transaction,
        });
      }
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      this.logger.error(
        `Failed to importComplianceDetails, message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError(error.message);
    }
  }
}
