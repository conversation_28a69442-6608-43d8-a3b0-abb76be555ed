import { Inject, Provide } from '@midwayjs/core';
import {
  ComplianceDetectionDesignCatalog,
  ComplianceDetectionDesignCatalogAttributes,
  ComplianceDetectionDesignDetail,
  ComplianceDetectionDesignQuestion,
  ComplianceDetectionDesignStruct,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { Op } from 'sequelize';
import { randomUUID } from '@midwayjs/core/dist/util/uuid';

@Provide()
export class ComplianceDetectionDesignCatalogService extends BaseService<ComplianceDetectionDesignCatalog> {
  @Inject()
  ctx: Context;

  constructor() {
    super('达标检测设计目录');
  }
  getModel = () => {
    return ComplianceDetectionDesignCatalog;
  };

  /**
   * 复制教材目录信息及作业信息到达标检测设计目录
   * @param sourceData 源目录数据
   * @param design_id 达标检测设计id
   * @param transaction 事务
   * @param templateDetail 达标检测作业详情 可不传 不传直接复制目录
   */
  async copyCatalog(
    sourceData,
    design_id,
    transaction = null,
    templateDetail = []
  ) {
    const idMapping = {};

    // 分离一级目录和子目录
    const firstLevelItems = sourceData.filter(item => item.parent_id === null);
    const childrenItems = sourceData.filter(item => item.parent_id !== null);

    // 定义通用的目录插入逻辑
    const insertCatalog = async (items, parentMapping = {}) => {
      for (const item of items) {
        const catalogInfo = {
          old_id: item.id, //原本目录id
          old_title: item.title, //原本目录名称
          design_id, // 达标检测设计id
          textbookChecklist_id: item.textbookChecklist_id, //教材名录id
          title: item.title, //名称
          sort_order: item.sort_order, //排序
          parent_id: parentMapping[item.parent_id] || null, // 确保 parent_id 存在
        };

        const catalog = await ComplianceDetectionDesignCatalog.create(
          catalogInfo,
          { transaction }
        );
        idMapping[item.id] = catalog.id;
      }
    };

    // 插入第一层级的数据
    await insertCatalog(firstLevelItems);
    // 插入子目录的数据
    await insertCatalog(childrenItems, idMapping);

    // 如果有达标检测详细信息存在，则复制达标检测作业信息
    if (templateDetail && templateDetail.length > 0) {
      // 根据目录信息获取达标检测作业详情
      for (const detail of templateDetail) {
        const { questions, structs, ...info } = detail.toJSON();
        // 复制达标检测作业信息
        const detailInfo = {
          design_id, //达标检测设计id
          textbookChecklist_id: info.textbookChecklist_id, //教材名录id
          catalogIds: info.catalogIds, //目录id集合
          catalogs: info.catalogs, //目录
          name: info.name, //名称
          detection_type: info.detection_type, //检测类型 单元 其中 期末
          status: info.status, //状态 草稿 发布
        };
        const designDetail = await ComplianceDetectionDesignDetail.create(
          detailInfo,
          { transaction }
        );

        // 批量复制达标检测作业结构信息
        if (structs && structs.length > 0) {
          const structData = structs.map(struct => ({
            id: struct.id
              ? `${struct.id}_${designDetail.id}`
              : `hidden_${randomUUID()}`, //结构id
            designDetail_id: designDetail.id, //达标检测作业详情id
            name: struct.name, //结构名称
            questionIds: struct.questionIds, //试题ids
            sort_order: struct.sort_order, //排序
          }));
          await ComplianceDetectionDesignStruct.bulkCreate(structData, {
            transaction,
          });
        }

        // 批量复制达标检测作业题目信息
        if (questions && questions.length > 0) {
          const questionData = questions.map(question => ({
            designDetail_id: designDetail.id, //达标检测作业详情id
            question_id: question.question_id, //试题id
            sort_order: question.sort_order, //排序
            source_table: question.source_table, //试题来源表
          }));
          await ComplianceDetectionDesignQuestion.bulkCreate(questionData, {
            transaction,
          });
        }
      }
    }
  }

  /**
   * 复制教材目录信息及作业信息到达标检测设计目录 优化逻辑
   * @param sourceData 源目录数据
   * @param design_id 达标检测设计id
   * @param transaction 事务
   * @param templateDetail 达标检测作业详情 可不传 不传直接复制目录
   */
  async copyCatalogNew(
    sourceData,
    design_id,
    transaction = null,
    templateDetail = []
  ) {
    const idMapping = {};

    // 分离一级目录和子目录
    const firstLevelItems = sourceData.filter(item => item.parent_id === null);
    const childrenItems = sourceData.filter(item => item.parent_id !== null);

    // 定义通用的目录插入逻辑
    const insertCatalog = async (items, parentMapping = {}) => {
      const catalogData = items.map(item => ({
        old_id: item.id, //原本目录id
        old_title: item.title, //原本目录名称
        design_id, // 达标检测设计id
        textbookChecklist_id: item.textbookChecklist_id, //教材名录id
        title: item.title, //名称
        sort_order: item.sort_order, //排序
        parent_id: parentMapping[item.parent_id] || null, // 确保 parent_id 存在
      }));

      const catalogsRes = await ComplianceDetectionDesignCatalog.bulkCreate(
        catalogData,
        { transaction }
      );

      // 更新映射关系
      catalogsRes.forEach((catalog, index) => {
        idMapping[items[index].id] = catalog.id;
      });
    };

    // 插入第一层级的数据
    await insertCatalog(firstLevelItems);
    // 插入子目录的数据
    await insertCatalog(childrenItems, idMapping);

    // 批量创建作业详情信息
    if (templateDetail && templateDetail.length > 0) {
      const detailData = [];
      templateDetail.forEach(detail => {
        const newCatalogIds = detail.catalogIds.map(oldId => idMapping[oldId]);
        detailData.push({
          design_id, //达标检测设计id
          textbookChecklist_id: detail.textbookChecklist_id, //教材名录id
          catalogIds: detail.catalogIds, //目录id集合
          catalogs: detail.catalogs, //目录
          new_catalogIds: newCatalogIds, //新目录ids
          new_catalogs: detail.catalogs, //新目录名称，由于是复制范本中的目录，所以此时目录名称不会变化，与原本目录一致
          name: detail.name, //名称
          detection_type: detail.detection_type, //检测类型 单元 其中 期末
          status: detail.status, //状态 草稿 发布
          duration: detail.duration, //作业时长
        });
      });

      const detailsRes = await ComplianceDetectionDesignDetail.bulkCreate(
        detailData,
        { transaction }
      );

      // 创建 detailIdMapping 用于后续的试题和结构信息创建
      const detailIdMapping = {};
      detailsRes.forEach((detail, index) => {
        const originalDetailId = templateDetail[index].id;
        detailIdMapping[originalDetailId] = detail.id;
      });

      // 批量创建试题信息和结构信息
      const questionData = [];
      const structData = [];

      templateDetail.forEach(detail => {
        const newDetailId = detailIdMapping[detail.id];
        if (newDetailId) {
          const { questions, structs } = detail.toJSON();

          if (questions && questions.length > 0) {
            questions.forEach(question => {
              questionData.push({
                designDetail_id: newDetailId, //达标检测作业详情id
                question_id: question.question_id, //试题id
                sort_order: question.sort_order, //排序
                source_table: question.source_table, //试题来源表
              });
            });
          }

          if (structs && structs.length > 0) {
            structs.forEach(struct => {
              structData.push({
                id: struct.id
                  ? `${struct.id}_${newDetailId}`
                  : `hidden_${randomUUID()}`, //结构id
                designDetail_id: newDetailId, //达标检测作业详情id
                name: struct.name, //结构名称
                questionIds: struct.questionIds, //试题ids
                sort_order: struct.sort_order, //排序
              });
            });
          }
        }
      });
      await ComplianceDetectionDesignQuestion.bulkCreate(questionData, {
        transaction,
      });
      await ComplianceDetectionDesignStruct.bulkCreate(structData, {
        transaction,
      });
    }
  }

  /**
   * 插入节点
   * @param id 参照节点id
   * @param name 新节点名称
   * @param old_id 旧节点id，如果是根节点则为null，主要用于查询题库
   * @param old_title 旧节点名称，如果是根节点则为null，主要用于查询题库
   * @param action 操作类型，insert表示插入到参照节点前，append表示插入到参照节点后
   * @returns 新节点
   */
  async insert(
    id: number,
    title: string,
    old_id: number | null,
    old_title: string,
    action: 'insert' | 'append'
  ) {
    const catalogInfo = await ComplianceDetectionDesignCatalog.findOne({
      where: {
        id,
      },
    });
    if (!catalogInfo) {
      throw new Error('参照节点不存在');
    }
    const sort_order: number =
      action === 'append' ? catalogInfo.sort_order + 1 : catalogInfo.sort_order;
    const info: Omit<ComplianceDetectionDesignCatalogAttributes, 'id'> = {
      /** 教材名录ID */
      textbookChecklist_id: catalogInfo.textbookChecklist_id,
      design_id: catalogInfo.design_id,
      /** 名称 */
      title,
      /** 父ID */
      parent_id: catalogInfo.parent_id,
      old_id,
      old_title,
      /** 排序 */
      sort_order,
    };
    // 查询相同排序有没有被占用
    const isExist = await ComplianceDetectionDesignCatalog.findOne({
      where: {
        textbookChecklist_id: catalogInfo.textbookChecklist_id,
        design_id: catalogInfo.design_id,
        parent_id: catalogInfo.parent_id,
        sort_order,
      },
      attributes: ['id'],
    });
    if (isExist) {
      // 占用了，需要把后面的所有节点的排序都+1
      const todoList: ComplianceDetectionDesignCatalog[] =
        await ComplianceDetectionDesignCatalog.findAll({
          where: {
            textbookChecklist_id: catalogInfo.textbookChecklist_id,
            parent_id: catalogInfo.parent_id,
            sort_order: {
              [Op.gte]: sort_order,
            },
          },
        });
      for (const todo of todoList) {
        todo.sort_order = todo.sort_order + 1;
        await todo.save();
      }
    }
    const newCatalogInfo = await ComplianceDetectionDesignCatalog.create(info);
    return newCatalogInfo;
  }
}
