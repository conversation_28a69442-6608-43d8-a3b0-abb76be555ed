import { DataTypes } from 'sequelize';
import { Column, Model, Table } from 'sequelize-typescript';

export interface QuestionTagAttributes {
  id: number;
  name: string;
  gradeSectionCode: string;
  gradeSection: string;
  subjectCode: string;
  subject: string;
  description: string;
  creator: string;
  updater: string;
  createdAt: Date;
  updatedAt: Date;
}
@Table({ tableName: 'question_tag', comment: '类题标签表', timestamps: true })
export class QuestionTag
  extends Model<QuestionTagAttributes>
  implements QuestionTagAttributes
{
  @Column({
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;
  @Column({
    type: DataTypes.STRING(64),
    allowNull: false,
    comment: '标签名称',
  })
  name: string;
  @Column({
    type: DataTypes.STRING(64),
    allowNull: false,
    comment: '学段code',
  })
  gradeSectionCode: string;
  @Column({
    type: DataTypes.STRING(64),
    allowNull: true,
    comment: '学段',
  })
  gradeSection: string;
  @Column({
    type: DataTypes.STRING(64),
    allowNull: false,
    comment: '学科code',
  })
  subjectCode: string;
  @Column({
    type: DataTypes.STRING(36),
    allowNull: true,
    comment: '学科',
  })
  subject: string;
  @Column({
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: '标签描述',
  })
  description: string;
  @Column({
    type: DataTypes.STRING(36),
    allowNull: true,
    comment: '创建人',
  })
  creator: string;
  @Column({
    type: DataTypes.DATE,
    allowNull: false,
    comment: '创建时间',
  })
  createdAt: Date;
  @Column({
    type: DataTypes.STRING(36),
    allowNull: true,
    comment: '更新人',
  })
  updater: string;
  @Column({
    type: DataTypes.DATE,
    allowNull: false,
    comment: '更新时间',
  })
  updatedAt: Date;
}
