import {
  Table,
  Column,
  Model,
  DataType,
  HasMany,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { COMPOSER_TYPE } from '../common/Constants';
import { ComposerTestPaperDetail } from './composer_test_paper_detail.entity';
import { ComposerPaper } from './composer_paper.entity';
import { ComposerTestPaperStruct } from './composer_test_paper_struct.entity';

export interface ComposerTestPaperAttributes {
  /** 主键ID */
  id: number;
  /** 组卷方案ID */
  composerPaperId: number;
  /** 试卷名称 */
  name: string;
  /** 试卷总分 */
  score: number;
  /** 试卷时长 */
  duration: number;
  /** 组卷类型 */
  ruleType: COMPOSER_TYPE;
  /** 顺序 */
  orderIndex: number;
  /** 来源 */
  source?: string;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
  /** 试卷详情 */
  details?: ComposerTestPaperDetail[];
}

@Table({
  tableName: 'composer_test_paper',
  timestamps: true,
  comment: '试卷表',
  indexes: [
    {
      fields: ['composer_paper_id'],
    },
  ],
})
export class ComposerTestPaper
  extends Model<ComposerTestPaperAttributes>
  implements ComposerTestPaperAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
    field: 'id',
  })
  id: number;

  @ForeignKey(() => ComposerPaper)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '组卷方案ID',
    field: 'composer_paper_id',
  })
  composerPaperId: number;

  @BelongsTo(() => ComposerPaper, {
    foreignKey: 'composerPaperId',
    as: 'composerPaper',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  composerPaper: ComposerPaper;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '试卷名称',
    field: 'name',
  })
  name: string;

  @Column({
    type: DataType.FLOAT,
    allowNull: false,
    comment: '试卷总分',
    field: 'score',
  })
  score: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '试卷时长',
    field: 'duration',
  })
  duration: number;
  @Column({
    type: DataType.ENUM(COMPOSER_TYPE.MANUAL, COMPOSER_TYPE.AUTO),
    allowNull: false,
    comment: '组卷类型',
    field: 'rule_type',
  })
  ruleType: COMPOSER_TYPE;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '顺序',
    field: 'order_index',
  })
  orderIndex: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '来源',
    field: 'source',
  })
  source?: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '创建时间',
    field: 'created_at',
  })
  createdAt?: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '更新时间',
    field: 'updated_at',
  })
  updatedAt?: Date;

  @HasMany(() => ComposerTestPaperDetail, {
    foreignKey: 'composerTestPaperId',
    as: 'details',
  })
  details?: ComposerTestPaperDetail[];

  @HasMany(() => ComposerTestPaperStruct, {
    foreignKey: 'composerTestPaperId',
    as: 'structs',
  })
  structs?: ComposerTestPaperStruct[];
}
