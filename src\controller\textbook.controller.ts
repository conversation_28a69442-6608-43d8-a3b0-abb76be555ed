import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { TextbookService } from '../service/textbook.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { Publisher, Subject } from '../entity';

@Controller('/textbooks')
export class TextbookController {
  @Inject()
  ctx: Context;

  @Inject()
  service: TextbookService;

  @Get('/', { summary: '查询教材列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      filter,
      sort,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // alias支持模糊查询
    if (queryInfo.alias) {
      queryInfo.alias = {
        [Op.like]: `%${queryInfo.alias}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      filter,
      sort,
      order: [['id', 'ASC']],
      include: [Subject, Publisher],
    });
  }

  @Get('/:id', { summary: '按ID查询教材' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定教材');
    }
    return res;
  }

  @Post('/', { summary: '新增教材' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新教材' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除教材' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }
}
