import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { TextbookConfig } from '.';

export interface ClassRelationAttributes {
  /** id */
  id: number;
  /** 作业配置id */
  textbook_config_id?: number;
  /** 单元 */
  unit?: string;
  /** 课时 */
  period?: string;
  /** 相似题来源code */
  similarSourceCode?: string;
  /** 相似题来源 */
  similarSource?: string;
  /** 匹配相似题单元 */
  match_unit?: string;
  /** 匹配相似题课时 */
  match_period?: string;
}

@Table({
  tableName: 'class_relation',
  timestamps: true,
  comment: '课时关系表',
})
export class ClassRelation
  extends Model<ClassRelationAttributes>
  implements ClassRelationAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'id',
  })
  id: number;

  @ForeignKey(() => TextbookConfig)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '作业配置id',
  })
  textbook_config_id?: number;

  @BelongsTo(() => TextbookConfig, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  textbook_config?: TextbookConfig;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '单元',
  })
  unit?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '课时',
  })
  period?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '相似题来源code',
  })
  similarSourceCode?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '相似题来源',
  })
  similarSource?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '匹配相似题单元',
  })
  match_unit?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '匹配相似题课时',
  })
  match_period?: string;
}
