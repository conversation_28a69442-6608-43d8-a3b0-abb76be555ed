import { Inject, Provide } from '@midwayjs/core';
import { User, UserAttributes } from '../entity/user.entity';
import { Role } from '../entity/role.entity';
import { EnterpriseService } from './enterprise.service';
import { BaseService } from '../common/BaseService';
import { UserIdentity, UserInfo_Carry } from './carry/interface';
import { CustomError } from '../error/custom.error';

@Provide()
export class UserService extends BaseService<User> {
  @Inject()
  enterpriseService: EnterpriseService;

  constructor() {
    super('用户');
  }

  getModel() {
    return User;
  }

  async getOrcreateFromSSO(ssoUser: UserInfo_Carry) {
    // admin账号不做被动同步
    if (ssoUser.username === 'admin') {
      return await User.findOne({
        where: {
          username: ssoUser.username,
        },
        include: [
          {
            model: Role,
            attributes: ['id', 'name'],
          },
        ],
        attributes: ['id', 'username', 'nickname', 'avatar', 'phone', 'email'],
      });
    }
    // 获取单位信息，不存在则创建
    const enterprise = await this.enterpriseService.getOrcreateFromSSO({
      name: ssoUser.orgCodeTxt || '测试学校', // TODO 临时加一个默认学校名称，sso那边能返回具体信息后改为必填,
      code: ssoUser.orgCode,
      areaCode: ssoUser.areaCode,
    });
    if (!enterprise) {
      throw new CustomError('单位信息获取失败');
    }

    // 获取角色信息
    let roleName: string;
    switch (ssoUser.userIdentity) {
      case UserIdentity.市级管理员:
        roleName = '市管理员';
        break;
      case UserIdentity.区县管理员:
        roleName = '区管理员';
        break;
      case UserIdentity.学校管理员:
        switch (ssoUser.jobNumber) {
          case '高中':
            roleName = '高中管理员';
            break;
          case '初中':
            roleName = '初中管理员';
            break;
          case '小学':
            roleName = '小学管理员';
            break;
          default:
            roleName = '小学管理员'; //TODO 临时设置默认值，后期取消
        }
        break;
      case UserIdentity.教师:
        switch (ssoUser.jobNumber) {
          case '高中':
            roleName = '高中教师';
            break;
          case '初中':
            roleName = '初中教师';
            break;
          case '小学':
            roleName = '小学教师';
            break;
          default:
            roleName = '小学教师'; //TODO 临时设置默认值，后期取消
        }
        break;
      default:
        throw new CustomError('当前用户身份在作业设计模块无业务');
    }
    const role = await Role.findOne({ where: { name: roleName } });
    if (!role) {
      throw new CustomError('当前用户身份在作业设计模块无业务');
    }

    // 封装sso传过来的用户信息
    const userInfo: Omit<UserAttributes, 'id'> = {
      username: ssoUser.username,
      password: ssoUser.username,
      nickname: ssoUser.realname,
      avatar: ssoUser.avatar,
      phone: ssoUser.phone,
      email: ssoUser.email,
      isActive: true,
      enterprise_id: enterprise.id,
    };
    // 先检查当前用户是否存在
    const currentUser = await User.findOne({
      where: {
        username: ssoUser.username,
      },
      include: [
        {
          model: Role,
          attributes: ['id', 'name'],
        },
      ],
      attributes: ['id', 'username', 'nickname', 'avatar', 'phone', 'email'],
    });

    if (currentUser) {
      console.log(currentUser.roles);
      // 用户存在
      if (!currentUser.roles.map(r => r.name).includes(roleName)) {
        // 更新角色
        await currentUser.$set('roles', [role]);
      }
      if (
        currentUser.nickname === userInfo.nickname &&
        currentUser.avatar === userInfo.avatar &&
        currentUser.phone === userInfo.phone &&
        currentUser.email === userInfo.email &&
        currentUser.enterprise_id === enterprise.id
      ) {
        // 用户不需要更新
        return currentUser;
      }

      // 更新用户信息
      delete userInfo.password;
      const { username, ...updateInfo } = userInfo;
      await User.update(updateInfo, {
        where: {
          username,
        },
      });
      return { id: currentUser.id, ...userInfo };
    }

    // 用户不存在，创建
    const newUser = await User.create(userInfo);
    await newUser.$add('role', role);
    return newUser;
  }
}
