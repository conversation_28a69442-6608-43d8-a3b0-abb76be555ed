import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ComplianceDetectionDesignDetail } from '.';

export interface ComplianceDetectionDesignQuestionAttributes {
  /** 主键 */
  id: number;
  /** 达标检测设计详情id */
  designDetail_id: number;
  /** 试题id */
  question_id: string;
  /** 排序 */
  sort_order: number;
  /** 试题来源表 */
  source_table: string;
  designDetail?: ComplianceDetectionDesignDetail;
}

@Table({
  tableName: 'compliance_detection_design_question',
  timestamps: true,
  comment: '达标检测设计题目表',
})
export class ComplianceDetectionDesignQuestion
  extends Model<ComplianceDetectionDesignQuestionAttributes>
  implements ComplianceDetectionDesignQuestionAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @ForeignKey(() => ComplianceDetectionDesignDetail)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '达标检测设计详情id',
  })
  designDetail_id: number;

  @BelongsTo(() => ComplianceDetectionDesignDetail, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  designDetail?: ComplianceDetectionDesignDetail;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '试题id',
  })
  question_id: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '排序',
  })
  sort_order: number;

  @Column({
    type: DataType.ENUM('system', 'school', 'personal'),
    allowNull: true,
    comment: '试题来源表',
  })
  source_table: string;
}
