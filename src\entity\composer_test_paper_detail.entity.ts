import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ComposerTestPaper } from './composer_test_paper.entity';

export interface ComposerTestPaperDetailAttributes {
  /** 主键ID */
  id: number;
  /** 名称 */
  name?: string;
  /** 试卷ID */
  composerTestPaperId: number;
  /** 分数 */
  score: number;
  /** 时长 */
  duration: number;
  /** 试题ID */
  questionBankId?: string;
  /** 顺序 */
  orderIndex: number;
  /** 试题来源类型 */
  sourceTable: string;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({
  tableName: 'composer_test_paper_detail',
  timestamps: true,
  comment: '试卷详情表',
  indexes: [
    {
      fields: ['composer_test_paper_id'],
    },
  ],
})
export class ComposerTestPaperDetail
  extends Model<ComposerTestPaperDetailAttributes>
  implements ComposerTestPaperDetailAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
    field: 'id',
  })
  id: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '名称',
    field: 'name',
  })
  name?: string;

  @ForeignKey(() => ComposerTestPaper)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '试卷ID',
    field: 'composer_test_paper_id',
  })
  composerTestPaperId: number;

  @BelongsTo(() => ComposerTestPaper)
  composerTestPaper?: ComposerTestPaper;
  @Column({
    type: DataType.FLOAT,
    allowNull: false,
    comment: '分数',
    field: 'score',
  })
  score: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '时长',
    field: 'duration',
  })
  duration: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '试题ID',
    field: 'question_bank_id',
  })
  questionBankId?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '顺序',
    field: 'order_index',
  })
  orderIndex: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '试题来源类型',
    field: 'source_table',
  })
  sourceTable: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '创建时间',
    field: 'created_at',
  })
  createdAt?: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '更新时间',
    field: 'updated_at',
  })
  updatedAt?: Date;
}
