import { createHash } from 'crypto';
import { existsSync, mkdirSync } from 'fs';
import { basename, extname, join } from 'path';
import { load } from 'cheerio';
import { round } from 'lodash';
export class Util {
  /**
   * 字符串转数字
   *
   * @param {*} str 字符串
   * @return {*} number

  **/
  static toInt(str) {
    if (typeof str === 'number') return str;
    if (!str) return null;
    return parseInt(str, 10) || 0;
  }
  /**
   * 根据参数生成签名
   *
   * @param {*} query 参数对象
   * @param {*} body 参数对象
   * @param {*} salt 盐
   * @return {string} 签名字符串
   **/
  static getSign(query, body, salt) {
    let queStr = '';
    if (query) {
      queStr = Object.keys(query)
        .filter(key => query[key])
        .sort()
        .map(key => `${key}=${query[key]}`)
        .join('&');
    }
    let resStr = '';
    if (body) {
      resStr = Object.keys(body)
        .filter(key => body[key])
        .sort()
        .map(key => `${key}=${body[key]}`)
        .join('&');
    }
    const content = `${queStr}&${resStr}&${salt}`;
    const signStr = createHash('md5')
      .update(content)
      .digest('hex')
      .toUpperCase();
    return signStr;
  }

  /**
   * 对象数组根据key去重
   *
   * @param {*} array 对象数组
   * @param {*} key key
   * @return {*} number
   **/
  static uniqueArray(array, key) {
    if (array.length) {
      const result = [array[0]];
      for (let i = 1; i < array.length; i++) {
        const item = array[i];
        let repeat = false;
        for (let j = 0; j < result.length; j++) {
          if (item[key] === result[j][key]) {
            repeat = true;
            break;
          }
        }
        if (!repeat) {
          result.push(item);
        }
      }
      return result;
    }
    return [];
  }

  static getNum() {
    const n = Math.random().toFixed(2).split('.')[1].toString();
    if (n === '00') {
      return this.getNum();
    }
    return n;
  }

  /**
   * 判断一维数组内容是否相等
   *
   * @param {*} arr1
   * @param {*} arr2
   * @return {*} boolean
   */
  static arraysEqual(arr1, arr2) {
    const sortedArr1 = arr1.sort();
    const sortedArr2 = arr2.sort();
    return JSON.stringify(sortedArr1) === JSON.stringify(sortedArr2);
  }

  /**
   * 获取填写内容是否正确
   *
   * @param {*} content 填写内容
   * @param {*} type 单选 多选
   * @param {*} practive 答案
   * @return {*} 0 1
   */
  static getIsCorrect(content, type, practive) {
    let isCorrect = 0;
    if (content) {
      switch (type) {
        case '单选':
          isCorrect = content === practive ? 1 : 0;
          break;
        case '多选':
          {
            const newcontent = content.split(',');
            const newpractive = practive.split(',');
            isCorrect = this.arraysEqual(newcontent, newpractive) ? 1 : 0;
          }
          break;
        default:
          break;
      }
    }
    return isCorrect;
  }

  /**
   * 根据年月范围计算开始时间和结束时间
   *
   * @param {*} year
   * @param {*} startMonth
   * @param {*} endMonth
   * @return {*}  {}
   */
  static getStartAndEndTime(year, startMonth = 1, endMonth = 12) {
    // 使用下个月的第0天，即当前月的最后一天
    const date = new Date(year, endMonth, 0);
    const day = date.getDate();
    return {
      startDate: `${year}-${startMonth.toString().padStart(2, '0')}-01`,
      endDate: `${year}-${endMonth.toString().padStart(2, '0')}-${day
        .toString()
        .padStart(2, '0')}`,
    };
  }

  /**
   * 组装附件属性
   *
   * @param {*} fullname 文件名或url
   * @param {*} attachment 附件记录对象
   * @param {*} config 全局配置信息
   * @param {string} [subPath=''] 子路径，可选
   */
  static async initAttachmentInfo(fullname, attachment, config, subPath = '/') {
    if (subPath?.includes('..')) {
      // 不允许通过../对目录进行遍历
      throw new Error('上传路径非法');
    }
    // 文件扩展名称
    const extnameRes = extname(fullname).toLowerCase();
    // 文件名称
    const filename = basename(fullname, extnameRes);
    // 预览地址
    let baseUrl = config.uploadBaseUrl || '/uploads';
    if (baseUrl.endsWith('/')) {
      baseUrl = baseUrl.substring(0, baseUrl.length - 1);
    }
    let realSubPath = subPath;
    if (subPath) {
      if (!realSubPath.startsWith('/')) {
        realSubPath = '/' + realSubPath;
      }
      if (!realSubPath.endsWith('/')) {
        realSubPath += '/';
      }
    }
    const realBaseUrl = `${baseUrl}${realSubPath}`;
    // 上传位置
    const basePath = config.uploadBaseDir;
    const targetPath = join(basePath, realSubPath);
    if (!existsSync(targetPath)) {
      mkdirSync(targetPath, { recursive: true });
    }

    // 组装参数 model
    attachment.extname = extname;
    attachment.filename = filename;
    attachment.url = encodeURI(`${realBaseUrl}${filename}${extname}`);
    attachment.path = join(targetPath, encodeURI(`${filename}${extname}`));
    attachment.subPath = realSubPath;
  }

  /**
   * 获取组合['a', 'b', 'c'] 时，它将输出 [['a','b'],['a','c'],['b','c'],['a'],['b'],['c']]
   * @param {*} arr
   * @return [['a','b'],['a','c'],['b','c'],['a'],['b'],['c']]
   */
  static getSplitZsd(arr) {
    const result = [];

    function fun(subarray, start) {
      result.push(subarray);

      for (let i = start; i < arr.length; i++) {
        fun(subarray.concat(arr[i]), i + 1);
      }
    }

    fun([], 0);

    return result.filter(
      item => item.length !== arr.length && item.length !== 0
    );
  }

  static addStyleForHtmlStr(htmlStr) {
    const $ = load(htmlStr);
    $('p').css({
      'word-break': 'break-all',
      'word-wrap': 'break-word',
      'white-space': 'break-spaces',
    });
    return $('body').html();
  }
  static toHtml(list) {
    const l = [];
    for (let index = 0; index < list.length; index++) {
      const item = list[index];
      const html =
        '<div  class="tm" style="line-height: 2.75"><style> .dotted-underline {position: relative;} .dotted-underline::after{position: absolute; bottom: -5px; /* 调整点的位置 */left: 50%; /* 居中点 */transform: translateX(-33%); /* 居中点 */width: 4px;height: 4px;border-radius: 50%; /* 创建圆点 */background-color: #333; /* 圆点颜色 */ content:""; }</style><div class="additional">';
      let str = `<div class="tm" style="line-height: 2.75"><style> .dotted-underline {position: relative;}.dotted-underline::after {position: absolute;bottom: -5px; /* 调整点的位置 */left: 50%; /* 居中点 */transform: translateX(-33%); /* 居中点 */width: 4px;height: 4px;border-radius: 50%; /* 创建圆点 */background-color: #333; /* 圆点颜色 */ content:""; }</style><div style="color: #888">${
        item.baseType || ''
      }<div>`;

      let answer = html; //答案
      let pointstr = html; //知识点
      let analysis = html; //解析
      const ids = [];
      for (let j = 0; j < item.arr.length; j++) {
        const aitem = item.arr[j];
        const point = [];
        ids.push(aitem._id);
        for (let k = 0; k < aitem.points.length; k++) {
          const z = aitem.points[k];
          point.push(z.value);
        }
        const ponits = point.join('@');

        pointstr =
          pointstr +
          `<div class="zsd"><div style="vertical-align: middle">${
            j + 1
          } ${ponits}</div></div>`;
        analysis =
          analysis +
          `<div style="vertical-align: middle">(${j + 1}) ${
            aitem.analysis || ''
          }</div>`;
        answer =
          answer +
          `<div class="da"><div style="vertical-align: middle; font-family: Times New Roman; color: #000000">(${
            j + 1
          }) ${aitem.answer}</div></div>`;
        const hasImg = aitem.name.includes('<img');
        // 去掉新编辑器中录题会产生的换行符\n
        // let questionName = aitem.name.replace(/\n/g, '');
        let questionName = this.changeName(aitem);
        if (hasImg) {
          const $ = load(aitem.name);
          const temp = $('img').parent();
          temp.css('max-width', '100%');
          temp.css('overflow', 'auto');
          if (aitem.name.includes('<strong')) {
            const img = $('img');
            const imgCss =
              temp.prop('nodeName') &&
              temp.prop('nodeName').toLocaleLowerCase() === 'strong'
                ? {
                    'vertical-align': 'middle',
                    height: '15vh',
                  }
                : {
                    'vertical-align': 'middle',
                    height: '25vh',
                  };
            img.css(imgCss);
          }
          questionName = $('body').html();
        }
        str = str + `<div><div></div>${questionName}</div>`;
      }
      str = this.addStyleForHtmlStr(str + '</div>');
      answer = this.addStyleForHtmlStr(answer + '</div></div>');
      pointstr = this.addStyleForHtmlStr(pointstr + '<div></div>');
      l.push({
        name: str,
        answer,
        points: pointstr,
        analysis: this.addStyleForHtmlStr(analysis),
        id: ids.join(','),
      });
    }
    return l;
  }

  /**
   * 拼接新编辑录入的试题题干和选项分开
   * @param {*} item 题目详情
   * @return 拼接好题干和选项的题目名
   */
  static changeName(item) {
    const optionArr = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'];
    // 定义字符串，存放转换后的选项
    let formatString = '';
    if (item.isOptions && item.topicOptions.length > 0) {
      item.topicOptions.forEach((option, index) => {
        formatString += `<div style = "display: flex; align-items: center; margin: 5px 0">${optionArr[index]}.${option.content}</div>`;
      });
    }
    item.name = `${item.name}${formatString}`;
    return item.name;
  }

  static replaceDTQ(src) {
    const $ = load(src);
    $('p:contains("【答题区】")').addClass('dtq');
    return $('body').html();
  }

  /**
   * 获取指定父节点的所有子节点
   * @param {{id:string}} tree 需要查找的树的节点 必须有Id
   * @param {Array<{id:string,pid?:string, [key:string]:any}>} list 需要查找的全部数据
   * @param {string} [name=pid] - 需要对应Id 的字段
   */
  static getTree(tree, list, name = 'pid') {
    const newChildren = list.filter(v => v[name] === tree.id);
    if (newChildren.length) {
      for (let index = 0; index < newChildren.length; index++) {
        const item = newChildren[index];

        this.getTree(item, list, name);
      }
      tree.children = newChildren;
    }
  }
  static getZsdFromMap(zsd) {
    switch (zsd) {
      case '补充成语':
        return ['成语'];
      case '成语运用':
        return ['成语'];
      case '遣词造句':
        return ['词汇运用'];
      case 'AABB式词语 ':
        return ['词形', '成语'];
      case 'AABC式词语':
        return ['词形', '成语'];
      case 'ABAB式词语':
        return ['词形', '成语'];
      case 'ABAC式词语':
        return ['词形', '成语'];
      case 'AAB式词语':
        return ['词形', '成语'];
      case 'ABB式词语':
        return ['词形', '成语'];
      case 'ABCC式词语':
        return ['词形', '成语'];
      case '词形辨析':
        return ['词形'];
      case '词语含义的理解':
        return ['词义'];
      case '反义词':
        return ['词义'];
      case '近义词':
        return ['词义'];
      case '写人类记叙文阅读':
        return ['记叙文阅读'];
      case '叙事类记叙文阅读':
        return ['记叙文阅读'];
      case '仿写':
        return ['句子排序、仿写'];
      case '变读音与重读音':
        return ['拼音'];
      case '汉字查字典':
        return ['拼音', '字形', '字义'];
      case '看拼音写汉字':
        return ['拼音', '字形'];
      case '音节':
        return ['拼音'];
      case '字音':
        return ['拼音'];
      case '古诗词填空':
        return ['诗歌鉴赏', '诗'];
      case '古诗词运用':
        return ['诗歌鉴赏', '诗'];
      case '拟人修辞手法':
        return ['修辞手法'];
      case '修辞手法辨析':
        return ['修辞手法'];
      case '其他语言表达':
        return ['语言表达'];
      case '汉字书写':
        return ['字形', '字形', '字义'];
      case '字形辨析':
        return ['字形'];
      default:
        return [zsd];
    }
  }
  /**
   * 从知识点缓存树中找到指定信息
   *
   * @param {object} zsdTree 知识点缓存树
   * @param {string} zsd 知识点名称
   * @return {object} 找到的信息
   * @memberof UploaderController
   */
  static getZsdInfo(zsdTree, zsd) {
    if (!zsd) {
      return {
        title: '',
        value: '',
      };
    }
    const info = zsdTree.children.find(c => c.title === zsd);
    if (info) {
      return info;
    }
    for (const c of zsdTree.children) {
      if (c.children && c.children.length > 0) {
        const res = this.getZsdInfo(c, zsd);
        if (res) {
          return res;
        }
      }
    }

    return null;
  }

  /**
   * 字符串转boolean
   *
   * @param {*} str 字符串
   * @return {*} boolean
   */
  static toBoolean(str) {
    if (typeof str === 'string') {
      switch (str) {
        case 'true':
          return true;
        case 'false':
        default:
          return false;
      }
    }
    if (typeof str === 'number') {
      return !!str;
    }
    return undefined;
  }

  /**
   * 动态获取题库表
   * @param {string} sectionStatus 章节状态
   * @param {string} type 小节类型（校本作业，范本作业） school | textbook
   * @param {*} model 数据库模型
   * @return {* } sectionStatus为草稿时返回TempMgQuestion，否则返回MgQuestion
   */
  static getQuesTableName(sectionStatus: string, type: string): string {
    if (type && type === 'school') return 'schoolQuestions';
    return !sectionStatus || sectionStatus === '已发布'
      ? 'systemQuestions'
      : 'tempMgQuestions';
  }
  /**
   * 计算阶乘
   * @param n
   * @returns
   */
  static factorial(n: bigint): bigint {
    let result = BigInt(1);
    for (let i = BigInt(2); i <= BigInt(n); i++) {
      result *= i;
    }
    return result || BigInt(1);
  }
  /**
   * 计算组合数
   * @param m
   * @param n
   * @returns
   */
  static combination(m: number, n: number): bigint {
    return (
      this.factorial(BigInt(m)) /
      (this.factorial(BigInt(n)) * this.factorial(BigInt(m - n)))
    );
  }
  /**
   * 从随机化的索引数组中截取前 num 个索引，并获取对应的值
   *
   * @static
   * @param {string[]} originalArray
   * @param {number} num
   * @return {*} res
   * @memberof Util
   */
  static getRandomSubset(originalArray: string[], num: number) {
    // 创建一个从 0 到 ids.length - 1 的索引数组
    const indices = Array.from({ length: originalArray.length }, (_, i) => i);
    // 使用 Fisher-Yates 洗牌算法随机化索引数组
    for (let i = originalArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [indices[i], indices[j]] = [indices[j], indices[i]];
    }
    // 从随机化的索引数组中截取前 num 个索引，并获取对应的值
    return indices.slice(0, num).map(index => originalArray[index]);
  }

  /**
   * 计算时间间隔
   * @param startTime 开始时间
   * @returns {number}
   */
  static getDuration(startTime: number): number {
    const duration = round((Date.now() - startTime) / 1000, 2);
    return duration;
  }

  /**
   * 构建树级结构
   * @param array
   * @param key
   * @returns
   */
  static buildTree(array: any, key: string, sortKey = 'id') {
    // 创建一个映射，以ID为键，元素为值
    const elementMap = new Map();
    array.forEach(item => {
      elementMap.set(item.id, item);
    });

    // 创建根节点列表
    const rootNodes = [];

    array.forEach(item => {
      // 如果元素的父节点存在，则将其添加为子节点
      if (item[key] && elementMap.has(item[key])) {
        const parent = elementMap.get(item[key]);
        if (!parent.children) parent.children = [];
        parent.children.push(item);
      } else {
        // 否则，将其视为根节点
        rootNodes.push(item);
      }
    });

    // 遍历根节点，递归构建子树
    rootNodes.forEach(root => {
      this.buildChildren(root, elementMap, sortKey);
    });

    return rootNodes;
  }

  // 递归构建子树的辅助函数
  static buildChildren(node, elementMap, sortKey) {
    if (node.children) {
      node.children.sort((a, b) =>
        `${a[sortKey]}`.localeCompare(`${b[sortKey]}`)
      ); // 可选：按名字排序
      node.children.forEach(child => {
        this.buildChildren(child, elementMap, sortKey);
      });
    }
  }

  /**
   * 去重一维数组
   * @param arr
   * @returns
   */
  static uniqArray(arr: any[]) {
    return [...new Set(arr)];
  }

  // 数字转中文函数（支持1-99）
  static getChineseNumber(num) {
    const digits = [
      '',
      '一',
      '二',
      '三',
      '四',
      '五',
      '六',
      '七',
      '八',
      '九',
      '十',
    ];
    if (num <= 10) {
      return digits[num]; // 直接返回1-10对应的中文
    } else if (num < 20) {
      return `十${digits[num - 10]}`; // 处理11-19（十加个位）
    } else {
      const ten = Math.floor(num / 10); // 十位数
      const remainder = num % 10; // 个位数
      return `${digits[ten]}十${remainder === 0 ? '' : digits[remainder]}`; // 二十及以上
    }
  }
}
