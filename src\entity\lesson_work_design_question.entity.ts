import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { LessonWorkDesignDetail } from '.';

export interface LessonWorkDesignQuestionAttributes {
  /** 主键 */
  id: number;
  /** 课时作业设计详情id */
  designDetail_id: number;
  /** 题目id */
  question_id: string;
  /** 排序 */
  sort_order: number;
  /** 试题来源表 */
  source_table?: string;
  designDetail?: LessonWorkDesignDetail;
}

@Table({
  tableName: 'lesson_work_design_question',
  timestamps: true,
  comment: '课时作业设计题目表',
})
export class LessonWorkDesignQuestion
  extends Model<LessonWorkDesignQuestionAttributes>
  implements LessonWorkDesignQuestionAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @ForeignKey(() => LessonWorkDesignDetail)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '课时作业设计详情id',
  })
  designDetail_id: number;

  @BelongsTo(() => LessonWorkDesignDetail, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  designDetail?: LessonWorkDesignDetail;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '题目id',
  })
  question_id: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '排序',
  })
  sort_order: number;

  @Column({
    type: DataType.ENUM('system', 'school', 'personal'),
    allowNull: true,
    comment: '试题来源表',
  })
  source_table?: string;
}
