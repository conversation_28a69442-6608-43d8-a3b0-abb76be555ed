import { Inject, Provide } from '@midwayjs/core';
import { PlanCourseSubject } from '../entity/plan_course_subject.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class PlanCourseSubjectService extends BaseService<PlanCourseSubject> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课程科目');
  }
  getModel = () => {
    return PlanCourseSubject;
  };
}
