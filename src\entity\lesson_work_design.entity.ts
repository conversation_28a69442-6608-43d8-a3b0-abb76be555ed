import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ClassWork, Enterprise, Subject, Textbook, TextbookChecklist } from '.';

export interface LessonWorkDesignAttributes {
  /** 主键 */
  id: number;
  /** 教材版本id */
  textbook_id: number;
  /** 教材名录ID */
  textbookChecklist_id: number;
  /** 课时作业名称 */
  name: string;
  /** 学段code */
  grade_section_code: string;
  /** 学段 */
  grade_section_name: string;
  /** 学科id */
  subject_id: number;
  /** 学科 */
  subject_name: string;
  /** 年级code */
  grade_code: string;
  /** 年级 */
  grade_name: string;
  /** 教材版本 */
  textbook_version: string;
  /** 册次 */
  volume: string;
  /** 来源code */
  source_code?: string;
  /** 来源 */
  source?: string;
  /** 课时范本id */
  class_work_id?: number;
  /** 企业id */
  enterprise_id: number;
  /** 状态 */
  status: string;
  /** 创建人id */
  creator_id?: number;
  /** 创建人 */
  creator_name?: string;
  textbook?: Textbook;
  textbookChecklist?: TextbookChecklist;
  subject?: Subject;
  classWork?: ClassWork;
  enterprise?: Enterprise;
}

@Table({
  tableName: 'lesson_work_design',
  timestamps: true,
  comment: '课时作业设计表',
})
export class LessonWorkDesign
  extends Model<LessonWorkDesignAttributes>
  implements LessonWorkDesignAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @ForeignKey(() => Textbook)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材版本id',
  })
  textbook_id: number;

  @BelongsTo(() => Textbook)
  textbook?: Textbook;

  @ForeignKey(() => TextbookChecklist)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材名录ID',
  })
  textbookChecklist_id: number;

  @BelongsTo(() => TextbookChecklist)
  textbookChecklist?: TextbookChecklist;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '课时作业名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '学段code',
  })
  grade_section_code: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '学段',
  })
  grade_section_name: string;

  @ForeignKey(() => Subject)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '学科id',
  })
  subject_id: number;

  @BelongsTo(() => Subject, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  subject?: Subject;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '学科',
  })
  subject_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '年级code',
  })
  grade_code: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '年级',
  })
  grade_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '教材版本',
  })
  textbook_version: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '册次',
  })
  volume: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '来源code',
  })
  source_code: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '来源',
  })
  source: string;

  @ForeignKey(() => ClassWork)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '课时范本id',
  })
  class_work_id?: number;

  @BelongsTo(() => ClassWork)
  classWork?: ClassWork;

  @ForeignKey(() => Enterprise)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '企业id',
  })
  enterprise_id: number;

  @Column({
    type: DataType.ENUM('草稿', '发布'),
    allowNull: false,
    comment: '状态',
  })
  status: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '创建人id',
  })
  creator_id?: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '创建人',
  })
  creator_name?: string;

  @BelongsTo(() => Enterprise)
  enterprise?: Enterprise;
}
