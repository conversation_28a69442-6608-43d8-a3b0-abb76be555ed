import { Table, Column, Model, DataType } from 'sequelize-typescript';

export interface AnalysisTemplateShareRecordAttributes {
  /** 主键ID */
  id: number;
  /** 学期编码 */
  semester_code?: string;
  /** 学期名称 */
  semester_name?: string;
  /** 省份编码 */
  province_code?: string;
  /** 省份名称 */
  province_name?: string;
  /** 市编码 */
  city_code?: string;
  /** 市名称 */
  city_name?: string;
  /** 区编码 */
  area_code?: string;
  /** 区名称 */
  area_name?: string;
  /** 学制编码 */
  school_system_code?: string;
  /** 学制名称 */
  school_system_name?: string;
  /** 学段编码 */
  grade_section_code?: string;
  /** 学段名称 */
  grade_section_name?: string;
  /** 企业编码 */
  enterprise_code?: string;
  /** 企业名称 */
  enterprise_name?: string;
  /** 范本引用次数 */
  template_cite_number?: number;
}

@Table({
  tableName: 'analysis_template_share_record',
  timestamps: true,
  comment: '范本共享记录表',
})
export class AnalysisTemplateShareRecord
  extends Model<AnalysisTemplateShareRecordAttributes>
  implements AnalysisTemplateShareRecordAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '学期编码',
  })
  semester_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '学期名称',
  })
  semester_name?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '省份编码',
  })
  province_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '省份名称',
  })
  province_name?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '市编码',
  })
  city_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '市名称',
  })
  city_name?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '区编码',
  })
  area_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '区名称',
  })
  area_name?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学制编码',
  })
  school_system_code?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学制名称',
  })
  school_system_name?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学段编码',
  })
  grade_section_code?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学段名称',
  })
  grade_section_name?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '企业编码',
  })
  enterprise_code?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '企业名称',
  })
  enterprise_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '范本引用次数',
  })
  template_cite_number?: number;
}
