/** 枚举值定义 */

/** 试题难度 */
export enum QUESTION_DIFFICULTY {
  BASE = '基础题',
  MIDDLE = '中档题',
  HIGH = '较难题',
}

/** 组卷方式 */
export enum COMPOSER_TYPE {
  AUTO = '自动',
  MANUAL = '手工',
}

/** 审核状态 */
export enum CHECK_STATUS {
  DEFAULT = '待审核',
  PASSED = '已通过',
  REJECTED = '已拒绝',
}

/**
 * 检测类型
 */
export enum DETECTION_TYPE {
  UNIT = '单元',
  INTERIM = '期中',
  FINAL = '期末',
}

/**
 * mongodb的模型枚举
 */
export enum MONGO_MODEL_KEY {
  SYSTEM = 'system', // 系统题库
  PERSONAL = 'personal', // 个人题库
  SCHOOL = 'school', // 学校题库
}

/**
 * 创建类型
 */
export enum CREATE_TYPE {
  CUSTOM = '自定义',
  BUILTIN = '内置',
}

/**
 * 范本状态
 */
export enum TEMPLATE_STATUS {
  DRAFT = '草稿',
  PUBLISHED = '发布',
}

// types/mapping.ts
export const enum MAPPING_TYPE {
  NULL = 'null',
  ID = 'id',
  CODE = 'code',
  ARRAY = 'array',
  BOOLEAN = 'boolean',
  IN = 'in',
  REGEX = 'regex',
}

export type TransformMappingType = {
  getKey: (key: string) => string;
  transform: (value: string) => any;
};

export type QuestionFilterFieldMapping = Record<string, MAPPING_TYPE>;
// 查询条件映射类型
export const TRANSFORM_MAPPING_TYPE: Record<
  MAPPING_TYPE,
  TransformMappingType
> = {
  [MAPPING_TYPE.CODE]: {
    getKey: key => `${key}.${MAPPING_TYPE.CODE}`,
    transform: value => value,
  },
  [MAPPING_TYPE.ID]: {
    getKey: key => `${key}.${MAPPING_TYPE.ID}`,
    transform: value => Number(value),
  },
  [MAPPING_TYPE.ARRAY]: {
    getKey: key => `${key}.${MAPPING_TYPE.ID}`,
    transform: value => ({
      $all: value.split(',').map((v: string) => Number(v.trim())),
    }),
  },
  [MAPPING_TYPE.NULL]: {
    getKey: key => key,
    transform: value => value,
  },
  [MAPPING_TYPE.BOOLEAN]: {
    getKey: key => key,
    transform: value => value === 'true',
  },
  [MAPPING_TYPE.IN]: {
    getKey: key => key,
    transform: value => ({ $in: value.split(',') }),
  },
  [MAPPING_TYPE.REGEX]: {
    getKey: key => key,
    transform: value => ({ $regex: value }), // 当前版本不支持中文分词，后续版本升级
  },
} as const;

// 定义试题中对象属性的查询条件映射
export const QUESTION_FILTER_FIELD_MAPPING: Record<string, MAPPING_TYPE> = {
  // 对应转换为code查询条件
  difficulty: MAPPING_TYPE.CODE, // 试题难度
  grade: MAPPING_TYPE.CODE, // 年级
  gradeSection: MAPPING_TYPE.CODE, // 学段
  baseType: MAPPING_TYPE.CODE, // 基础题型
  type: MAPPING_TYPE.CODE, // 扩展题型
  source: MAPPING_TYPE.CODE, // 来源
  // 对应转换为id查询条件
  textbookVersion: MAPPING_TYPE.ID, // 教材版本
  subject: MAPPING_TYPE.ID, // 学科
  unit: MAPPING_TYPE.ID, // 单元
  period: MAPPING_TYPE.ID, // 课时
  volume: MAPPING_TYPE.ID, // 册次
  author: MAPPING_TYPE.ID, // 作者
  classification: MAPPING_TYPE.ID, // 分类
  tier: MAPPING_TYPE.ID, // 分层
  cognitiveHierarchy: MAPPING_TYPE.ID, // 认知层次
  coreQuality: MAPPING_TYPE.ID, // 核心素养
  investigationAbility: MAPPING_TYPE.ID, // 考察能力
  // 对应转换为array全匹配查询条件
  points: MAPPING_TYPE.ARRAY, // 知识点
  tags: MAPPING_TYPE.ARRAY, // 标签
  // 数组类型单个匹配查询条件
  checkStatus: MAPPING_TYPE.IN,
  // 设置boolean类型查询条件
  isShared: MAPPING_TYPE.BOOLEAN, // 是否共享
  // 设置模糊查询条件
  stem: MAPPING_TYPE.REGEX, // 题干
};

export enum SUBJECT_DICTIONARY_TYPE {
  CORE_QUALITY = '核心素养', // 核心素养
  INVESTIGATION_ABILITY = '考察能力', // 考察能力
  COGNITIVE_HIERARCHY = '认知层次', // 认知层次
}

export const SCORE_WEIGHTS = {
  point: 0.5,
  type: 0.2,
  difficulty: 0.2,
  cognitiveHierarchy: 0.1,
};

// 学制学段映射
export const SCHOOL_SYSTEM_TO_GRADE_SECTION_MAP: Record<
  string,
  { code: string; name: string }[]
> = {
  PRIMARY: [{ code: 'GRADE_PRIMARY', name: '小学' }], // 小学
  MIDDLE: [{ code: 'GRADE_MIDDLE', name: '初中' }], // 初中
  HIGH: [{ code: 'GRADE_HIGH', name: '高中' }], // 高中
  YEAR9: [
    { code: 'GRADE_PRIMARY', name: '小学' },
    { code: 'GRADE_MIDDLE', name: '初中' },
  ], //九年一贯制
  YEAR12: [
    { code: 'GRADE_PRIMARY', name: '小学' },
    { code: 'GRADE_MIDDLE', name: '初中' },
    { code: 'GRADE_HIGH', name: '高中' },
  ], // 12年一贯制
  YEAR6: [
    { code: 'GRADE_MIDDLE', name: '初中' },
    { code: 'GRADE_HIGH', name: '高中' },
  ], // 完全中学
};

// 学段年级映射
export const GRADE_SECTION_TO_GRADE_MAP = {
  GRADE_PRIMARY: [
    { code: '1', name: '一年级' },
    { code: '2', name: '二年级' },
    { code: '3', name: '三年级' },
    { code: '4', name: '四年级' },
    { code: '5', name: '五年级' },
    { code: '6', name: '六年级' },
  ],
  GRADE_MIDDLE: [
    { code: '7', name: '初一' },
    { code: '8', name: '初二' },
    { code: '9', name: '初三' },
  ],
  GRADE_HIGH: [
    { code: '10', name: '高一' },
    { code: '11', name: '高二' },
    { code: '12', name: '高三' },
  ],
};
