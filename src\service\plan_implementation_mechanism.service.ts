import { Inject, Provide } from '@midwayjs/core';
import { PlanImplementationMechanism } from '../entity/plan_implementation_mechanism.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class PlanImplementationMechanismService extends BaseService<PlanImplementationMechanism> {
  @Inject()
  ctx: Context;

  constructor() {
    super('');
  }
  getModel = () => {
    return PlanImplementationMechanism;
  };
}
