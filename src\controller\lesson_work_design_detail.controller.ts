import {
  Body,
  Controller,
  Del,
  <PERSON>,
  Files,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { LessonWorkDesignDetailService } from '../service/lesson_work_design_detail.service';
import { CustomError } from '../error/custom.error';
import { LessonWorkDesignQuestion, LessonWorkDesignStruct } from '../entity';
import { LessonWorkDesignService } from '../service/lesson_work_design.service';
import { UploadFileInfo, UploadMiddleware } from '@midwayjs/busboy';

@Controller('/lesson_work_design_detail')
export class LessonWorkDesignDetailController {
  @Inject()
  ctx: Context;

  @Inject()
  service: LessonWorkDesignDetailService;

  @Inject()
  lessonWorkDesignService: LessonWorkDesignService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      include: [
        { model: LessonWorkDesignQuestion, order: [['sort_order', 'ASC']] },
        { model: LessonWorkDesignStruct },
      ],
    });
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: string) {
    const res = await this.service.show(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() body: any) {
    const { design_id, catalog_id } = body;
    if (!design_id) {
      throw new CustomError('缺少必要参数');
    }
    if (!catalog_id) {
      throw new CustomError('缺少必要参数');
    }
    const res = await this.service.create(body);
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Post('/import', { middleware: [UploadMiddleware], summary: '导入范本作业' })
  async importLessonWorkDetails(
    @Files() files: Array<UploadFileInfo>,
    @Fields() fieldRecord: Record<string, string>
  ) {
    // 1. 验证用户登录信息
    const user = this.ctx.state?.user;
    if (!user) {
      throw new CustomError('缺少用户登录信息！');
    }
    if (!files.length) {
      throw new CustomError('请先选择文件！');
    }
    const file = files[0];
    const { fields } = fieldRecord;
    if (!fields) {
      throw new CustomError('缺少必要参数！');
    }
    const info = JSON.parse(fields);
    const { name, outterId, newId, ...questionInfo } = info;
    if (!name) {
      throw new CustomError('缺少课时作业名称！');
    }
    if (!outterId) {
      throw new CustomError('缺少课时作业ID！');
    }
    if (!questionInfo.tableName) {
      throw new CustomError('未指定试题表！');
    }
    if (!questionInfo.subject) {
      throw new CustomError('未指定学科！');
    }
    // 导入默认是草稿
    const lessonWorkInfo = {
      design_id: outterId,
      name,
      status: '草稿',
      catalog_id: newId,
    };
    return await this.service.importLessonWorkDetails(
      file.data,
      questionInfo,
      lessonWorkInfo
    );
  }
}
