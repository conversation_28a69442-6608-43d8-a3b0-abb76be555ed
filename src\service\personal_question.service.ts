import { Inject, Provide, Logger } from '@midwayjs/core';
import { PersonalQuestions } from '../model';
import { Context } from '@midwayjs/koa';
import { InjectEntityModel } from '@midwayjs/typegoose';
import { ReturnModelType } from '@typegoose/typegoose';
import { assign, omit, pick } from 'lodash';
import { Types } from 'mongoose';
import { QuestionService } from './question.service';
import { MONGO_MODEL_KEY } from '../common/Constants';
@Provide()
export class PersonalQuestionService {
  @Inject()
  ctx: Context;

  @Logger()
  logger;

  @Inject()
  questionService: QuestionService;

  @InjectEntityModel(PersonalQuestions)
  personalQuestions: ReturnModelType<typeof PersonalQuestions>;

  /**
   * 查询列表
   * @param {*} queryObj 查询条件
   * @param {*} current 当前页码
   * @param {*} pageSize 每页条数
   * @memberof PersonalQuestionService
   */
  async index(queryObj, current, pageSize, order = { createdAt: -1 }) {
    return await this.questionService.findQuestionsByFilter(
      queryObj,
      current,
      pageSize,
      order,
      MONGO_MODEL_KEY.PERSONAL
    );
  }

  /**
   * 查询单个
   * @param {*} id
   * @memberof PersonalQuestionService
   */
  async show(id) {
    return await this.questionService.findById(id, MONGO_MODEL_KEY.PERSONAL);
  }

  /**
   * 创建
   * @param {*} info
   * @return {*}
   * @memberof PersonalQuestionService
   */
  async create(info) {
    return await this.questionService.create(info, MONGO_MODEL_KEY.PERSONAL);
  }

  /**
   * 更新试题
   * @param {*} _id 试题id
   * @param {*} info 更新对象
   * @memberof PersonalQuestionService
   */
  async update(_id: string, info) {
    return await this.questionService.update(
      _id,
      info,
      MONGO_MODEL_KEY.PERSONAL
    );
  }

  /**
   * 删除单个
   * @param {*} _id 试题id
   * @memberof PersonalQuestionService
   */
  async destroy(_id) {
    await this.questionService.destroy(_id, MONGO_MODEL_KEY.PERSONAL);
  }

  /**
   * 批量删除
   * @param {*} ids
   * @memberof PersonalQuestionService
   */
  async destroybulk(ids) {
    await this.questionService.destroyBulk(ids, MONGO_MODEL_KEY.PERSONAL);
  }

  /**
   * 批量更新
   * @param {*} ids id数组
   * @param {*} data 更新对象
   * @return {*} 更新结果
   * @memberof PersonalQuestionService
   */
  async updateBulk(ids, data) {
    const res = await this.personalQuestions
      .updateMany(
        { _id: { $in: ids.map(id => new Types.ObjectId(id)) } },
        { $set: data }
      )
      .exec();
    return res;
  }

  /**
   * 构造父子题 子题数据结构
   * @param parentInfo 父题数据
   * @param children 子题数据
   */
  async buildChildren(parentInfo, children: any[]) {
    const commonInfo = pick(parentInfo, [
      'difficulty', //难度
      'grade', //年级
      'textbookVersion', //教材版本
      'gradeSection', //学段
      'subject', //学科
      'unit', //单元
      'period', //课时
      'author', //作者
      'source', //来源
      'volume', //册次
      'tier', //层次
      'year', //年份
      'cognitiveHierarchy', //认知层次
      'coreQuality', //核心素养
      'investigationAbility', //考察能力
      'createType', //题目创建类型
      'enterpriseCode', //企业编码
      'sourceTable', //来源试题表
      'sourceId', //来源试题id
      'classification', //分类
      'referencedNum', //被引用数量
      'isShared', //是否共享
      'tableName', //试题所在表名称
    ]);
    const childQuestions = children.map(item => {
      item.pid = parentInfo._id;
      item.parentStem = parentInfo.name;
      return assign(item, commonInfo);
    });
    return childQuestions;
  }

  /**
   * 创建父子题 父子题分别为独立的题
   * @param {*} data
   * @return {*}
   * @memberof PersonalQuestionService
   */
  async createAssociateQuestion(data) {
    const { children, ...info } = data;
    const pid = new Types.ObjectId()._id;
    const parentQuestion = {
      _id: pid,
      ...info,
    };
    const childQuestions = await this.buildChildren(parentQuestion, children);
    return await this.personalQuestions.insertMany([
      parentQuestion,
      ...childQuestions,
    ]);
  }

  /**
   * 编辑父子题
   * @param {*} data 子题数据数组
   * @param {*} pid 父题id
   * @return {*}
   * @memberof PersonalQuestionService
   */
  async updateAssociateQuestion(data, pid: string) {
    const { children, ...info } = data;
    await this.personalQuestions
      .deleteMany({ pid: new Types.ObjectId(pid) })
      .exec();

    console.log(info);
    const childQuestions = await this.buildChildren(info, children);
    await this.personalQuestions.updateOne({
      _id: new Types.ObjectId(pid),
      data,
    });
    return await this.personalQuestions.insertMany(childQuestions);
  }

  /**
   * 根据父题id删除关联的子题
   * @param {*} pid
   * @memberof PersonalQuestionService
   */
  async deleteQuestionByPid(pid: string) {
    const questionId = new Types.ObjectId(pid);
    await this.personalQuestions
      .deleteMany({ $or: [{ pid: questionId }, { _id: questionId }] })
      .exec();
  }

  /**
   * 试题审核通过后推送到学校题库
   * @param questionId 试题id
   */
  async shareToSchool(questionIds: string[]) {
    // 查询出审核试题
    const questions = await this.personalQuestions
      .find({
        $or: [
          {
            _id: { $in: questionIds.map(id => new Types.ObjectId(id)) },
          },
          {
            pid: { $in: questionIds.map(id => new Types.ObjectId(id)) },
          },
        ],
      })
      .lean()
      .exec();
    // 推送到学校题库
    // 检出试题的通用数据进行数据组装 批量写入学校数据库
    const idMap = new Map();
    questions
      .filter(s => s.pid)
      .forEach(q => idMap.set(q.pid.toString(), new Types.ObjectId()));
    const insertQuestions = questions.map(q => {
      const obj: any = {
        ...omit(q, [
          '_id',
          'checkStatus',
          'isShared',
          'sourceTable',
          'sourceId',
          'tableName',
          'createdAt',
          'updatedAt',
        ]),
        sourceTable: MONGO_MODEL_KEY.PERSONAL,
        sourceId: q._id,
        tableName: MONGO_MODEL_KEY.SCHOOL,
        _id: idMap.get(q._id.toString()) || new Types.ObjectId(),
        pid: q.pid ? idMap.get(q.pid.toString()) : null,
      };
      return obj;
    });
    await this.questionService.insertMany(
      insertQuestions,
      MONGO_MODEL_KEY.SCHOOL
    );
  }

  /**
   * 根据试题id数组获取多个试题
   * @param {string[]} ids 试题id数组
   */
  async findAllByIds(ids: string[]) {
    const res = await this.questionService.findQuestionsByFilter(
      { idArray: ids },
      null,
      null,
      { commitAt: -1 },
      MONGO_MODEL_KEY.PERSONAL
    );
    return res;
  }

  /**
   * 条件更新
   * @param filter
   * @param updateData
   * @returns
   */
  async updateMany(filter: any, updateData: any) {
    const document = await this.questionService.updateMany(
      filter,
      updateData,
      MONGO_MODEL_KEY.PERSONAL
    );
    return document;
  }
}
