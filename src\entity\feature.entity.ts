import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
  Index,
} from 'sequelize-typescript';
import { Permission } from './permission.entity';

export interface FeatureAttributes {
  /** 功能编号，主键 */
  code: string;
  /** 功能名称 */
  name: string;
  /** 父级功能编号（可选） */
  parentCode?: string;
  /** 功能图标（可选） */
  icon?: string;
  /** 功能路径 */
  path: string;
  /** 排序 */
  orderIndex: number;
  /** 描述（可选） */
  description?: string;
  permissions: Permission[];
}

@Table({ tableName: 'features', timestamps: true, comment: '系统功能表' })
export class Feature
  extends Model<FeatureAttributes>
  implements FeatureAttributes
{
  @Column({
    type: DataType.STRING,
    primaryKey: true,
    comment: '功能编号',
  })
  code: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '功能名称',
  })
  @Index({ name: 'name', unique: true })
  name: string;

  @ForeignKey(() => Feature)
  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '父级功能编号',
  })
  parentCode: string;

  @BelongsTo(() => Feature, 'parentCode')
  parent: Feature;

  @HasMany(() => Feature, 'parentCode')
  children: Feature[];

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '功能图标',
  })
  icon: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '功能路径',
  })
  path: string;

  @Column({
    type: DataType.INTEGER,
    defaultValue: 0,
    comment: '功能排序',
  })
  orderIndex: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '功能描述',
  })
  description: string;

  @HasMany(() => Permission, 'featureCode')
  permissions: Permission[];
}
