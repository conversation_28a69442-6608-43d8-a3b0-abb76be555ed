import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { SimilarQuestionService } from '../service/similar_question.service';
import { CustomError } from '../error/custom.error';

@Controller('/similar_question')
export class SimilarQuestionController {
  @Inject()
  ctx: Context;

  @Inject()
  service: SimilarQuestionService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;
    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.getList(queryInfo, offset, limit);
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    if (!info.question_id) {
      throw new CustomError('请选择试题!');
    }
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: string) {
    if (!id) {
      throw new CustomError('请选择需要删除的题目!');
    }
    await this.service.delete({ id });
    return true;
  }

  @Post('/bulkcreate', { summary: '批量创建' })
  async bulkCreate(@Body() body: any) {
    const result = await this.service.bulkCreate(body);
    return result;
  }

  @Post('/set_similar_question', { summary: '设置相似题' })
  async batchSetQuestions(@Body() body: any) {
    if (!body) {
      throw new CustomError('未指定要关联的题目信息!');
    }
    const result = await this.service.batchSetQuestions(body);
    return result;
  }
}
