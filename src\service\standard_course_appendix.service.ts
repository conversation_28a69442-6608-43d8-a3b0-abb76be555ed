import { Inject, Provide } from '@midwayjs/core';
import { StandardCourseAppendix } from '../entity/standard_course_appendix.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class StandardCourseAppendixService extends BaseService<StandardCourseAppendix> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课标附录');
  }
  getModel = () => {
    return StandardCourseAppendix;
  };
}
