import { Inject, Provide } from '@midwayjs/core';
import { ComposerTestPaperStruct } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';

@Provide()
export class ComposerTestPaperStructService extends BaseService<ComposerTestPaperStruct> {
  @Inject()
  ctx: Context;

  constructor() {
    super('试卷结构');
  }
  getModel = () => {
    return ComposerTestPaperStruct;
  };

  async batchCreateStruct(
    composerTestPaperId: number,
    structs: any[],
    transaction = null
  ) {
    try {
      const infoArr = structs.map(struct => {
        return {
          ...struct,
          composerTestPaperId,
        };
      });
      // 创建小题
      await this.batchCreate(infoArr, transaction);
    } catch (error) {
      throw new CustomError(error.message);
    }
  }
}
