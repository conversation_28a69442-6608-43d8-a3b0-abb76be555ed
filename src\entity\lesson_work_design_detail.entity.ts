import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import {
  LessonWorkDesign,
  LessonWorkCatalog,
  LessonWorkDesignStruct,
  LessonWorkDesignQuestion,
} from '.';
import { TEMPLATE_STATUS } from '../common/Constants';

export interface LessonWorkDesignDetailAttributes {
  /** 主键 */
  id: number;
  /** 作业设计id */
  design_id: number;
  /** 作业设计目录id */
  catalog_id: number;
  /** 名称 */
  name?: string;
  /** 排序 */
  sort_order: number;
  /** 状态 */
  status: string;
  /** 作业时长 */
  duration?: number;
  lessonWorkDesign?: LessonWorkDesign;
  lessonWorkCatalog?: LessonWorkCatalog;
  structs?: LessonWorkDesignStruct[];
  questions?: LessonWorkDesignQuestion[];
}

@Table({
  tableName: 'lesson_work_design_detail',
  timestamps: true,
  comment: '课时作业设计详情表',
})
export class LessonWorkDesignDetail
  extends Model<LessonWorkDesignDetailAttributes>
  implements LessonWorkDesignDetailAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @ForeignKey(() => LessonWorkDesign)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '作业设计id',
  })
  design_id: number;

  @BelongsTo(() => LessonWorkDesign, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  lessonWorkDesign?: LessonWorkDesign;

  @ForeignKey(() => LessonWorkCatalog)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '作业设计目录id',
  })
  catalog_id: number;

  @BelongsTo(() => LessonWorkCatalog, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  lessonWorkCatalog?: LessonWorkCatalog;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '名称',
  })
  name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '排序',
  })
  sort_order: number;

  @Column({
    type: DataType.ENUM(TEMPLATE_STATUS.DRAFT, TEMPLATE_STATUS.PUBLISHED),
    allowNull: false,
    defaultValue: TEMPLATE_STATUS.DRAFT,
    comment: '状态',
  })
  status: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '作业时长',
  })
  duration?: number;

  @HasMany(() => LessonWorkDesignStruct)
  structs?: LessonWorkDesignStruct[];

  @HasMany(() => LessonWorkDesignQuestion)
  questions?: LessonWorkDesignQuestion[];
}
