import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';

export interface CityHomeworkCaseAttributes {
  /** 主键ID */
  id: number;
  /** 学科id */
  subject_id?: number;
  /** 学科名称 */
  subject_name?: string;
  /** 学段code */
  grade_section_code?: string;
  /** 教学阶段 */
  grade_section_name?: string;
  /** 案例名称 */
  case_name?: string;
  /** 案例类型 */
  case_type?: string;
  /** 案例示例 */
  case_example?: string;
  /** 案例分析 */
  case_analysis?: string;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({
  tableName: 'city_homework_case',
  timestamps: true,
  comment: '市级作业典型案例',
})
export class CityHomeworkCase
  extends Model<CityHomeworkCaseAttributes>
  implements CityHomeworkCaseAttributes
{
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '学科id',
  })
  subject_id?: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学科名称',
  })
  subject_name?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学段code',
  })
  grade_section_code?: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '教学阶段',
  })
  grade_section_name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '案例名称',
  })
  case_name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '案例类型',
  })
  case_type?: string;

  @Column({
    type: DataType.TEXT('long'),
    allowNull: true,
    comment: '案例示例',
  })
  case_example?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '案例分析',
  })
  case_analysis?: string;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    comment: '创建时间',
  })
  createdAt?: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    comment: '更新时间',
  })
  updatedAt?: Date;
}
