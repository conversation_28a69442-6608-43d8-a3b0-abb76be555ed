import { Inject, Provide } from '@midwayjs/core';
import {
  ComplianceDetectionTemplateDetail,
  ComplianceDetectionTemplateQuestion,
  ComplianceDetectionTemplateStruct,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';

@Provide()
export class ComplianceDetectionTemplateQuestionService extends BaseService<ComplianceDetectionTemplateQuestion> {
  @Inject()
  ctx: Context;

  constructor() {
    super('达标检测题目');
  }
  getModel = () => {
    return ComplianceDetectionTemplateQuestion;
  };

  /**
   * 批量创建
   * @param detail_id 达标检测详情id
   * @param info 数据
   */
  async bulkCreate(detail_id: number, info: any) {
    const transaction =
      await ComplianceDetectionTemplateQuestion.sequelize.transaction();
    try {
      // 先删除对应作业下之前创建的题目
      await ComplianceDetectionTemplateQuestion.destroy({
        where: { detail_id },
        transaction,
      });
      // 批量创建题目
      const data = info.map((item, index) => {
        return {
          ...item,
          sort_order: index + 1,
        };
      });
      await ComplianceDetectionTemplateQuestion.bulkCreate(data, {
        transaction,
      });
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 保存试卷
   * @param detail_id 达标检测详情id
   * @param info 题目 结构信息
   */
  async savePaper(detail_id, info) {
    const { questions, structs, ...data } = info;
    const transaction =
      await ComplianceDetectionTemplateQuestion.sequelize.transaction();
    try {
      // 更新达标检测详情
      await ComplianceDetectionTemplateDetail.update(data, {
        where: { id: detail_id },
        transaction,
      });
      // 先删除对应作业下之前创建的题目
      await ComplianceDetectionTemplateQuestion.destroy({
        where: { detail_id },
        transaction,
      });
      // 批量创建题目
      const questionArr = questions.map((item, index) => {
        return {
          ...item,
          sort_order: index + 1,
        };
      });
      await ComplianceDetectionTemplateQuestion.bulkCreate(questionArr, {
        transaction,
      });
      // 先删除对应作业下之前创建的结构
      await ComplianceDetectionTemplateStruct.destroy({
        where: { detail_id },
        transaction,
      });
      // 批量创建结构
      const structArr = structs.map((item, index) => {
        return {
          ...item,
          detail_id,
          sort_order: index + 1,
        };
      });
      await ComplianceDetectionTemplateStruct.bulkCreate(structArr, {
        transaction,
      });
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }
}
