import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import {
  ComplianceDetectionTemplate,
  ComplianceDetectionTemplateQuestion,
  ComplianceDetectionTemplateStruct,
} from '.';
import { DETECTION_TYPE, TEMPLATE_STATUS } from '../common/Constants';

export interface ComplianceDetectionTemplateDetailAttributes {
  /** 主键 */
  id: number;
  /** 达标检测范本id */
  template_id: number;
  /** 教材名录id */
  textbookChecklist_id: number;
  /** 教材目录id */
  catalogIds?: string;
  /** 教材目录 */
  catalogs?: string;
  /** 名称 */
  name?: string;
  /** 检测类型 */
  detection_type: string;
  /** 状态 */
  status: string;
  /** 创建人id */
  creator_id?: number;
  /** 创建人 */
  creator_name?: string;
  /** 作业时长 */
  duration?: number;
  complianceDetection?: ComplianceDetectionTemplate;
  questions?: ComplianceDetectionTemplateQuestion[];
  structs?: ComplianceDetectionTemplateStruct[];
}

@Table({
  tableName: 'compliance_detection_template_detail',
  timestamps: true,
  comment: '达标检测范本详情表',
})
export class ComplianceDetectionTemplateDetail
  extends Model<ComplianceDetectionTemplateDetailAttributes>
  implements ComplianceDetectionTemplateDetailAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @ForeignKey(() => ComplianceDetectionTemplate)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '达标检测范本id',
  })
  template_id: number;

  @BelongsTo(() => ComplianceDetectionTemplate, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  complianceDetection?: ComplianceDetectionTemplate;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材名录id',
  })
  textbookChecklist_id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '教材目录ids',
    get() {
      const newValue = this.getDataValue('catalogIds');
      return newValue ? newValue.split(',') : [];
    },
    set(value: string[]) {
      this.setDataValue('catalogIds', value.join(','));
    },
  })
  catalogIds: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '教材目录',
    get() {
      const newValue = this.getDataValue('catalogs');
      return newValue ? newValue.split(',') : [];
    },
    set(value: string[]) {
      this.setDataValue('catalogs', value.join(','));
    },
  })
  catalogs: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '名称',
  })
  name?: string;

  @Column({
    type: DataType.ENUM(
      DETECTION_TYPE.UNIT,
      DETECTION_TYPE.INTERIM,
      DETECTION_TYPE.FINAL
    ),
    allowNull: false,
    defaultValue: DETECTION_TYPE.UNIT,
    comment: '检测类型',
  })
  detection_type: string;

  @Column({
    type: DataType.ENUM(TEMPLATE_STATUS.DRAFT, TEMPLATE_STATUS.PUBLISHED),
    allowNull: false,
    defaultValue: TEMPLATE_STATUS.DRAFT,
    comment: '状态',
  })
  status: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '创建人id',
  })
  creator_id?: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '创建人',
  })
  creator_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '作业时长',
  })
  duration?: number;

  @HasMany(() => ComplianceDetectionTemplateQuestion)
  questions?: ComplianceDetectionTemplateQuestion[];

  @HasMany(() => ComplianceDetectionTemplateStruct)
  structs?: ComplianceDetectionTemplateStruct[];
}
