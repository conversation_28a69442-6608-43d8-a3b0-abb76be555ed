import { Body, Controller, Get, Inject, Post, Query } from '@midwayjs/core';
import { SSO_CALLBACK_REQUEST_DTO } from '../dto/auth';
import { SsoService } from '../service/sso.service';
import { SsoService as CarrySsoService } from '../service/carry/sso.service';
import { UserService } from '../service/user.service';
import { FeatureService } from '../service/feature.service';
import { JwtService } from '@midwayjs/jwt';
import { Context } from '@midwayjs/koa';
import { SSOToken } from '../interface';
import { CustomError } from '../error/custom.error';
import { Enterprise, User } from '../entity';

@Controller('/sso')
export class SSOController {
  @Inject()
  ctx: Context;

  @Inject()
  service: SsoService;

  @Inject()
  carryService: CarrySsoService;

  @Inject()
  userService: UserService;

  @Inject()
  featureService: FeatureService;

  @Inject()
  jwtService: JwtService;

  @Get('/callback', { summary: '用户授权回调' })
  async callback(@Query() query: SSO_CALLBACK_REQUEST_DTO) {
    const { code, redirect_uri } = query;
    const token = await this.service.getToken(code, redirect_uri);
    // console.log('token', token);
    const userInfo = await this.service.getUser(token.access_token);
    // console.log('userInfo', userInfo);
    const expiresIn =
      Math.floor((new Date(token.expires_in).getTime() - Date.now()) / 1000) -
      300;
    // console.log('callback expiresIn: ', expiresIn);
    // 生成 JWT
    return {
      token: this.jwtService.signSync(
        { ...userInfo, ssoToken: token },
        {
          expiresIn,
        }
      ),
      expiresIn,
    };
  }

  @Get('/userInfo/ssoToken', {
    summary: '使用第三方SSO令牌获取用户信息',
    description:
      '这个接口只用来解析第三方令牌，前端在频繁获取到token时，会调用这个接口与自身登录信息进行对比，以确定是否需要重新登录',
  })
  async getUserInfoBySsoToken(@Query('ssoToken') ssoToken: string) {
    if (!ssoToken) {
      throw new CustomError('未登录，请先登录');
    }
    console.log('ssoToken', ssoToken);
    const userInfo = await this.carryService.getUser(ssoToken);
    return userInfo;
  }

  @Post('/login/ssoToken', {
    summary: '使用第三方SSO令牌登录',
    description:
      '目前仅实现对接凯锐(carry)的登录，后期若有其他第三方，可通过设置config，然后在这里调用不同的sso service',
  })
  async login(@Body() body: { ssoToken: string }) {
    const { ssoToken } = body;
    if (!ssoToken) {
      throw new CustomError('未登录，请先登录');
    }
    const userInfo = await this.carryService.getUser(ssoToken);
    console.log('userInfo', userInfo);

    // 判断自身系统是否存在该用户，若不存在，则创建用户，并返回用户信息
    const user = await this.userService.getOrcreateFromSSO(userInfo);

    const expiresIn = 7200;
    // 生成 JWT
    return {
      token: this.jwtService.signSync(
        { id: user.id, username: user.username, ssoToken },
        {
          expiresIn,
        }
      ),
      expiresIn,
    };
  }

  @Get('/refresh', { summary: '刷新令牌' })
  async refresh(@Query('refreshToken') refreshToken: string) {
    // 刷新令牌暂时与认证令牌不做区分，jwt过期只影响verify方法，仍能通过decode方法获取payload
    const payload = (await this.jwtService.decodeSync(refreshToken)) as any;
    const { ssoToken } = payload;
    if (ssoToken) {
      const newToken = await this.service.refreshToken(
        (ssoToken as SSOToken).refresh_token
      );
      console.log('newToken: ', newToken);
      const expiresIn =
        Math.floor(
          (new Date(newToken.expires_in).getTime() - Date.now()) / 1000
        ) - 300;
      // console.log('refresh newToken expiresIn: ', expiresIn);
      delete payload.iat;
      delete payload.exp;
      return {
        token: this.jwtService.signSync(
          { ...payload, ssoToken: newToken },
          {
            expiresIn,
          }
        ),
        expiresIn,
      };
    }
    throw new CustomError('未登录，令牌刷新失败');
  }

  @Get('/userinfo', { summary: '获取用户信息' })
  async userinfo(@Query('access_token') token: string) {
    const payload = this.jwtService.decodeSync(token) as any;
    const user = await User.findOne({
      where: {
        username: payload.username,
      },
      include: [
        {
          association: 'roles',
        },
        {
          model: Enterprise,
          attributes: [
            'code',
            'name',
            'school_system',
            'type',
            'province',
            'province_name',
            'city',
            'city_name',
            'area',
            'area_name',
          ],
        },
      ],
      attributes: {
        exclude: ['password'],
      },
    });
    if (!user) {
      throw new CustomError('获取用户信息失败');
    }
    const userInfo = user.toJSON();
    const { roles } = userInfo;
    const features = await this.featureService.getListByRoles(
      roles.map(item => item.id)
    );
    return {
      ...userInfo,
      features,
    };
  }

  @Get('/logout', { summary: '注销退出' })
  async logout(@Query('access_token') token: string) {
    const payload = this.jwtService.decodeSync(token) as any;
    const { ssoToken } = payload;
    if (ssoToken) {
      await this.service.logout((ssoToken as SSOToken).access_token);
    }
    return true;
  }
}
