import { Inject, Logger, Provide } from '@midwayjs/core';
import {
  // ComplianceDetectionDesignCatalog,
  // ComplianceDetectionDesign,
  ComplianceDetectionDesignDetail,
  ComplianceDetectionDesignQuestion,
  ComplianceDetectionDesignStruct,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { SystemQuestions } from '../model';
import { InjectEntityModel } from '@midwayjs/typegoose';
import { ReturnModelType } from '@typegoose/typegoose';
import { QuestionService } from './question.service';
import { randomUUID } from '@midwayjs/core/dist/util/uuid';

@Provide()
export class ComplianceDetectionDesignDetailService extends BaseService<ComplianceDetectionDesignDetail> {
  @Inject()
  ctx: Context;

  @Logger()
  logger;

  @Inject()
  questionService: QuestionService;

  constructor() {
    super('达标检测设计详情');
  }
  getModel = () => {
    return ComplianceDetectionDesignDetail;
  };

  @InjectEntityModel(SystemQuestions)
  systemQuestions: ReturnModelType<typeof SystemQuestions>;

  /**
   * 获取单个
   * @param id id
   * @memberof ComplianceDetectionDesignDetailService
   */
  async show(id) {
    return await ComplianceDetectionDesignDetail.findOne({
      where: { id },
      include: [{ model: ComplianceDetectionDesignQuestion }],
    });
  }

  // /**
  //  * 获取最大排序索引
  //  * @param design_id 达标检测设计id
  //  * @param catalog_id 达标检测设计目录id
  //  * @return {*} 最大排序索引
  //  */
  // async getMaxIndex(design_id, catalog_id) {
  //   const maxIndex = await ComplianceDetectionDesignDetail.max('sort_order', {
  //     where: {
  //       design_id,
  //       catalog_id,
  //     },
  //   });
  //   return (maxIndex as number) || 0;
  // }

  /**
   * 创建达标检测作业详情
   * @param body
   */
  async create(body) {
    const { questions, structs, ...info } = body;
    const transaction =
      await ComplianceDetectionDesignDetail.sequelize.transaction();
    try {
      // 获取最大排序索引
      // const maxIndex = await this.getMaxIndex(info.design_id, info.catalog_id);
      // 创建达标检测作业
      const res = await ComplianceDetectionDesignDetail.create(info, {
        transaction,
      });
      // 如果创建作业有题目信息，则直接入库
      if (questions) {
        const data = questions.map((item, index) => {
          return {
            ...item,
            designDetail_id: res.id,
            sort_order: index + 1,
          };
        });
        await ComplianceDetectionDesignQuestion.bulkCreate(data, {
          transaction,
        });
      }
      // 如果创建作业有结构信息，则直接入库
      if (structs) {
        const structArr = structs.map((item, index) => {
          return {
            ...item,
            designDetail_id: res.id,
            sort_order: index + 1,
          };
        });
        await ComplianceDetectionDesignStruct.bulkCreate(structArr, {
          transaction,
        });
      }
      // else {
      //   // 获取达标检测作业设计详情
      //   const complianceDetectionDesign =
      //     await ComplianceDetectionDesign.findOne({
      //       where: { id: info.design_id },
      //     });
      //   // 批量创建课时作业题目
      //   await this.createBulk(
      //     JSON.stringify(complianceDetectionDesign),
      //     JSON.stringify(res),
      //     transaction
      //   );
      // }
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  // /**
  //  * 批量创建达标检测作业题目信息
  //  * @param complianceDetectionDesign 达标检测设计
  //  * @param complianceDetectionDesignDetail 达标检测设计详情
  //  * @param transaction 事务
  //  */
  // async createBulk(
  //   complianceDetectionDesign,
  //   complianceDetectionDesignDetail,
  //   transaction = null
  // ) {
  //   const { catalog_id, id } = complianceDetectionDesignDetail;
  //   // 根据达标检测目录id获取目录信息
  //   const catalogInfo = await ComplianceDetectionDesignCatalog.findOne({
  //     where: { id: catalog_id },
  //   });

  //   // 根据达标检测目录信息获取对应来源、课时的试题信息
  //   const questions = await this.systemQuestions.aggregate([
  //     {
  //       $match: {
  //         source: complianceDetectionDesign.source,
  //         period: catalogInfo.title,
  //         pid: null, // 查找pid为null的试题，排除掉子题
  //       },
  //     },
  //     { $sort: { createdAt: 1 } }, // 按照createdAt字段升序排序
  //   ]);
  //   //根据课时信息过滤试题
  //   const questionList = questions.filter(v => v.period === catalogInfo.title);
  //   // 删除之前创建的题目
  //   await ComplianceDetectionDesignQuestion.destroy({
  //     where: { designDetail_id: id },
  //     transaction,
  //   });

  //   // 组装作业题目数据
  //   const data = questionList.map((v, index) => ({
  //     designDetail_id: id,
  //     question_id: typeof v._id === 'string' ? v._id : v._id.toString(),
  //     sort_order: index + 1,
  //   }));
  //   await ComplianceDetectionDesignQuestion.bulkCreate(data, { transaction });
  // }

  /**
   * 导入达标范本
   * @param filePath 文件路径
   * @param questionInfo 试题参数
   * @param detailId 达标详情id
   * @returns
   */
  async importComplianceDesignDetails(
    filePath: string,
    questionInfo: any,
    detailId: number
  ) {
    const transaction =
      await ComplianceDetectionDesignDetail.sequelize.transaction();
    // 创建作业详情
    try {
      // 先导入试题
      const questions = await this.questionService.importQuestions(
        filePath,
        questionInfo
      );
      // 需要先删除之前关联的达标试题、达标结构
      await ComplianceDetectionDesignQuestion.destroy({
        where: {
          designDetail_id: detailId,
        },
        transaction,
      });
      await ComplianceDetectionDesignStruct.destroy({
        where: {
          designDetail_id: detailId,
        },
        transaction,
      });
      // 达标作业详情试题
      // 达标作业详情结构
      if (questions && questions.length > 0) {
        const designQuestions = [];
        const questionIds = [];
        questions.forEach((item, index) => {
          questionIds.push(item._id);
          designQuestions.push({
            designDetail_id: detailId,
            sort_order: index + 1,
            question_id: item._id,
            source_table: item.tableName,
          });
        });
        const defaultStruct = {
          id: `hidden_${randomUUID()}`,
          name: '暂无分组',
          designDetail_id: detailId,
          sort_order: 1,
          questionIds: questionIds,
        };
        await ComplianceDetectionDesignQuestion.bulkCreate(designQuestions, {
          transaction,
        });
        await ComplianceDetectionDesignStruct.create(defaultStruct, {
          transaction,
        });
      }
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      this.logger.error(
        `Failed to importComplianceDesignDetails, message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError(error.message);
    }
  }
}
