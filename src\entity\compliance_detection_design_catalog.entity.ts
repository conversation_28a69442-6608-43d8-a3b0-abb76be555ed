import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { TextbookChecklist, ComplianceDetectionDesign } from '.';

export interface ComplianceDetectionDesignCatalogAttributes {
  /** id，主键 */
  id: number;
  /** 原本目录id */
  old_id?: number;
  /** 原本的目录名称 */
  old_title?: string;
  /** 达标检测设计id */
  design_id: number;
  /** 教材名录id */
  textbookChecklist_id: number;
  /** 名称 */
  title: string;
  /** 父id */
  parent_id?: number;
  /** 排序 */
  sort_order: number;
  design?: ComplianceDetectionDesign;
  textbookChecklist?: TextbookChecklist;
  parent?: ComplianceDetectionDesignCatalog;
  children?: ComplianceDetectionDesignCatalog[];
}

@Table({
  tableName: 'compliance_detection_design_catalogs',
  timestamps: true,
  comment: '达标检测设计目录表',
})
export class ComplianceDetectionDesignCatalog
  extends Model<ComplianceDetectionDesignCatalogAttributes>
  implements ComplianceDetectionDesignCatalogAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'id，主键',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '原本目录id',
  })
  old_id?: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '原本的目录名称',
  })
  old_title?: string;

  @ForeignKey(() => ComplianceDetectionDesign)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '达标检测设计id',
  })
  design_id: number;

  @BelongsTo(() => ComplianceDetectionDesign, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  design?: ComplianceDetectionDesign;

  @ForeignKey(() => TextbookChecklist)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材名录id',
  })
  textbookChecklist_id: number;

  @BelongsTo(() => TextbookChecklist)
  textbookChecklist?: TextbookChecklist;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '名称',
  })
  title: string;

  @ForeignKey(() => ComplianceDetectionDesignCatalog)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '父id',
  })
  parent_id?: number;

  @BelongsTo(() => ComplianceDetectionDesignCatalog, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  parent?: ComplianceDetectionDesignCatalog;

  @HasMany(() => ComplianceDetectionDesignCatalog, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  children?: ComplianceDetectionDesignCatalog[];

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '排序',
  })
  sort_order: number;
}
