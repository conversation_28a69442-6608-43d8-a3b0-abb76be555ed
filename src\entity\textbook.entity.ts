import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { Subject, TextbookChecklist } from '.';
import { Publisher } from '.';

export interface TextbookAttributes {
  /** ID，主键 */
  id: number;
  /** 学科ID */
  subject_id: number;
  /** 教材版本 */
  textbook_version: string;
  /** 别名 */
  alias?: string;
  /** 出版单位ID */
  publisher_id?: number;
  subject?: Subject;
  publisher?: Publisher;
  checklist?: TextbookChecklist[];
}

@Table({ tableName: 'textbooks', timestamps: true, comment: '教材表' })
export class Textbook
  extends Model<TextbookAttributes>
  implements TextbookAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'ID，主键',
  })
  id: number;

  @ForeignKey(() => Subject)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_subject_textbook',
      msg: '该学科已存在相同名称的教材版本',
    },
    comment: '学科ID',
  })
  subject_id: number;

  @BelongsTo(() => Subject)
  subject: Subject;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    unique: {
      name: 'unique_subject_textbook',
      msg: '该学科已存在相同名称的教材版本',
    },
    comment: '教材版本',
  })
  textbook_version: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '别名',
  })
  alias: string;

  @ForeignKey(() => Publisher)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '出版单位ID',
  })
  publisher_id: number;

  @BelongsTo(() => Publisher)
  publisher?: Publisher;

  @HasMany(() => TextbookChecklist)
  checklist?: TextbookChecklist[];
}
