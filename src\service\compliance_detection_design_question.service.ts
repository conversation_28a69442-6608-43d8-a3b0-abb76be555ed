import { Inject, Provide } from '@midwayjs/core';
import {
  ComplianceDetectionDesignDetail,
  ComplianceDetectionDesignQuestion,
  ComplianceDetectionDesignStruct,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';

@Provide()
export class ComplianceDetectionDesignQuestionService extends BaseService<ComplianceDetectionDesignQuestion> {
  @Inject()
  ctx: Context;

  constructor() {
    super('达标检测设计题目');
  }
  getModel = () => {
    return ComplianceDetectionDesignQuestion;
  };

  /**
   * 批量创建
   * @param designDetail_id 达标检测设计详情id
   * @param body 试题详情
   */
  async bulkCreate(designDetail_id, body) {
    const transaction =
      await ComplianceDetectionDesignQuestion.sequelize.transaction();
    try {
      //先删除对应作业下之前创建的题目
      await ComplianceDetectionDesignQuestion.destroy({
        where: { designDetail_id },
        transaction,
      });
      // 批量创建题目
      const data = body.map((item, index) => {
        return {
          ...item,
          sort_order: index + 1,
        };
      });
      const res = await ComplianceDetectionDesignQuestion.bulkCreate(data, {
        transaction,
      });
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 保存试卷
   * @param designDetail_id 达标检测设计详情id
   * @param info 试题、结构详情
   */
  async savePaper(designDetail_id, info) {
    const { questions, structs, ...data } = info;
    const transaction =
      await ComplianceDetectionDesignQuestion.sequelize.transaction();
    try {
      // 更新课时作业详情
      await ComplianceDetectionDesignDetail.update(data, {
        where: { id: designDetail_id },
      });
      // 先删除对应作业下之前创建的题目
      await ComplianceDetectionDesignQuestion.destroy({
        where: { designDetail_id },
        transaction,
      });
      // 批量创建题目
      const questionArr = questions.map((item, index) => {
        return {
          ...item,
          sort_order: index + 1,
        };
      });
      await ComplianceDetectionDesignQuestion.bulkCreate(questionArr, {
        transaction,
      });
      // 先删除对应作业下之前创建的结构
      await ComplianceDetectionDesignStruct.destroy({
        where: { designDetail_id },
        transaction,
      });
      // 批量创建结构
      const structArr = structs.map((item, index) => {
        return {
          ...item,
          designDetail_id,
          sort_order: index + 1,
        };
      });
      await ComplianceDetectionDesignStruct.bulkCreate(structArr, {
        transaction,
      });
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }
}
