import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  ForeignKey,
  HasMany,
} from 'sequelize-typescript';
import { Textbook, TextbookCatalog } from '.';

export interface TextbookChecklistAttributes {
  /** ID，主键 */
  id: number;
  /** 教材版本ID */
  textbook_id: number;
  /** 年级编号 */
  grade: string;
  /** 册次 */
  volume: string;
  /** 主编 */
  editor?: string;
  /** 学科教材 */
  textbook?: Textbook;
  catalogs?: TextbookCatalog[];
}

@Table({
  tableName: 'textbook_checklist',
  timestamps: true,
  comment: '教材名录表',
})
export class TextbookChecklist
  extends Model<TextbookChecklistAttributes>
  implements TextbookChecklistAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'ID，主键',
  })
  id: number;

  @ForeignKey(() => Textbook)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_textbook_grade_volume',
      msg: '已存在相同教材名录',
    },
    comment: '教材版本ID',
  })
  textbook_id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: {
      name: 'unique_textbook_grade_volume',
      msg: '已存在相同教材名录',
    },
    comment: '年级编号',
  })
  grade: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: {
      name: 'unique_textbook_grade_volume',
      msg: '已存在相同教材名录',
    },
    comment: '册次',
  })
  volume: string;

  @Column({
    type: DataType.STRING,
    comment: '主编',
  })
  editor?: string;

  @BelongsTo(() => Textbook)
  textbook?: Textbook;

  @HasMany(() => TextbookCatalog)
  catalogs?: TextbookCatalog[];
}
