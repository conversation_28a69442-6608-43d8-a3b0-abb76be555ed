import { Logger, Provide, Scope, ScopeEnum } from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';

type sourceType = '认证' | '试卷' | 'debug';
export type LoggerMsgProps = {
  /** 日志内容 */
  message: string;
  /** 日志来源 */
  source: sourceType;
  /** 用户名 */
  username?: string;
  /** 错误详情 */
  details?: string;
};

@Provide()
@Scope(ScopeEnum.Singleton, { allowDowngrade: true })
export class CustomLogger implements ILogger {
  @Logger()
  logger: ILogger;

  /**
   * 信息日志
   *
   * @param {LoggerMsgProps} msg 日志信息
   * @param {...any[]} args
   * @memberof CustomLogger
   */
  public info(msg: LoggerMsgProps, ...args: any[]) {
    this.logger.info(JSON.stringify(msg), ...args);
  }
  /**
   * 调试日志
   *
   * @param {LoggerMsgProps} msg 日志信息
   * @param {...any[]} args 日志参数
   * @memberof CustomLogger
   */
  public debug(msg: LoggerMsgProps, ...args: any[]) {
    this.logger.debug(JSON.stringify(msg), ...args);
  }
  /**
   * 错误日志不建议使用这个方法，请使用 上下文日志（App Logger）对应的error，以获取接口请求信息
   *
   * @param {LoggerMsgProps} msg 日志信息
   * @param {...any[]} args 日志参数
   * @memberof CustomLogger
   */
  public error(msg: LoggerMsgProps, ...args: any[]) {
    this.logger.error(JSON.stringify(msg), ...args);
  }
  /**
   * 警告日志
   *
   * @param {LoggerMsgProps} msg 日志信息
   * @param {...any[]} args 日志参数
   * @memberof CustomLogger
   */
  public warn(msg: LoggerMsgProps, ...args: any[]) {
    this.logger.warn(JSON.stringify(msg), ...args);
  }

  public write(msg: LoggerMsgProps, ...args: any[]) {
    this.logger.write(JSON.stringify(msg), ...args);
  }
  public verbose(msg: LoggerMsgProps, ...args: any[]) {
    this.logger.verbose(JSON.stringify(msg), ...args);
  }
}
