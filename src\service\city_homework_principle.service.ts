import { Inject, Provide } from '@midwayjs/core';
import { CityHomeworkPrinciple } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class CityHomeworkPrincipleService extends BaseService<CityHomeworkPrinciple> {
  @Inject()
  ctx: Context;

  constructor() {
    super('市级作业设计原则');
  }
  getModel = () => {
    return CityHomeworkPrinciple;
  };
}
