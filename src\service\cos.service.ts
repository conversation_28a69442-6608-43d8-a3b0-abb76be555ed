import { App, Provide } from '@midwayjs/core';
import { Application } from '@midwayjs/koa';
import * as COS from 'cos-nodejs-sdk-v5';
import * as STS from 'qcloud-cos-sts';

/** 授权策略 */
const policy = (config: {
  AppID: string;
  region: string;
  bucket: string;
  allowPrefix: string;
}) => ({
  version: '2.0',
  statement: [
    {
      action: [
        // 简单上传操作
        'name/cos:PutObject',
        // 表单上传对象
        'name/cos:PostObject',
        // 分块上传：初始化分块操作
        'name/cos:InitiateMultipartUpload',
        // 分块上传：List 进行中的分块上传
        'name/cos:ListMultipartUploads',
        // 分块上传：List 已上传分块操作
        'name/cos:ListParts',
        // 分块上传：上传分块操作
        'name/cos:UploadPart',
        // 分块上传：完成所有分块上传操作
        'name/cos:CompleteMultipartUpload',
        // 取消分块上传操作
        'name/cos:AbortMultipartUpload',
        // 下载操作
        'name/cos:GetObject',
        // 删除操作
        'name/cos:DeleteObject',
      ],
      effect: 'allow',
      resource: [
        // 授权范围,授予 APPID 为config.AppID，地域为 config.region，存储桶为 config.bucket ，路径前缀限制为 config.allowPrefix 的读写权限
        `qcs::cos:${config.region}:uid/${config.AppID}:${config.bucket}/${config.allowPrefix}`,
      ],
    },
  ],
});

@Provide()
export class CosService {
  @App()
  app: Application;

  private cos: COS;

  constructor() {
    this.cos = new COS({
      getAuthorization: async (_options, callback) => {
        const res = await this.getCredential();
        console.log('更新cos密钥');
        callback({
          TmpSecretId: res.credentials.tmpSecretId,
          TmpSecretKey: res.credentials.tmpSecretKey,
          XCosSecurityToken: res.credentials.sessionToken,
          StartTime: res.startTime,
          ExpiredTime: res.expiredTime,
        });
      },
    });
  }

  /**
   * 获取临时密钥
   *
   * @return {*} res
   * @memberof CosService
   */
  async getCredential(): Promise<STS.CredentialData> {
    const config: {
      secretId: string;
      secretKey: string;
      proxy: string;
      host: string;
      durationSeconds: number;
      // 放行判断相关参数
      AppID: string;
      bucket: string;
      region: string;
      allowPrefix: string;
    } = this.app.getConfig('cos');

    return new Promise((resolve, reject) => {
      STS.getCredential(
        {
          secretId: config.secretId,
          secretKey: config.secretKey,
          proxy: config.proxy,
          policy: policy({
            AppID: config.AppID,
            region: config.region,
            bucket: config.bucket,
            allowPrefix: config.allowPrefix,
          }),
          durationSeconds: config.durationSeconds,
        },
        (err, credential) => {
          if (err) {
            reject(err);
          } else {
            resolve(credential);
          }
        }
      );
    });
  }

  /**
   * 上传文件
   */
  async uploadFile(key: string, filePath: string) {
    const {
      bucket,
      region,
      allowPrefix,
    }: {
      bucket: string;
      region: string;
      allowPrefix: string;
    } = this.app.getConfig('cos');
    return new Promise((resolve, reject) => {
      this.cos.uploadFile(
        {
          Bucket: bucket,
          Region: region,
          // 本项目只能上传到指定位置
          Key: allowPrefix + key,
          FilePath: filePath,
        },
        (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        }
      );
    });
  }

  /**
   * 下载文件
   */
  async getObject(key: string) {
    const {
      bucket,
      region,
    }: {
      bucket: string;
      region: string;
    } = this.app.getConfig('cos');
    return new Promise((resolve, reject) => {
      this.cos.getObject(
        {
          Bucket: bucket,
          Region: region,
          Key: key,
        },
        (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data.Body);
          }
        }
      );
    });
  }

  /**
   * 删除文件
   */
  async deleteObject(key: string) {
    const {
      bucket,
      region,
    }: {
      bucket: string;
      region: string;
    } = this.app.getConfig('cos');

    console.log('deleteObject: ', {
      Bucket: bucket,
      Region: region,
      Key: key,
    });

    return new Promise((resolve, reject) => {
      this.cos.deleteObject(
        {
          Bucket: bucket,
          Region: region,
          Key: decodeURIComponent(key),
        },
        (err, data) => {
          console.log(err || data);
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        }
      );
    });
  }

  /**
   * 列出文件
   */
  async listObjects(bucket: string, region: string, prefix: string) {
    return new Promise((resolve, reject) => {
      this.cos.getBucket(
        {
          Bucket: bucket,
          Region: region,
          Prefix: prefix,
        },
        (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data.Contents);
          }
        }
      );
    });
  }
}
