import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { SemesterService } from '../service/semester.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { CreateSemesterDTO, UpdateSemesterDTO } from '../dto/semester';
import { Semester } from '../entity/semester.entity';

@Controller('/semesters')
export class SemesterController {
  @Inject()
  ctx: Context;

  @Inject()
  service: SemesterService;

  @Get('/', { summary: '查询学期列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // name支持模糊查询
    if (queryInfo.name) {
      queryInfo.name = {
        [Op.like]: `%${queryInfo.name}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['code', 'DESC']],
    });
  }

  @Get('/:id', { summary: '按ID查询学期' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定学期');
    }
    return res;
  }

  @Post('/', { summary: '新增学期' })
  async create(@Body() info: CreateSemesterDTO) {
    const res = await this.service.create(info);
    if (info.status === 1) {
      console.log('激活学期：', info.code);
      await Semester.update(
        { status: 0 },
        { where: { code: { [Op.not]: res.code } } }
      );
    }
    return res;
  }

  @Put('/:id', { summary: '更新学期' })
  async update(@Param('id') id: number, @Body() body: UpdateSemesterDTO) {
    await this.service.update({ id }, body);
    if (body.status === 1) {
      console.log('激活学期：', body.code);
      await Semester.update({ status: 0 }, { where: { id: { [Op.not]: id } } });
    }
    return true;
  }

  @Del('/:id', { summary: '删除学期' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }
}
