import { App, Inject, InjectClient, Provide } from '@midwayjs/core';
import { Application } from '@midwayjs/koa';
import { HttpServiceFactory, HttpService } from '@midwayjs/axios';
import { JwtService } from '@midwayjs/jwt';
import { CustomError } from '../../error/custom.error';
import { UserInfo_Carry } from './interface';

@Provide()
export class SsoService {
  @App()
  app: Application;

  @InjectClient(HttpServiceFactory, 'carry')
  ssoAxios: HttpService;

  @Inject()
  jwtService: JwtService;

  /**
   * 使用访问令牌获取用户信息
   *
   * @param {string} access_token
   * @return {string} res
   * @memberof SsoService
   */
  async getUser(access_token: string) {
    const userInfoResponse = await this.ssoAxios.get<{
      success: boolean;
      message?: string;
      code: number;
      result?: {
        userInfo: UserInfo_Carry;
      };
      timestamp: number;
    }>('/sys/user/getUserInfoToZysj', {
      headers: {
        'X-Access-Token': access_token,
      },
    });
    const { success, message, code, result, timestamp } = userInfoResponse.data;
    console.log('第三方认证返回timestamp: ', timestamp);
    if (code !== 200) {
      console.log(userInfoResponse);
      throw new CustomError(message || '获取用户信息失败');
    }
    if (!success) {
      console.log(userInfoResponse);
      throw new CustomError(message || '获取用户信息失败');
    }
    const { userInfo: data } = result;
    return data;
  }
}
