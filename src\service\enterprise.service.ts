import { Inject, Provide } from '@midwayjs/core';
import { Enterprise } from '../entity/enterprise.entity';
import { AreaService } from './area.service';
import { BaseService } from '../common/BaseService';

@Provide()
export class EnterpriseService extends BaseService<Enterprise> {
  @Inject()
  areaService: AreaService;

  constructor() {
    super('单位');
  }

  getModel() {
    return Enterprise;
  }

  async getOrcreateFromSSO({
    name,
    code,
    areaCode,
  }: {
    name: string;
    code: string;
    areaCode: string;
  }) {
    const enterprise = await this.findOne({ code });
    const areaInfo = await this.areaService.getS_S_Q(areaCode);
    if (enterprise) {
      if (
        enterprise.name !== name ||
        enterprise.province !== areaInfo.province ||
        enterprise.city !== areaInfo.city ||
        enterprise.area !== areaInfo.area
      ) {
        await Enterprise.update(
          {
            name,
            ...areaInfo,
          },
          {
            where: {
              id: enterprise.id,
            },
          }
        );
        return {
          ...enterprise.toJSON(),
          name,
          ...areaInfo,
        };
      }
      return enterprise;
    }
    return await this.create({
      name,
      code,
      ...areaInfo,
    });
  }
}
