import { Table, Column, Model, DataType } from 'sequelize-typescript';

export interface PlanCourseSystemAttributes {
  /** 主键 */
  id: number;
  /** 学制 */
  system?: string;
  /** 类别名称 */
  type?: string;
  /** 描述 */
  describe?: string;
}

@Table({
  tableName: 'plan_course_system',
  timestamps: true,
  comment: '课程学制表',
})
export class PlanCourseSystem
  extends Model<PlanCourseSystemAttributes>
  implements PlanCourseSystemAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @Column({
    type: DataType.ENUM('五四制', '六三制'),
    allowNull: true,
    comment: '学制',
  })
  system?: string;

  @Column({
    type: DataType.ENUM('国家课程', '地方课程', '校本课程'),
    allowNull: true,
    comment: '类别名称',
  })
  type?: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '描述',
  })
  describe?: string;
}
