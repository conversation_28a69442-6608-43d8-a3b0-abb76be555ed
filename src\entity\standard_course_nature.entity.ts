import { Table, Column, Model, DataType } from 'sequelize-typescript';

export interface StandardCourseNatureAttributes {
  /** 主键ID */
  id: number;
  /** 名称 */
  name?: string;
  /** 描述 */
  describe?: string;
  /** 解析 */
  analy?: string;

  // /** 课程科目id */
  // course_id?: number;
  // /** 课程科目 */
  // course_name?: string;
  // /** 课程类别 */
  // course_category_id?: number;
  // /** 课程类别 */
  // course_category?: string;
  // /** 课程性质描述 */
  // course_nature?: string;
  // /** 学校id */
  // enterprise_id?: number;
  // courseCategory?: PlanCourseSystem;
}

@Table({
  tableName: 'standard_course_nature',
  timestamps: true,
  comment: '课程性质表',
})
export class StandardCourseNature
  extends Model<StandardCourseNatureAttributes>
  implements StandardCourseNatureAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质名称',
  })
  name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质描述',
  })
  describe?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质解析',
  })
  analy?: string;

  // @ForeignKey(() => PlanCourseSubject)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '课程科目id',
  // })
  // course_id?: number;

  // @Column({
  //   type: DataType.STRING(64),
  //   allowNull: true,
  //   comment: '课程科目',
  // })
  // course_name?: string;

  // @ForeignKey(() => PlanCourseSystem)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '课程类别',
  // })
  // course_category_id?: number;

  // @Column({
  //   type: DataType.STRING(255),
  //   allowNull: true,
  //   comment: '课程类别',
  // })
  // course_category?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '课程性质描述',
  // })
  // course_nature?: string;

  // @ForeignKey(() => Enterprise)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '学校id',
  // })
  // enterprise_id?: number;

  // @BelongsTo(() => PlanCourseSubject, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // courseSubject?: PlanCourseSubject;

  // @BelongsTo(() => PlanCourseSystem, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // courseCategory?: PlanCourseSystem;

  // @BelongsTo(() => Enterprise, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // enterprise?: Enterprise;
}
