# 作业设计系统服务端文件列表

## 目录结构概览

本项目是基于Midway.js框架开发的作业设计系统后端服务，主要包含以下目录：

- common: 通用工具和基础类
- config: 配置文件
- controller: 控制器
- dto: 数据传输对象
- entity: 数据实体模型
- error: 错误处理
- filter: 过滤器
- listener: 监听器
- middleware: 中间件
- model: MongoDB模型
- service: 服务层

## 文件列表及说明

### 配置文件

| 文件名 | 说明 |
| --- | --- |
| configuration.ts | 应用配置入口，导入各种中间件和组件 |
| config/config.default.ts | 默认配置，包含数据库连接等信息 |
| config/config.local.ts | 本地开发环境配置 |
| config/config.production.ts | 生产环境配置 |
| interface.ts | 接口定义文件 |

### 通用工具

| 文件名 | 说明 |
| --- | --- |
| common/BaseService.ts | 基础服务类，提供通用CRUD操作 |
| common/CustomLogger.ts | 自定义日志工具 |
| common/ErrorCode.ts | 错误码定义 |
| common/ExcelUtil.ts | Excel导入导出工具 |
| common/InitData.ts | 初始化数据 |
| common/Util.ts | 通用工具函数 |

### 控制器

| 文件名 | 说明 |
| --- | --- |
| controller/area.controller.ts | 行政区划控制器 |
| controller/auth.controller.ts | 认证控制器，处理登录等 |
| controller/class_relation.controller.ts | 班级关系控制器 |
| controller/cos.controller.ts | 对象存储控制器 |
| controller/dictionary.controller.ts | 字典控制器 |
| controller/enterprise.controller.ts | 企业/单位控制器 |
| controller/exclude_simliar_question.controller.ts | 排除相似题控制器 |
| controller/feature.controller.ts | 功能点控制器 |
| controller/file.controller.ts | 文件控制器 |
| controller/import.controller.ts | 导入控制器 |
| controller/knowledge_point.controller.ts | 知识点控制器 |
| controller/permission.controller.ts | 权限控制器 |
| controller/personal_question.controller.ts | 个人题库控制器 |
| controller/publisher.controller.ts | 出版单位控制器 |
| controller/question_extend_type.controller.ts | 题目扩展类型控制器 |
| controller/question_relation.controller.ts | 题目关系控制器 |
| controller/question_tag.controller.ts | 题目标签控制器 |
| controller/role.controller.ts | 角色控制器 |
| controller/school_question.controller.ts | 校本题库控制器 |
| controller/semester.controller.ts | 学期控制器 |
| controller/similar_question.controller.ts | 相似题控制器 |
| controller/sso.controller.ts | 单点登录控制器 |
| controller/subject.controller.ts | 学科控制器 |
| controller/system_question.controller.ts | 系统题库控制器 |
| controller/textbook.controller.ts | 教材控制器 |
| controller/textbook_catalog.controller.ts | 教材目录控制器 |
| controller/textbook_checklist.controller.ts | 教材检查表控制器 |
| controller/textbook_choose.controller.ts | 教材选用控制器 |
| controller/textbook_config.controller.ts | 教材配置控制器 |
| controller/textbook_temp.controller.ts | 教材模板控制器 |
| controller/textbook_temp_detail.controller.ts | 教材模板详情控制器 |
| controller/user.controller.ts | 用户控制器 |

### 服务层

| 文件名 | 说明 |
| --- | --- |
| service/area.service.ts | 行政区划服务 |
| service/auth.service.ts | 认证服务 |
| service/class_relation.service.ts | 班级关系服务 |
| service/cos.service.ts | 对象存储服务 |
| service/dictionary.service.ts | 字典服务 |
| service/enterprise.service.ts | 企业/单位服务 |
| service/exclude_similar_question.service.ts | 排除相似题服务 |
| service/feature.service.ts | 功能点服务 |
| service/file.service.ts | 文件服务 |
| service/import.service.ts | 导入服务 |
| service/knowledge_point.service.ts | 知识点服务 |
| service/permission.service.ts | 权限服务 |
| service/personal_question.service.ts | 个人题库服务 |
| service/publisher.service.ts | 出版单位服务 |
| service/question_extend_type.service.ts | 题目扩展类型服务 |
| service/question_relation.service.ts | 题目关系服务 |
| service/question_tag.service.ts | 题目标签服务 |
| service/role.service.ts | 角色服务 |
| service/school_homework.service.ts | 校本作业服务 |
| service/school_homework_detail.service.ts | 校本作业详情服务 |
| service/school_question.service.ts | 校本题库服务 |
| service/semester.service.ts | 学期服务 |
| service/similar_question.service.ts | 相似题服务 |
| service/sso.service.ts | 单点登录服务 |
| service/subject.service.ts | 学科服务 |
| service/system.service.ts | 系统服务 |
| service/system_question.service.ts | 系统题库服务 |
| service/textbook.service.ts | 教材服务 |
| service/textbook_catalog.service.ts | 教材目录服务 |
| service/textbook_checklist.service.ts | 教材检查表服务 |
| service/textbook_choose.service.ts | 教材选用服务 |
| service/textbook_config.service.ts | 教材配置服务 |
| service/textbook_temp.service.ts | 教材模板服务 |
| service/textbook_temp_detail.service.ts | 教材模板详情服务 |
| service/user.service.ts | 用户服务 |

### 数据实体

| 文件名 | 说明 |
| --- | --- |
| entity/area.entity.ts | 行政区划实体 |
| entity/class_relation.entity.ts | 班级关系实体 |
| entity/dictionary.entity.ts | 字典实体 |
| entity/enterprise.entity.ts | 企业/单位实体 |
| entity/exclude_similar_question.entity.ts | 排除相似题实体 |
| entity/feature.entity.ts | 功能点实体 |
| entity/homework_detail_template_mapping.entity.ts | 作业详情模板映射实体 |
| entity/index.ts | 实体索引文件 |
| entity/knowledge_point.entity.ts | 知识点实体 |
| entity/permission.entity.ts | 权限实体 |
| entity/publisher.entity.ts | 出版单位实体 |
| entity/question_extend_type.entity.ts | 题目扩展类型实体 |
| entity/question_relation.entity.ts | 题目关系实体 |
| entity/question_tag.entity.ts | 题目标签实体 |
| entity/role.entity.ts | 角色实体 |
| entity/role_permission.entity.ts | 角色权限实体 |
| entity/school_homework.entity.ts | 校本作业实体 |
| entity/school_homework_detail.entity.ts | 校本作业详情实体 |
| entity/semester.entity.ts | 学期实体 |
| entity/similar_question.entity.ts | 相似题实体 |
| entity/subject.entity.ts | 学科实体 |
| entity/textbook.entity.ts | 教材实体 |
| entity/textbook_catalog.entity.ts | 教材目录实体 |
| entity/textbook_checklist.entity.ts | 教材检查表实体 |
| entity/textbook_choose.entity.ts | 教材选用实体 |
| entity/textbook_choose_detail.entity.ts | 教材选用详情实体 |
| entity/textbook_config.entity.ts | 教材配置实体 |
| entity/textbook_temp.entity.ts | 教材模板实体 |
| entity/textbook_temp_detail.entity.ts | 教材模板详情实体 |
| entity/user.entity.ts | 用户实体 |
| entity/user_role.entity.ts | 用户角色实体 |

### 其他文件

| 文件名 | 说明 |
| --- | --- |
| dto/BaseDTO.ts | 基础数据传输对象 |
| dto/auth.ts | 认证相关DTO |
| dto/semester.ts | 学期相关DTO |
| error/custom.error.ts | 自定义错误类 |
| filter/axios.filter.ts | Axios请求过滤器 |
| filter/custom.filter.ts | 自定义过滤器 |
| filter/database.filter.ts | 数据库过滤器 |
| filter/default.filter.ts | 默认过滤器 |
| filter/noauth.filter.ts | 无认证过滤器 |
| filter/notfound.filter.ts | 404过滤器 |
| listener/autoload.ts | 自动加载监听器 |
| middleware/format.middleware.ts | 格式化中间件 |
| middleware/jwt.middleware.ts | JWT认证中间件 |
| middleware/requestlogger.middleware.ts | 请求日志中间件 |
| model/index.ts | 模型索引文件 |
| model/personal_questions.entity.ts | 个人题库MongoDB模型 |
| model/reference_type.ts | 引用类型模型 |
| model/school_questions.entity.ts | 校本题库MongoDB模型 |
| model/system_questions.entity.ts | 系统题库MongoDB模型 |