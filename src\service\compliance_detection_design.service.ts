import { Inject, Provide } from '@midwayjs/core';
import {
  ComplianceDetectionDesign,
  ComplianceDetectionDesignCatalog,
  ComplianceDetectionDesignDetail,
  ComplianceDetectionTemplateDetail,
  ComplianceDetectionTemplateQuestion,
  ComplianceDetectionTemplateStruct,
  TextbookCatalog,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { ComplianceDetectionDesignCatalogService } from '../service/compliance_detection_design_catalog.service';

@Provide()
export class ComplianceDetectionDesignService extends BaseService<ComplianceDetectionDesign> {
  @Inject()
  ctx: Context;

  constructor() {
    super('达标检测设计');
  }
  getModel = () => {
    return ComplianceDetectionDesign;
  };

  @Inject()
  complianceDetectionDesignCatalogService: ComplianceDetectionDesignCatalogService;

  /**
   * 创建达标检测设计
   * 复制对应教材目录，以及对应目录下的作业详情、作业题目、作业结构
   * @param info 达标检测设计信息
   */
  async create(info) {
    const transaction = await ComplianceDetectionDesign.sequelize.transaction();
    try {
      // 如果没有使用达标检测范本，则直接创建达标检测设计信息及目录信息
      if (!info.template_id) {
        // 创建达标检测设计信息
        const res = await ComplianceDetectionDesign.create(info, {
          transaction,
        });
        // 复制当前教材目录信息
        await this.comonCopyCatalog(info, res.id, transaction);

        await transaction.commit();
        return res;
      } else {
        // 如果使用达标检测范本，则将对应的目录信息、作业信息、试题信息、结构信息复制到达标检测设计中
        // 创建达标检测设计信息
        const res = await ComplianceDetectionDesign.create(info, {
          transaction,
        });

        // 复制当前教材目录信息、作业信息、试题、结构信息
        await this.comonCopyCatalog(info, res.id, transaction);

        await transaction.commit();
        return res;
      }
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 更新达标检测设计
   * @param id 达标检测设计id
   * @param info 更新数据
   */
  async updateById(id, info) {
    const transaction = await ComplianceDetectionDesign.sequelize.transaction();
    try {
      // 如果是发布作业设计更新作业设计及设计下的详情都为发布
      if (info.status === '发布') {
        await ComplianceDetectionDesign.update(info, {
          where: { id },
          transaction,
        });
        await ComplianceDetectionDesignDetail.update(
          { status: info.status },
          { where: { design_id: id }, transaction }
        );
      } else if (info.template_id) {
        // 如果是编辑作业设计，判断是否更换作业范本，如果更换作业范本，需要清除所有作业试题、结构信息，根据新的范本生成
        const designInfo = await ComplianceDetectionDesign.findOne({
          where: { id },
        });

        // 如果传入的课时作业范本id不等于当前课时作业范本id
        if (designInfo.template_id !== info.template_id) {
          // 删除课时作业设计目录信息
          await ComplianceDetectionDesignCatalog.destroy({
            where: { design_id: id },
            transaction,
          });
          // 删除课时作业设计详情信息，会一并删除试题、结构信息
          await ComplianceDetectionDesignDetail.destroy({
            where: { design_id: id },
            transaction,
          });

          // 复制当前教材目录信息及作业信息、试题、结构信息
          await this.comonCopyCatalog(info, id, transaction);
        }
        // 更新达标检测设计信息
        await ComplianceDetectionDesign.update(info, {
          where: { id },
          transaction,
        });
      } else {
        // 更新达标检测设计信息
        await ComplianceDetectionDesign.update(info, {
          where: { id },
          transaction,
        });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 公共方法，复制对应教材目录，以及对应目录下的作业信息、试题信息、结构信息
   * @param {*} designInfo 达标检测设计信息
   * @param {*} id 达标检测设计id
   * @param {*} transaction 事务
   * @memberof ComplianceDetectionDesignService
   */
  async comonCopyCatalog(designInfo, id, transaction) {
    let templateDetail = [];
    // 如果达标检测范本id存在，则获取范本下的作业信息、试题信息、结构信息
    if (designInfo.template_id) {
      templateDetail = await ComplianceDetectionTemplateDetail.findAll({
        where: {
          template_id: designInfo.template_id,
          status: '发布', //查询已发布的范本作业，未发布不做同步
        },
        include: [
          { model: ComplianceDetectionTemplateQuestion },
          { model: ComplianceDetectionTemplateStruct },
        ],
      });
    }

    // 根据教材名录id, 获取对应教材目录信息
    const textbookCatalogs = await TextbookCatalog.findAll({
      where: {
        textbookChecklist_id: designInfo.textbookChecklist_id,
      },
      order: [['id', 'ASC']], //避免复制目录层级错乱
    });

    // 如果教材目录信息存在，则复制教材目录信息及作业信息到达标检测设计目录中
    if (textbookCatalogs && textbookCatalogs.length > 0) {
      // await this.complianceDetectionDesignCatalogService.copyCatalog(
      //   textbookCatalogs,
      //   id,
      //   transaction,
      //   templateDetail
      // );
      // 使用优化后的方法复制
      await this.complianceDetectionDesignCatalogService.copyCatalogNew(
        textbookCatalogs,
        id,
        transaction,
        templateDetail
      );
    }
  }
}
