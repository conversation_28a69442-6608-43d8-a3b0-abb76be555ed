import { Inject, Provide } from '@midwayjs/core';
import { CityHomeworkCase } from '../entity/city_homework_case.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class CityHomeworkCaseService extends BaseService<CityHomeworkCase> {
  @Inject()
  ctx: Context;

  constructor() {
    super('市级作业典型案例集');
  }
  getModel = () => {
    return CityHomeworkCase;
  };
}
