import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { TextbookConfig } from '../entity';

export interface QuestionRelationAttributes {
  /** id */
  id: number;
  /** 作业配置id */
  textbook_config_id?: number;
  /** 题目类型code */
  question_type_value: string;
  /** 题目类型 */
  question_type: string;
  /** 是否仅需按题型匹配 */
  is_match: boolean;
  /** 可匹配题型code */
  match_type_value?: string;
  /** 可匹配题型 */
  match_type?: string;
}

@Table({
  tableName: 'question_relation',
  timestamps: true,
  comment: '题型关系表',
})
export class QuestionRelation
  extends Model<QuestionRelationAttributes>
  implements QuestionRelationAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '题型关系id',
  })
  id: number;

  @ForeignKey(() => TextbookConfig)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '作业配置id',
  })
  textbook_config_id?: number;

  @BelongsTo(() => TextbookConfig, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  textbook_config: TextbookConfig;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '题目类型code',
  })
  question_type_value: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '题目类型',
  })
  question_type: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否仅需按题型匹配',
  })
  is_match: boolean;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '可匹配题型code',
  })
  match_type_value?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '可匹配题型',
  })
  match_type?: string;
}
