import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ComplianceDetectionDesignDetail } from '.';

export interface ComplianceDetectionDesignStructAttributes {
  /** 主键 */
  id: string;
  /** 达标检测设计详情id */
  designDetail_id: number;
  /** 结构名称 */
  name: string;
  /** 试题ids */
  questionIds: string[];
  /** 排序 */
  sort_order: number;
  designDetail?: ComplianceDetectionDesignDetail;
}

@Table({
  tableName: 'compliance_detection_design_struct',
  timestamps: true,
  comment: '达标检测设计结构表',
})
export class ComplianceDetectionDesignStruct
  extends Model<ComplianceDetectionDesignStructAttributes>
  implements ComplianceDetectionDesignStructAttributes
{
  @Column({
    type: DataType.STRING(64),
    primaryKey: true,
    comment: '主键',
  })
  id: string;

  @ForeignKey(() => ComplianceDetectionDesignDetail)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '达标检测设计详情id',
  })
  designDetail_id: number;

  @BelongsTo(() => ComplianceDetectionDesignDetail, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  designDetail?: ComplianceDetectionDesignDetail;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '结构名称',
  })
  name: string;

  @Column({
    type: DataType.TEXT,
    allowNull: false,
    comment: '试题ids',
    get() {
      const newValue = this.getDataValue('questionIds');
      return newValue ? newValue.split(',') : [];
    },
    set(value: string[]) {
      this.setDataValue('questionIds', value.join(','));
    },
  })
  questionIds: string[];

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '排序',
  })
  sort_order: number;
}
