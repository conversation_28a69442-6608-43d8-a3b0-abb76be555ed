import { Inject, Provide } from '@midwayjs/core';
import { PlanCourseDuration } from '../entity/plan_course_duration.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class PlanCourseDurationService extends BaseService<PlanCourseDuration> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课程时长');
  }
  getModel = () => {
    return PlanCourseDuration;
  };
}
