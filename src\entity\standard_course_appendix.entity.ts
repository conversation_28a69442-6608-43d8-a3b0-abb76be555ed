import {
  Table,
  Column,
  Model,
  DataType,
  // ForeignKey,
  // BelongsTo,
} from 'sequelize-typescript';
// import { Subject, Enterprise } from '.';

export interface StandardCourseAppendixAttributes {
  /** 主键ID */
  id: number;
  /** 名称 */
  name?: string;
  /** 描述 */
  describe?: string;
  /** 解析 */
  analy?: string;
  // /** 附录序号 */
  // appendix_number?: number;
  // /** 附录名称 */
  // appendix_name?: string;
  // /** 附录内容 */
  // appendix_content?: string;
  // /** 附录解析 */
  // appendix_analysis?: string;
  // /** 所属学科名称 */
  // subject_name?: string;
  // /** 所属学科id */
  // subject_id?: number;
  // /** 学校id */
  // enterprise_id?: number;
}

@Table({
  tableName: 'standard_course_appendix',
  timestamps: true,
  comment: '课程附录表',
})
export class StandardCourseAppendix
  extends Model<StandardCourseAppendixAttributes>
  implements StandardCourseAppendixAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质名称',
  })
  name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质描述',
  })
  describe?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质解析',
  })
  analy?: string;

  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '附录序号',
  // })
  // appendix_number?: number;

  // @Column({
  //   type: DataType.STRING(255),
  //   allowNull: true,
  //   comment: '附录名称',
  // })
  // appendix_name?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '附录内容',
  // })
  // appendix_content?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '附录解析',
  // })
  // appendix_analysis?: string;

  // @Column({
  //   type: DataType.STRING(64),
  //   allowNull: true,
  //   comment: '所属学科名称',
  // })
  // subject_name?: string;

  // @ForeignKey(() => Subject)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '所属学科id',
  // })
  // subject_id?: number;

  // @ForeignKey(() => Enterprise)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '学校id',
  // })
  // enterprise_id?: number;

  // @BelongsTo(() => Subject, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // subject?: Subject;

  // @BelongsTo(() => Enterprise, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // enterprise?: Enterprise;
}
