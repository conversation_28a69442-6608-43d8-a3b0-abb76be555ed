import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { TextbookTempService } from '../service/textbook_temp.service';
import { CustomError } from '../error/custom.error';
import { TextbookChecklist } from '../entity';

@Controller('/textbook_temp')
export class TextbookTempController {
  @Inject()
  ctx: Context;

  @Inject()
  service: TextbookTempService;

  @Get('/', { summary: '获取教材作业模板列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.getList(queryInfo, offset, limit);
  }

  @Get('/:id', { summary: '获取教材作业模板详情' })
  async show(@Param('id') id: string) {
    const res = await this.service.get(id);
    if (!res) {
      throw new CustomError('未找到指教材作业模版');
    }
    return res;
  }

  @Post('/', { summary: '创建教材作业模版' })
  async create(@Body() body: any) {
    const { subject, textbook_id, grade, volume, ...info } = body;
    if (!grade || !subject || !textbook_id || !volume) {
      throw new CustomError('缺少必要参数');
    }

    const textbook_checklist = await TextbookChecklist.findOne({
      where: {
        textbook_id,
        grade,
        volume,
      },
    });

    if (!textbook_checklist) {
      throw new CustomError('未找到教材信息');
    }

    const res = await this.service.create({
      ...info,
      textbook_id,
    });
    return res;
  }

  @Put('/:id', { summary: '更新教材作业模版' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除教材作业模版' })
  async destroy(@Param('id') id: string) {
    // TODO:删除范本时，删除其中导入的小节中所有导入的题目
    await this.service.destroy({ id });
    return true;
  }
}
