import {
  Table,
  Column,
  Model,
  DataType,
  // ForeignKey,
  // BelongsTo,
} from 'sequelize-typescript';
// import { PlanCourseSubject } from './plan_course_subject.entity'; // 假设存在一个 Course 实体类
// import { StandardCourseNature } from './standard_course_nature.entity'; // 假设存在一个 StandardCourseNature 实体类
// import { StandardCourseConcept } from './standard_course_concept.entity'; // 假设存在一个 StandardCourseConcept 实体类
// import { Enterprise } from './enterprise.entity'; // 假设存在一个 Enterprise 实体类

export interface StandardCourseGoalAttributes {
  /** 主键ID */
  id: number;
  /** 名称 */
  name?: string;
  /** 描述 */
  describe?: string;
  /** 解析 */
  analy?: string;
  // /** 课程科目id */
  // course_id?: number;
  // /** 课程科目 */
  // course_name?: string;
  // /** 父级目标ID */
  // parent_id?: number;
  // /** 课程性质ID */
  // course_nature_id?: number;
  // /** 课程理念ID */
  // course_concept_id?: number;
  // /** 核心素养 */
  // core_competence?: string;
  // /** 学段 */
  // grade_section_code?: string;
  // /** 学段名称 */
  // grade_section_name?: string;
  // /** 目标描述 */
  // goal_description?: string;
  // /** 目标优先级（数字越大越优先） */
  // priority?: number;
  // /** 目标重要性 */
  // importance?: string;
  // /** 课程目标类型 */
  // type?: string;
  // /** 学校id */
  // enterprise_id?: number;
}

@Table({
  tableName: 'standard_course_goal',
  timestamps: true,
  comment: '课程目标表',
})
export class StandardCourseGoal
  extends Model<StandardCourseGoalAttributes>
  implements StandardCourseGoalAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '名称',
  })
  name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '描述',
  })
  describe?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '解析',
  })
  analy?: string;

  // @ForeignKey(() => PlanCourseSubject)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '课程科目id',
  // })
  // course_id?: number;

  // @Column({
  //   type: DataType.STRING(64),
  //   allowNull: true,
  //   comment: '课程科目',
  // })
  // course_name?: string;

  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '父级目标ID（树形结构）',
  // })
  // parent_id?: number;

  // @ForeignKey(() => StandardCourseNature)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '课程性质ID',
  // })
  // course_nature_id?: number;

  // @ForeignKey(() => StandardCourseConcept)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '课程理念ID',
  // })
  // course_concept_id?: number;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '核心素养',
  // })
  // core_competence?: string;

  // @Column({
  //   type: DataType.STRING(64),
  //   allowNull: true,
  //   comment: '学段',
  // })
  // grade_section_code?: string;

  // @Column({
  //   type: DataType.STRING(64),
  //   allowNull: true,
  //   comment: '学段名称',
  // })
  // grade_section_name?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '目标描述',
  // })
  // goal_description?: string;

  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   defaultValue: 0,
  //   comment: '目标优先级（数字越大越优先）',
  // })
  // priority?: number;

  // @Column({
  //   type: DataType.ENUM('高', '中', '低'),
  //   allowNull: true,
  //   defaultValue: '中',
  //   comment: '目标重要性',
  // })
  // importance?: string;

  // @Column({
  //   type: DataType.ENUM('总目标', '科目目标'),
  //   allowNull: true,
  //   comment: '课程目标类型',
  // })
  // type?: string;

  // @ForeignKey(() => Enterprise)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '学校id',
  // })
  // enterprise_id?: number;

  // @BelongsTo(() => PlanCourseSubject, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // course?: PlanCourseSubject;

  // @BelongsTo(() => StandardCourseNature, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // course_nature?: StandardCourseNature;

  // @BelongsTo(() => StandardCourseConcept, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // course_concept?: StandardCourseConcept;

  // @BelongsTo(() => Enterprise, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // enterprise?: Enterprise;
}
