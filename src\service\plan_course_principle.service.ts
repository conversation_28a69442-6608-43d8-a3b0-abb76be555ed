import { Inject, Provide } from '@midwayjs/core';
import { PlanCoursePrinciple } from '../entity/plan_course_principle.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class PlanCoursePrincipleService extends BaseService<PlanCoursePrinciple> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课程方案原则');
  }
  getModel = () => {
    return PlanCoursePrinciple;
  };

  /**
   * 批量删除
   * @param ids 需要删除的id数组
   */
  async bulkDestroy(ids) {
    await PlanCoursePrinciple.destroy({
      where: {
        id: ids,
      },
    });
  }
}
