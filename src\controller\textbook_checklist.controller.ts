import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { TextbookRegistryService } from '../service/textbook_checklist.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { Textbook, TextbookChecklistAttributes } from '../entity';

@Controller('/textbook_checklist')
export class TextbookRegistryController {
  @Inject()
  ctx: Context;

  @Inject()
  service: TextbookRegistryService;

  @Get('/', { summary: '查询教材名录列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // textbook_version支持模糊查询
    if (queryInfo.textbook_version) {
      queryInfo.textbook_version = {
        [Op.like]: `%${queryInfo.textbook_version}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['id', 'ASC']],
      include: [Textbook],
    });
  }

  @Get('/:id', { summary: '按ID查询教材名录' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定教材名录');
    }
    return res;
  }

  @Post('/', { summary: '新增教材名录' })
  async create(@Body() info: TextbookChecklistAttributes) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新教材名录' })
  async update(
    @Param('id') id: number,
    @Body() body: Partial<TextbookChecklistAttributes>
  ) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除教材名录' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }
}
