import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { DictionaryService } from '../service/dictionary.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { DictionaryAttributes } from '../entity';

@Controller('/dictionaries')
export class DictionaryController {
  @Inject()
  ctx: Context;

  @Inject()
  service: DictionaryService;

  @Get('/', { summary: '查询字典列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // name支持模糊查询
    if (queryInfo.name) {
      queryInfo.name = {
        [Op.like]: `%${queryInfo.name}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [
        ['type', 'asc'],
        ['sortOrder', 'ASC'],
      ],
    });
  }

  @Get('/:code', { summary: '按code查询字典' })
  async show(@Param('code') code: string) {
    const res = await this.service.findOne({ code });
    if (!res) {
      throw new CustomError('未找到指定字典');
    }
    return res;
  }

  @Post('/', { summary: '新增字典' })
  async create(@Body() info: DictionaryAttributes) {
    const sortOrder = await this.service.getNextOrder(info.type);
    const res = await this.service.create({ ...info, sortOrder });
    return res;
  }

  @Put('/:code', { summary: '更新字典' })
  async update(@Param('code') code: string, @Body() body: any) {
    console.log('code', code);
    await this.service.update({ code }, body);
    return true;
  }

  @Del('/:code', { summary: '删除字典' })
  async destroy(@Param('code') code: string) {
    await this.service.delete({ code });
    return true;
  }
}
