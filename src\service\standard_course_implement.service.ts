import { Inject, Provide } from '@midwayjs/core';
import { StandardCourseImplement } from '../entity/standard_course_implement.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class StandardCourseImplementService extends BaseService<StandardCourseImplement> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课程实施');
  }
  getModel = () => {
    return StandardCourseImplement;
  };
}
