import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import {
  ComplianceDetectionDesign,
  ComplianceDetectionDesignQuestion,
  ComplianceDetectionDesignStruct,
} from '.';
import { DETECTION_TYPE, TEMPLATE_STATUS } from '../common/Constants';

export interface ComplianceDetectionDesignDetailAttributes {
  /** 主键 */
  id: number;
  /** 达标检测设计id */
  design_id: number;
  /** 教材名录id */
  textbookChecklist_id: number;
  /** 教材目录ids */
  catalogIds?: string;
  /** 教材目录 */
  catalogs?: string;
  /** 新目录ids */
  new_catalogIds?: string;
  /** 新目录名称 */
  new_catalogs?: string;
  /** 名称 */
  name?: string;
  /** 检测类型 */
  detection_type: string;
  /** 状态 */
  status: string;
  /** 作业时长 */
  duration?: number;
  design?: ComplianceDetectionDesign;
  designQuestions?: ComplianceDetectionDesignQuestion[];
  designStructs?: ComplianceDetectionDesignStruct[];
}

@Table({
  tableName: 'compliance_detection_design_detail',
  timestamps: true,
  comment: '达标检测设计详情表',
})
export class ComplianceDetectionDesignDetail
  extends Model<ComplianceDetectionDesignDetailAttributes>
  implements ComplianceDetectionDesignDetailAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @ForeignKey(() => ComplianceDetectionDesign)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '达标检测设计id',
  })
  design_id: number;

  @BelongsTo(() => ComplianceDetectionDesign, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  design?: ComplianceDetectionDesign;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材名录id',
  })
  textbookChecklist_id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '教材目录ids',
    get() {
      const newValue = this.getDataValue('catalogIds');
      return newValue ? newValue.split(',') : [];
    },
    set(value: string[]) {
      this.setDataValue('catalogIds', value.join(','));
    },
  })
  catalogIds?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '教材目录',
    get() {
      const newValue = this.getDataValue('catalogs');
      return newValue ? newValue.split(',') : [];
    },
    set(value: string[]) {
      this.setDataValue('catalogs', value.join(','));
    },
  })
  catalogs?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '新目录ids',
    get() {
      const newValue = this.getDataValue('new_catalogIds');
      return newValue ? newValue.split(',') : [];
    },
    set(value: string[]) {
      this.setDataValue('new_catalogIds', value.join(','));
    },
  })
  new_catalogIds?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '新目录名称',
    get() {
      const newValue = this.getDataValue('new_catalogs');
      return newValue ? newValue.split(',') : [];
    },
    set(value: string[]) {
      this.setDataValue('new_catalogs', value.join(','));
    },
  })
  new_catalogs?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '名称',
  })
  name?: string;

  @Column({
    type: DataType.ENUM(
      DETECTION_TYPE.UNIT,
      DETECTION_TYPE.INTERIM,
      DETECTION_TYPE.FINAL
    ),
    allowNull: false,
    defaultValue: DETECTION_TYPE.UNIT,
    comment: '检测类型',
  })
  detection_type: string;

  @Column({
    type: DataType.ENUM(TEMPLATE_STATUS.DRAFT, TEMPLATE_STATUS.PUBLISHED),
    allowNull: false,
    defaultValue: TEMPLATE_STATUS.DRAFT,
    comment: '状态',
  })
  status: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '作业时长',
  })
  duration?: number;

  @HasMany(() => ComplianceDetectionDesignQuestion)
  designQuestions?: ComplianceDetectionDesignQuestion[];

  @HasMany(() => ComplianceDetectionDesignStruct)
  designStructs?: ComplianceDetectionDesignStruct[];
}
