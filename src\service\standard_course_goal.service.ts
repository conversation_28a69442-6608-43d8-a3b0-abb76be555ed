import { Inject, Provide } from '@midwayjs/core';
import { StandardCourseGoal } from '../entity/standard_course_goal.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class StandardCourseGoalService extends BaseService<StandardCourseGoal> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课程目标');
  }
  getModel = () => {
    return StandardCourseGoal;
  };
}
