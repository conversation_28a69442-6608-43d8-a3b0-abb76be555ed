import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { PublisherService } from '../service/publisher.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';

@Controller('/publishers')
export class PublisherController {
  @Inject()
  ctx: Context;

  @Inject()
  service: PublisherService;

  @Get('/', { summary: '查询出版商列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      filter,
      sort,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // name支持模糊查询
    if (queryInfo.name) {
      queryInfo.name = {
        [Op.like]: `%${queryInfo.name}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      sort,
      filter,
      order: [['name', 'ASC']],
    });
  }

  @Get('/:id', { summary: '按ID查询出版商' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定出版商');
    }
    return res;
  }

  @Post('/', { summary: '新增出版商' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新出版商' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除出版商' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }
}
