import { Table, Column, Model, DataType } from 'sequelize-typescript';
// import { PlanCourseSubject, Enterprise } from '.';

export interface StandardCourseConceptAttributes {
  /** 主键ID */
  id: number;
  /** 名称 */
  name?: string;
  /** 描述 */
  describe?: string;
  /** 解析 */
  analy?: string;
  // /** 课程科目id */
  // course_id?: number;
  // /** 课程科目 */
  // course_name?: string;
  // /** 课程理念名称 */
  // concept_name?: string;
  // /** 课程育人功能 */
  // educational_function?: string;
  // /** 阶段性和发展型 */
  // stage_development?: string;
  // /** 时代典范 */
  // era_exemplar?: string;
  // /** 情境和实践 */
  // situation_practice?: string;
  // /** 过程和整体 */
  // process_holistic?: string;
  // /** 学校id */
  // enterprise_id?: number;
}

@Table({
  tableName: 'standard_course_concept',
  timestamps: true,
  comment: '课程理念表',
})
export class StandardCourseConcept
  extends Model<StandardCourseConceptAttributes>
  implements StandardCourseConceptAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质名称',
  })
  name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质描述',
  })
  describe?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质解析',
  })
  analy?: string;

  // @ForeignKey(() => PlanCourseSubject)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '课程科目id',
  // })
  // course_id?: number;

  // @Column({
  //   type: DataType.STRING(64),
  //   allowNull: true,
  //   comment: '课程科目',
  // })
  // course_name?: string;

  // @Column({
  //   type: DataType.STRING(255),
  //   allowNull: true,
  //   comment: '课程理念名称',
  // })
  // concept_name?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '课程育人功能',
  // })
  // educational_function?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '阶段性和发展型',
  // })
  // stage_development?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '时代典范',
  // })
  // era_exemplar?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '情境和实践',
  // })
  // situation_practice?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '过程和整体',
  // })
  // process_holistic?: string;

  // @ForeignKey(() => Enterprise)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '学校id',
  // })
  // enterprise_id?: number;

  // @BelongsTo(() => PlanCourseSubject, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // course?: PlanCourseSubject;

  // @BelongsTo(() => Enterprise, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // enterprise?: Enterprise;
}
