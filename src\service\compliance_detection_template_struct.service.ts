import { Inject, Provide } from '@midwayjs/core';
import {
  ComplianceDetectionTemplateQuestion,
  ComplianceDetectionTemplateStruct,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';

@Provide()
export class ComplianceDetectionTemplateStructService extends BaseService<ComplianceDetectionTemplateStruct> {
  @Inject()
  ctx: Context;

  constructor() {
    super('达标检测结构');
  }
  getModel = () => {
    return ComplianceDetectionTemplateStruct;
  };

  /**
   * 获取最大排序索引
   * @param detailId 达标检测范本详情id
   * @returns 最大排序索引
   */
  async getMaxIndex(detailId: number) {
    const maxIndex = await ComplianceDetectionTemplateStruct.max('sort_order', {
      where: {
        detail_id: detailId,
      },
    });
    return (maxIndex as number) || 0;
  }

  /**
   * 批量创建达标检测范本结构
   * @param detail_id 达标检测范本详情id
   * @param info 结构信息
   * @returns
   */
  async bulkCreate(detail_id: number, info: any) {
    const transaction =
      await ComplianceDetectionTemplateStruct.sequelize.transaction();
    try {
      // 删除对应达标检测范本结构
      await ComplianceDetectionTemplateStruct.destroy({
        where: { detail_id },
        transaction,
      });
      // 批量创建
      const data = info.map((item, index) => {
        return {
          ...item,
          detail_id,
          sort_order: index + 1,
        };
      });
      const res = await ComplianceDetectionTemplateStruct.bulkCreate(data, {
        transaction,
      });
      await transaction.commit();
      return res;
    } catch (error) {
      transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 清空
   * @param detail_id 达标检测范本详情id
   * @returns
   */
  async bulkDelete(detail_id: number) {
    const transaction =
      await ComplianceDetectionTemplateStruct.sequelize.transaction();
    try {
      if (!detail_id) {
        throw new CustomError('缺少必要参数');
      }
      // 清除对应课时作业结构
      await ComplianceDetectionTemplateStruct.destroy({
        where: {
          detail_id,
        },
        transaction,
      });
      // 清除对应课时作业下的题目
      await ComplianceDetectionTemplateQuestion.destroy({
        where: {
          detail_id,
        },
        transaction,
      });
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }
}
