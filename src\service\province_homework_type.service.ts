import { Inject, Provide } from '@midwayjs/core';
import { ProvinceHomeworkType } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class ProvinceHomeworkTypeService extends BaseService<ProvinceHomeworkType> {
  @Inject()
  ctx: Context;

  constructor() {
    super('省级作业类型');
  }
  getModel = () => {
    return ProvinceHomeworkType;
  };
}
