import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
  BelongsToMany,
} from 'sequelize-typescript';
import {
  KnowledgePoint,
  TextbookChecklist,
  TextbookCatalogKnowledgePoint,
} from './';

export interface TextbookCatalogAttributes {
  /** ID，主键 */
  id: number;
  /** 教材名录ID */
  textbookChecklist_id: number;
  /** 名称 */
  title: string;
  /** 父ID */
  parent_id?: number;
  /** 排序 */
  sort_order: number;
  textbookChecklist?: TextbookChecklist;
  parent?: TextbookCatalog;
  children?: TextbookCatalog[];
}

@Table({
  tableName: 'textbook_catalogs',
  timestamps: true,
  comment: '教材目录表',
})
export class TextbookCatalog
  extends Model<TextbookCatalogAttributes>
  implements TextbookCatalogAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'ID',
  })
  id: number;

  @ForeignKey(() => TextbookChecklist)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_textbookchecklist_parent_name',
      msg: '该教材同一目录下已存在相同名称的子目录',
    },
    comment: '教材ID',
  })
  textbookChecklist_id: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    unique: {
      name: 'unique_textbookchecklist_parent_name',
      msg: '该教材同一目录下已存在相同名称的子目录',
    },
    comment: '名称',
  })
  title: string;

  @ForeignKey(() => TextbookCatalog)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    unique: {
      name: 'unique_textbookchecklist_parent_name',
      msg: '该教材同一目录下已存在相同名称的子目录',
    },
    comment: '父ID',
  })
  parent_id?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '排序',
  })
  sort_order: number;

  @BelongsTo(() => TextbookChecklist, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  textbookChecklist?: TextbookChecklist;

  @BelongsTo(() => TextbookCatalog, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  parent?: TextbookCatalog;

  @HasMany(() => TextbookCatalog, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  children?: TextbookCatalog[];

  @BelongsToMany(() => KnowledgePoint, () => TextbookCatalogKnowledgePoint)
  knowledgePoints!: TextbookCatalogKnowledgePoint[];
}
