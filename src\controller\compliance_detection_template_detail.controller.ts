import {
  Body,
  Controller,
  Del,
  Fields,
  Files,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ComplianceDetectionTemplateDetailService } from '../service/compliance_detection_template_detail.service';
import { CustomError } from '../error/custom.error';
import { ComplianceDetectionTemplateQuestion } from '../entity';
import { UploadFileInfo, UploadMiddleware } from '@midwayjs/busboy';

@Controller('/compliance_detection_template_detail')
export class ComplianceDetectionTemplateDetailController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ComplianceDetectionTemplateDetailService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      include: [
        {
          model: ComplianceDetectionTemplateQuestion,
          order: [['sort_order', 'ASC']],
        },
      ],
    });
  }

  @Get('/:id', { summary: '根据ID查询单个' })
  async show(@Param('id') id: number) {
    const res = await this.service.show(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() body: any) {
    const { template_id } = body;
    if (!template_id) {
      throw new CustomError('缺少必要参数');
    }
    return this.service.create(body);
  }
  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async delete(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }

  @Post('/import', {
    middleware: [UploadMiddleware],
    summary: '导入达标范本作业',
  })
  async importComplianceDetails(
    @Files() files: Array<UploadFileInfo>,
    @Fields() fieldRecord: Record<string, string>
  ) {
    // 1. 验证用户登录信息
    const user = this.ctx.state?.user;
    if (!user) {
      throw new CustomError('缺少用户登录信息！');
    }
    if (!files.length) {
      throw new CustomError('请先选择文件！');
    }
    const file = files[0];
    const { fields } = fieldRecord;
    if (!fields) {
      throw new CustomError('缺少必要参数！');
    }
    const info = JSON.parse(fields);
    const { name, innerId, ...questionInfo } = info;
    if (!name) {
      throw new CustomError('缺少范本作业名称！');
    }
    if (!questionInfo.tableName) {
      throw new CustomError('未指定试题表！');
    }
    if (!questionInfo.subject) {
      throw new CustomError('未指定学科！');
    }
    return await this.service.importComplianceDetails(
      file.data,
      { ...questionInfo, catalog: questionInfo.catalog || questionInfo.volume },
      innerId
    );
  }
}
