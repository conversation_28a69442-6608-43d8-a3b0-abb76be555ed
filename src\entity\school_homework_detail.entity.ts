import {
  <PERSON><PERSON>s<PERSON>o,
  <PERSON>umn,
  <PERSON>Type,
  ForeignKey,
  HasMany,
  HasOne,
  Model,
  Table,
} from 'sequelize-typescript';
import { SchoolHomework, User, TextbookCatalog } from './';

export interface SchoolHomeworkDetailAttributes {
  id: number;
  name: string;
  describe: string;
  textbookCatalogId: number;
  pid: number;
  status: string;
  author: string;
  type: string;
  questionId: string;
  orderIndex: number;
  schoolHomeworkId: number;
  questionType: string;
  questionTypeName: string;
  parentQuestionId: string;
  createdAt: Date;
  updatedAt: Date;
  parent?: SchoolHomeworkDetail;
  children?: SchoolHomeworkDetail[];
}

@Table({
  tableName: 'school_homework_detail',
  timestamps: true,
  comment: '学校作业详情表',
  indexes: [
    {
      fields: ['schoolHomeworkId'],
    },
  ],
})
export class SchoolHomeworkDetail
  extends Model<SchoolHomeworkDetailAttributes>
  implements SchoolHomeworkDetailAttributes
{
  @Column({
    primaryKey: true,
    autoIncrement: true,
    type: DataType.INTEGER,
    comment: 'id',
  })
  id: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '描述',
  })
  describe: string;

  @ForeignKey(() => TextbookCatalog)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材目录id',
  })
  textbookCatalogId: number;
  @HasOne(() => TextbookCatalog, {
    foreignKey: 'id',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  textbookCatalog: TextbookCatalog;

  @ForeignKey(() => SchoolHomeworkDetail)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '父级id',
  })
  pid: number;
  @BelongsTo(() => SchoolHomeworkDetail, 'pid')
  parent: SchoolHomeworkDetail;
  @HasMany(() => SchoolHomeworkDetail, 'pid')
  children: SchoolHomeworkDetail[];

  @Column({
    type: DataType.ENUM('草稿', '已发布'),
    defaultValue: '草稿',
    allowNull: false,
    comment: '状态',
  })
  status: string;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    comment: '作者id',
  })
  author: string;
  @HasOne(() => User, {
    foreignKey: 'id',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  authUser: User;

  @Column({
    type: DataType.ENUM('小节', '大题', '小题'),
    comment: '类型',
  })
  type: string;

  @Column({
    type: DataType.STRING(64),
    comment: '题库小题id',
  })
  questionId: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '顺序',
  })
  orderIndex: number;

  @ForeignKey(() => SchoolHomework)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '学校作业id',
  })
  schoolHomeworkId: number;
  @BelongsTo(() => SchoolHomework, {
    foreignKey: 'id',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  schoolHomework: SchoolHomework;

  @Column({
    type: DataType.STRING(36),
    comment: '题目类型',
  })
  questionType: string;

  @Column({
    type: DataType.STRING(36),
    comment: '题目类型名称',
  })
  questionTypeName: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '小题父级题目id',
  })
  parentQuestionId: string;

  @Column({
    type: DataType.DATE,
    comment: '创建时间',
  })
  createdAt: Date;
  @Column({
    type: DataType.DATE,
    comment: '更新时间',
  })
  updatedAt: Date;
}
