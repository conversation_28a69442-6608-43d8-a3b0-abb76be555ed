import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { TextbookCatalog, TextbookTemp } from './';

export interface TextbookTempDetail {
  /** id */
  id: number;
  /** 题目名称 */
  name: string;
  /** 题目描述 */
  describe?: string;
  /** 教材目录id */
  textbook_catalog_id: number;
  /** 大题id */
  parent_id?: number;
  /** 状态 */
  status: string;
  /** 类型 */
  type: string;
  /** 试题库id */
  question_bank_id?: string;
  /** 排序 */
  orderIndex?: number;
  /** 教材模版id */
  textbook_temp_id: number;
  /** 试题类型编码 */
  question_type_code?: string;
  /** 试题类型 */
  question_type?: string;
  /** 小题的父题id */
  parent_question_id?: number;
  parent?: TextbookTempDetail;
  children?: TextbookTempDetail[];
}

@Table({
  tableName: 'textbook_temp_detail',
  timestamps: false,
  comment: '教材模版详情表',
})
export class TextbookTempDetail
  extends Model<TextbookTempDetail>
  implements TextbookTempDetail
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'id',
  })
  id: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '题目名称',
    field: 'name',
  })
  name: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '题目描述',
  })
  describe?: string;

  @ForeignKey(() => TextbookCatalog)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材目录id',
  })
  textbook_catalog_id: number;

  @BelongsTo(() => TextbookCatalog, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  textbook_catalog?: TextbookCatalog;

  @ForeignKey(() => TextbookTempDetail)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '大题id',
  })
  parent_id?: number;

  @Column({
    type: DataType.ENUM('草稿', '已发布'),
    allowNull: false,
    defaultValue: '草稿',
    comment: '状态',
  })
  status: string;

  @Column({
    type: DataType.ENUM('小节', '大题', '小题'),
    allowNull: false,
    comment: '类型',
  })
  type: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '试题库id',
  })
  question_bank_id?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '排序',
  })
  orderIndex?: number;

  @ForeignKey(() => TextbookTemp)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材模版id',
  })
  textbook_temp_id: number;

  @BelongsTo(() => TextbookTemp, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  textbook_temp?: TextbookTemp;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '试题类型编码',
  })
  question_type_code?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '试题类型',
  })
  question_type?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '小题的父题id',
  })
  parent_question_id?: number;

  @BelongsTo(() => TextbookTempDetail, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  parent?: TextbookTempDetail;

  @HasMany(() => TextbookTempDetail, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  children?: TextbookTempDetail[];
}
