import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import {
  TextbookChecklist,
  Subject,
  ClassWorkDetail,
  Enterprise,
  Textbook,
} from '.';
import { CREATE_TYPE, TEMPLATE_STATUS } from '../common/Constants';

export interface ClassWorkAttributes {
  /** id，主键 */
  id: number;
  /** 教材版本id */
  textbook_id: number;
  /** 教材名录ID */
  textbookChecklist_id: number;
  /** 课时作业范本名称 */
  name: string;
  /** 学段code */
  grade_section_code: string;
  /** 学段 */
  grade_section_name: string;
  /** 学科id */
  subject_id: number;
  /** 学科 */
  subject_name: string;
  /** 年级code */
  grade_code: string;
  /** 年级 */
  grade_name: string;
  /** 教材版本 */
  textbook_version: string;
  /** 册次 */
  volume: string;
  /** 来源code */
  source_code?: string;
  /** 来源 */
  source?: string;
  /** 企业id */
  enterprise_id?: number;
  /** 范本类型 */
  type?: string;
  /** 状态 */
  status: string;
  /** 创建人id */
  creator_id?: number;
  /** 创建人 */
  creator_name?: string;
  /** 当前范本id，用于学校引用系统内置范本的情况 */
  template_id?: number;
  /** 是否共享 */
  is_share: boolean;
  textbook?: Textbook;
  textbookChecklist?: TextbookChecklist;
  subject?: Subject;
  classWorkDetails?: ClassWorkDetail[];
  enterprise?: Enterprise;
}

@Table({ tableName: 'class_work', timestamps: true, comment: '课时作业范本表' })
export class ClassWork
  extends Model<ClassWorkAttributes>
  implements ClassWorkAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'id，主键',
  })
  id: number;

  @ForeignKey(() => Textbook)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材版本id',
  })
  textbook_id: number;

  @BelongsTo(() => Textbook, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  textbook?: Textbook;

  @ForeignKey(() => TextbookChecklist)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材名录ID',
  })
  textbookChecklist_id: number;

  @BelongsTo(() => TextbookChecklist, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  textbookChecklist?: TextbookChecklist;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '课时作业范本名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '学段code',
  })
  grade_section_code: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '学段',
  })
  grade_section_name: string;

  @ForeignKey(() => Subject)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '学科id',
  })
  subject_id: number;

  @BelongsTo(() => Subject, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  subject?: Subject;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '学科',
  })
  subject_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '年级code',
  })
  grade_code: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '年级',
  })
  grade_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '教材版本',
  })
  textbook_version: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '册次',
  })
  volume: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '来源code',
  })
  source_code?: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '来源',
  })
  source?: string;

  @ForeignKey(() => Enterprise)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '企业id',
  })
  enterprise_id?: number;

  @BelongsTo(() => Enterprise)
  enterprise?: Enterprise;

  @Column({
    type: DataType.ENUM(CREATE_TYPE.CUSTOM, CREATE_TYPE.BUILTIN),
    allowNull: false,
    defaultValue: CREATE_TYPE.CUSTOM,
    comment: '范本类型',
  })
  type?: string;

  @Column({
    type: DataType.ENUM(TEMPLATE_STATUS.DRAFT, TEMPLATE_STATUS.PUBLISHED),
    allowNull: false,
    defaultValue: TEMPLATE_STATUS.DRAFT,
    comment: '状态',
  })
  status: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '创建人id',
  })
  creator_id?: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '创建人',
  })
  creator_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '当前范本id',
  })
  template_id?: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否共享',
  })
  is_share: boolean;

  @HasMany(() => ClassWorkDetail)
  classWorkDetails?: ClassWorkDetail[];
}
