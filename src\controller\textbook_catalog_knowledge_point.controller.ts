import {
  Body,
  Controller,
  Get,
  Inject,
  Param,
  Post,
  // Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { TextbookCatalogKnowledgePointService } from '../service/textbook_catalog_knowledge_point.service';
// import { CustomError } from '../error/custom.error';

@Controller('/textbook_catalog_knowledge_point')
export class TextbookCatalogKnowledgePointServiceController {
  @Inject()
  ctx: Context;

  @Inject()
  service: TextbookCatalogKnowledgePointService;

  @Get('/:catalogId', { summary: '根据章节id获取对应知识点信息' })
  async index(@Param('catalogId') catalogId: number) {
    const res = await this.service.getList(catalogId);
    return res;
  }

  @Post('/bulkCreate/:catalogId', { summary: '批量创建课时知识点关联关系' })
  async bulkCreate(@Param('catalogId') catalogId: number, @Body() info: any) {
    const res = await this.service.bulkCreate(catalogId, info);
    return res;
  }
}
