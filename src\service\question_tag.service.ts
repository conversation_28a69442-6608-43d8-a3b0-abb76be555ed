import { Inject, Provide } from '@midwayjs/core';
import { QuestionTag } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { Op } from 'sequelize';

@Provide()
export class QuestionTagService extends BaseService<QuestionTag> {
  @Inject()
  ctx: Context;

  constructor() {
    super('类题标签');
  }
  getModel = () => {
    return QuestionTag;
  };
  /**
   * 查询列表
   * @param {*} query 查询条件
   * @param {*} offset 分页，跳过
   * @param {*} limit 分页，截取
   * @return {*} res
   * @memberof QuestionTagService
   */
  async getList(query: any, offset: any, limit: any) {
    const queryOption: any = {};
    const result: any = {};

    const hasPaging = offset !== undefined && limit !== undefined;
    if (hasPaging) {
      queryOption.offset = Number(offset) || 0;
      queryOption.limit = Number(limit) || 10;
    }

    if (query.name) {
      query.name = {
        [Op.like]: `%${query.name}%`,
      };
    }

    const where = {
      ...query,
      // 自定义查询参数参数
    };
    queryOption.where = where;
    queryOption.order = [['createdAt', 'DESC']];

    const res = await QuestionTag.findAll(queryOption);
    result.list = res;

    if (hasPaging) {
      // 获取总数
      const total = await QuestionTag.count({
        where,
        distinct: true,
      });
      result.total = total || 0;
    }
    return result;
  }

  /**
   * 根据条件查询
   * @param filter 查询条件
   * @returns 返回结果
   */
  async findByFilter(filter: any) {
    return await QuestionTag.findAll({
      where: filter,
    });
  }
}
