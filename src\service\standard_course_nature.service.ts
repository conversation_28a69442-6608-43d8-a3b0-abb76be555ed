import { Inject, Provide } from '@midwayjs/core';
import { StandardCourseNature } from '../entity/standard_course_nature.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class StandardCourseNatureService extends BaseService<StandardCourseNature> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课程性质');
  }
  getModel = () => {
    return StandardCourseNature;
  };
}
