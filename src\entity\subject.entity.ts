import { Table, Column, Model, DataType, HasMany } from 'sequelize-typescript';
import { KnowledgePoint, Textbook } from './';

export interface SubjectAttributes {
  /** ID，主键 */
  id: number;
  /** 学段 */
  grade_section: string;
  /** 学段名 */
  grade_section_name?: string;
  /** 学科 */
  subject: string;
  /** 封面 */
  cover?: string;
  /** 描述 */
  description?: string;
  /** 学科教材列表 */
  textbooks?: Textbook[];
  /** 知识点 */
  knowledgePoints?: KnowledgePoint[];
}

@Table({ tableName: 'subjects', timestamps: true, comment: '学科表' })
export class Subject
  extends Model<SubjectAttributes>
  implements SubjectAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'ID，主键',
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    unique: {
      name: 'unique_section_subject',
      msg: '同一学段已存在相同学科',
    },
    comment: '学段',
  })
  grade_section: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学段名',
  })
  grade_section_name?: string;

  @Column({
    type: DataType.STRING(50),
    unique: {
      name: 'unique_section_subject',
      msg: '同一学段已存在相同学科',
    },
    allowNull: false,
    comment: '学科',
  })
  subject: string;

  @Column({
    type: DataType.STRING(255),
    comment: '封面',
  })
  cover?: string;

  @Column({
    type: DataType.TEXT,
    comment: '描述',
  })
  description?: string;

  @HasMany(() => Textbook)
  textbooks?: Textbook[];

  @HasMany(() => KnowledgePoint)
  knowledgePoints?: KnowledgePoint[];
}
