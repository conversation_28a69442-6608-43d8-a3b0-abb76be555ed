import { Inject, Provide } from '@midwayjs/core';
import { TextbookChoose, TextbookChooseDetail } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';

@Provide()
export class TextbookChooseService extends BaseService<TextbookChoose> {
  @Inject()
  ctx: Context;

  constructor() {
    super('教材选用');
  }
  getModel = () => {
    return TextbookChoose;
  };

  async setItems(id: number, items: string[]) {
    const choose = await TextbookChoose.findByPk(id);
    if (!choose) {
      throw new CustomError('未找到指定信息');
    }
    await TextbookChooseDetail.destroy({ where: { textbook_choose_id: id } });
    await TextbookChooseDetail.bulkCreate(
      items.map(item => ({ textbook_choose_id: id, area_code: item }))
    );
  }
}
