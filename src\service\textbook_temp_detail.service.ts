import { Provide } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typegoose';
import { ReturnModelType } from '@typegoose/typegoose';
import { BaseService } from '../common/BaseService';
import { TextbookTempDetail, TextbookCatalog } from '../entity';
import { SystemQuestions } from '../model';

@Provide()
export class TextbookTempDetailService extends BaseService<TextbookTempDetail> {
  constructor() {
    super('TextbookTempDetail');
  }

  getModel() {
    return TextbookTempDetail;
  }

  @InjectEntityModel(SystemQuestions)
  systemQuestions: ReturnModelType<typeof SystemQuestions>;

  /**
   * 创建教材作业详情
   * @param data 数据
   * @param transaction 事务
   * @return res 返回创建结果
   */
  async create(data, transaction = null) {
    const res = TextbookTempDetail.create(data, { transaction });
    return res;
  }

  /**
   * 获取小节详情
   * @param id id
   * @return result 返回小节详情及其子项
   */
  async getDetailChildren(id) {
    const result = await TextbookTempDetail.findAll({
      where: {
        parent_id: id,
      },
      include: [{ model: TextbookTempDetail, as: 'children' }],
      order: [['orderIndex', 'ASC']],
    });
    return result;
  }

  /**
   * 获取最大排序索引
   * @param textbook_temp_id 模版id
   * @param textbook_catalog_id 教材目录id
   * @param type 排序类型，默认为 '小节'
   * @return {*} 返回最大排序索引
   */
  async getMaxOrderIndex(textbook_temp_id, textbook_catalog_id, type = '小节') {
    const maxIndex = await TextbookTempDetail.max('orderIndex', {
      where: {
        textbook_temp_id,
        textbook_catalog_id,
        type,
      },
    });
    // 使用类型断言确保返回值为 number 类型
    return (maxIndex as number) || 0;
  }

  /**
   * 批量创建题目
   * @param textbookTemp 教材作业模版
   * @param textbookTempDetail 教材作业模版详情
   * @param transaction 事务
   */
  async createBulk(textbookTemp, textbookTempDetail, transaction) {
    const { textbook_catalog_id: period_id, id: parent_id } =
      textbookTempDetail;

    const data = await TextbookCatalog.findByPk(period_id);
    const period = JSON.parse(JSON.stringify(data));
    console.log(period);
    console.log(parent_id);

    // 从题库中获取对应课时的试题列表
    const questionlist = await this.systemQuestions.aggregate([
      {
        $match: { period: period.title, soruce: textbookTemp.source },
      },
      { $sort: { createdAt: 1 } }, // 按照createdAt字段升序排序
    ]);
    console.log(questionlist);

    if (questionlist.length > 0) {
      // TODO:存入临时表
    }

    const periodQuestion = questionlist.filter(v => v.period === period.title);
    console.log('课时信息', periodQuestion);

    // 获取之前创建的题目
    const deleteList = await TextbookTempDetail.findAll({
      where: { parent_id: textbookTempDetail.id },
    });

    if (deleteList.length > 0) {
      await TextbookTempDetail.destroy({
        where: { id: deleteList.map(v => v.id) },
        transaction,
      });
    }
    // TODO:创建各题型关系中大题小题关系
  }

  /**
   * 发布/撤销小节
   * @param updateData 更新数据
   * @param id id
   * @return 返回更新后的数据
   */
  async updatePublish(updateData, id) {
    return TextbookTempDetail.update(updateData, {
      where: {
        id,
      },
    });
  }
}
