import { Body, Controller, Inject, Post } from '@midwayjs/core';
import { ImportService } from '../service/import.service';
import { CustomError } from '../error/custom.error';

@Controller('/import', {
  description: '数据导入',
})
export class ImportController {
  @Inject()
  service: ImportService;

  /**
   * 试题导入
   * @param files
   * @param fields
   * @returns
   */
  @Post('/subjects', {
    summary: '导入学科',
  })
  async importSubjects(@Body() body) {
    const { filePath } = body;
    if (!filePath) throw new CustomError('未找到导入文件');
    await this.service.importSubjects(body);
    return true;
  }
}
