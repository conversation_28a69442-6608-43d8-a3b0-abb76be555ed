import { Inject, Provide } from '@midwayjs/core';
import { CityHomeworkForm } from '../entity/city_homework_form.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class CityHomeworkFormService extends BaseService<CityHomeworkForm> {
  @Inject()
  ctx: Context;

  constructor() {
    super('市级作业设计形式');
  }
  getModel = () => {
    return CityHomeworkForm;
  };
}
