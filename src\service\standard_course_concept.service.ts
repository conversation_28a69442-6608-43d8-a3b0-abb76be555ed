import { Inject, Provide } from '@midwayjs/core';
import { StandardCourseConcept } from '../entity/standard_course_concept.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class StandardCourseConceptService extends BaseService<StandardCourseConcept> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课程理念');
  }
  getModel = () => {
    return StandardCourseConcept;
  };
}
