import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { PlanTrainTarget } from './plan_train_target.entity';

export interface PlanCoursePrincipleAttributes {
  /** 主键 */
  id: number;
  /** 基本原则名称 */
  name?: string;
  /** 基本原则描述 */
  describe?: string;
  /** 基本原则解析 */
  analy?: string;
  /** 培养目标id */
  target_id?: number;
  /** 培养目标名称 */
  target_name?: string;
  target?: PlanTrainTarget;
}

@Table({
  tableName: 'plan_course_principle',
  timestamps: true,
  comment: '课程方案原则表',
})
export class PlanCoursePrinciple
  extends Model<PlanCoursePrincipleAttributes>
  implements PlanCoursePrincipleAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '基本原则名称',
  })
  name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '基本原则描述',
  })
  describe?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '基本原则解析',
  })
  analy?: string;

  @ForeignKey(() => PlanTrainTarget)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '培养目标id',
  })
  target_id?: number;

  @BelongsTo(() => PlanTrainTarget, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  target?: PlanTrainTarget;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '培养目标名称',
  })
  target_name?: string;
}
