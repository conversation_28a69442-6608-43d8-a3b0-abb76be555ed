import { Inject, Provide } from '@midwayjs/core';
import { PlanCourseSystem } from '../entity/plan_course_system.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class PlanCourseSystemService extends BaseService<PlanCourseSystem> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课程学制');
  }
  getModel = () => {
    return PlanCourseSystem;
  };
}
