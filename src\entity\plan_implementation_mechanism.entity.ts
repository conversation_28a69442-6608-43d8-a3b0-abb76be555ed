import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { PlanTrainTarget, PlanCoursePrinciple, PlanCourseSetting } from '.';

export interface PlanImplementationMechanismAttributes {
  /** 主键 */
  id: number;
  /** 培养目标id */
  train_target_id?: number;
  /** 基本原则id */
  principle_id?: number;
  /** 课程设置id */
  course_setting_id?: number;
  /** 实施机制名称 */
  name?: string;
  /** 实施机制内容 */
  mechanism_content?: string;
  /** 实施开始日期 */
  start_date?: Date;
  /** 实施结束日期 */
  end_date?: Date;
  trainTarget?: PlanTrainTarget;
  principle?: PlanCoursePrinciple;
  courseSetting?: PlanCourseSetting;
}

@Table({
  tableName: 'plan_implementation_mechanism',
  timestamps: true,
  comment: '实施机制表',
})
export class PlanImplementationMechanism
  extends Model<PlanImplementationMechanismAttributes>
  implements PlanImplementationMechanismAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @ForeignKey(() => PlanTrainTarget)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '培养目标id',
  })
  train_target_id?: number;

  @BelongsTo(() => PlanTrainTarget, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  trainTarget?: PlanTrainTarget;

  @ForeignKey(() => PlanCoursePrinciple)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '基本原则id',
  })
  principle_id?: number;

  @ForeignKey(() => PlanCourseSetting)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '课程设置id',
  })
  course_setting_id?: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '实施机制名称',
  })
  name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '实施机制内容',
  })
  mechanism_content?: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '实施开始日期',
  })
  start_date: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    comment: '实施结束日期',
  })
  end_date?: Date;

  @BelongsTo(() => PlanTrainTarget, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  train_target?: PlanTrainTarget;

  @BelongsTo(() => PlanCoursePrinciple, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  principle?: PlanCoursePrinciple;

  @BelongsTo(() => PlanCourseSetting, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  courseSetting?: PlanCourseSetting;
}
