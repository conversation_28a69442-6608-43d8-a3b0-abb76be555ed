import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ComposerPaperDetailService } from '../service/composer_paper_detail.service';

@Controller('/composer_paper_detail', {
  description: '组卷详情',
})
export class ComposerPaperDetailController {
  @Inject()
  ctx: Context;
  @Inject()
  composerDetail: ComposerPaperDetailService;
  @Get('/', { description: '组卷详情列表' })
  async index(@Query() query) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;
    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.composerDetail.findAll({ query: queryInfo, offset, limit });
  }

  /**
   * GET /composer_detail/:id
   */
  @Get('/:id', { description: '组卷详情' })
  async show(@Param('id') id: string) {
    const res = await this.composerDetail.findById(id);
    return res;
  }

  /**
   * POST /composer_detail/edit
   */
  @Post('/edit', { description: '编辑组卷详情' })
  async edit(@Query() query, @Body() updateBody: any) {
    await this.composerDetail.update(query, updateBody);
    return true;
  }

  /**
   * POST /composer_detail
   */
  @Post('/', { description: '创建组卷详情' })
  async create(@Body() data: any) {
    const res = await this.composerDetail.create(data);
    return res;
  }

  /**
   * PUT /composer_detail/:id
   */
  @Put('/', { description: '更新组卷详情' })
  async update(@Param('id') id: string, @Body() updateBody: any) {
    await this.composerDetail.update({ id }, updateBody);
    return true;
  }

  /**
   * DELETE /composer_detail/:id
   */
  @Del('/:id', { description: '删除组卷详情' })
  async destroy(@Param('id') id: string) {
    await this.composerDetail.delete({ id });
    return true;
  }

  /**
   * 批量创建方案规则
   *
   * @returns Promise<void>
   */
  @Post('/bulkCreate', { description: '批量创建方案规则' })
  async bulkCreate(@Body() data: any) {
    const { composerPaperId, ...info } = data;
    // 获取现在最大的orderIndex
    const maxIndex = await this.composerDetail.getMaxOrderIndex(
      composerPaperId
    );
    info.forEach((item, i) => {
      item.orderIndex = maxIndex + i + 1;
    });
    const res = await this.composerDetail.bulkCreate(info, composerPaperId);
    return res;
  }
}
