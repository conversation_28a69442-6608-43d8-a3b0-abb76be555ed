import { HttpService } from '@midwayjs/axios';
import { Config, Inject, Logger, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { FileTransformService } from './file_transform.service';
import { QuestionService } from './question.service';
import { orderBy, pick } from 'lodash';
import { unlink } from 'fs/promises';
@Provide()
export class ExportService {
  @Inject()
  ctx: Context;
  @Logger()
  logger;

  @Config('fileTransform')
  transformConfig: any;
  @Inject()
  httpService: HttpService;
  @Inject()
  fileTransformService: FileTransformService;
  @Inject()
  questionService: QuestionService;

  /**
   * 导出作业试卷为word文档
   * @param name 作业名称
   * @param struct 作业结构
   * @param questions 作业试题
   * @param options 导出选项
   * @returns 导出路径
   */
  async exportPaperToWord(
    name: string,
    struct: any[],
    questions: any[],
    options: any
  ): Promise<{ wordFilePath: string; htmlFilePath: string }> {
    try {
      // 获取作业中的试题数据
      const jsonData = await this.getPaperData(name, struct, questions);
      // 将试题数据转换为HTML
      const htmlFilePath =
        await this.fileTransformService.jsonToHtmlByTemplateSave(
          jsonData,
          options
        );
      // 将HTML转换为Word文档
      const { baseURL, exportWord } = this.transformConfig;
      const response = await this.httpService.post(`${baseURL}${exportWord}`, {
        filePath: htmlFilePath,
        title: name,
      });
      const { code, message, data } = response.data; // 确保只返回可序列化的内容
      if (code !== 200) {
        await unlink(htmlFilePath);
        throw new CustomError(message);
      }
      // 返回临时文档路径
      return { wordFilePath: data.filePath, htmlFilePath };
    } catch (error) {
      this.logger.error(
        `Failed to execute exportPaperToWord, message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError('试卷导出失败，请稍后重试！');
    }
  }

  /**
   * 获取作业中的试题数据
   * @param name 作业名称
   * @param struct 作业结构
   * @param questions 作业试题
   * @returns 试题数据
   */
  async getPaperData(name: string, struct: any[], questions: any[]) {
    // 获取作业中的试题数据
    const questionsInfos = await this.questionService.findQuestions(questions);
    // 将questionsInfos和struct进行合并
    const mergedData = orderBy(struct, ['sort_order'], ['asc']).map(item => {
      return {
        type: item.name,
        questions: item.questionIds.map(questionId => {
          const question = questionsInfos.find(
            question => question._id.toString() === questionId
          );
          return pick(question, ['name', 'options', 'children']);
        }),
      };
    });
    return {
      name,
      infos: mergedData,
    };
  }
}
