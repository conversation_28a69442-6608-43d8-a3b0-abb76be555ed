import { Controller, Get, Inject, Query } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { AnalysisHomeworkRecordService } from '../service/analysis_homework_record.service';

@Controller('/analysis_homework_record')
export class AnalysisHomeworkRecordController {
  @Inject()
  ctx: Context;

  @Inject()
  service: AnalysisHomeworkRecordService;

  @Get('/statistic', { summary: '查询汇总数据' })
  async statistic(@Query() query: any) {
    return this.service.statistic(query);
  }

  @Get('/statistic/homework_analysis', { summary: '根据条件查询作业分析记录' })
  async statisticHomeworkAnalysis(@Query() query: any) {
    return this.service.statisticHomeworkAnalysis(query);
  }

  @Get('/statistic/month', { summary: '按月统计作业数量和试题数量' })
  async statisticMonth(@Query() query: any) {
    return this.service.statisticMonth(query);
  }

  @Get('/statistic/by_school', { summary: '统计学校作业数量和试题数量' })
  async statisticBySchool(@Query() query: any) {
    return this.service.statisticBySchool(query);
  }

  @Get('/auto_summary_homework_record', { summary: '自动汇总作业记录' })
  async autoSummaryHomeworkRecord() {
    return this.service.autoSummaryHomeworkRecord();
  }

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({ query: queryInfo, offset, limit });
  }
}
