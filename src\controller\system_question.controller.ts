import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { SystemQuestionService } from '../service/system_question.service';
import { CustomError } from '../error/custom.error';
import { Util } from '../common/Util';
import { isArray, includes } from 'lodash';
import { SimilarQuestionService } from '../service/similar_question.service';
import { QuestionService } from '../service/question.service';

@Controller('/system_question')
export class SystemQuestionController {
  @Inject()
  ctx: Context;

  @Inject()
  service: SystemQuestionService;

  @Inject()
  questionService: QuestionService;
  @Inject()
  similarQuestionService: SimilarQuestionService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      similar,
      calculate,
      order,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    const sort = order ? JSON.parse(order) : order;
    const result = await this.questionService.findQuestionsByFilter(
      queryInfo,
      Util.toInt(offset),
      Util.toInt(limit),
      sort
    );
    const { total, list } = result;
    const newList =
      Util.toBoolean(similar) || Util.toBoolean(calculate)
        ? await this.service.calculateCount(list, Util.toBoolean(calculate))
        : list;
    return { total, list: newList };
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: string) {
    const res = await this.questionService.showQuestion(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    const res = await this.questionService.create(info).catch(() => {
      throw new CustomError('新增试题失败，请稍后重试！');
    });
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.questionService.update(id, body).catch(() => {
      throw new CustomError('编辑试题失败，请稍后重试！');
    });
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: string) {
    await this.questionService.destroy(id).catch(() => {
      throw new CustomError('删除试题失败，请稍后重试！');
    });
    return true;
  }

  @Post('/associate', { summary: '创建父子题' })
  async createAssociateQuestion(@Body() body: any) {
    if (!body) {
      throw new CustomError('请先设置题目内容');
    }
    return await this.questionService.createAssociateQuestion(body);
  }

  @Post('/associate_update/:pid', { summary: '编辑父子题' })
  async updateAssociateQuestion(@Param('pid') pid: string, @Body() body: any) {
    if (!body) {
      throw new CustomError('请先设置题目内容');
    }
    return await this.questionService.updateAssociateQuestion(body, pid);
  }

  @Del('/associate/:pid', { summary: '根据父题id删除关联的子题' })
  async deleteAssociateQuestion(@Param('pid') pid: string) {
    if (!pid) {
      throw new CustomError('未指定要删除的题目');
    }
    await this.questionService.deleteQuestionByPid(pid);
    return true;
  }

  @Get('/get_count', { summary: '条件查询试题个数' })
  async findQuestionCountByFilter(@Query() query: any) {
    return await this.questionService.findQuestionCountByFilter(query);
  }

  @Del('/destroybulk', { summary: '批量删除' })
  async destroyBulk(@Body() body: any) {
    const { ids } = body;
    await this.questionService.bulkDestroyQuestions(ids);
    return true;
  }

  @Post('/updatebulk', { summary: '批量修改' })
  async updateBulk(@Body() body: any) {
    const { ids, data } = body;
    return await this.questionService.bulkUpdateQuestions(ids, data);
  }

  @Post('/list', { summary: '根据试题ids获取多个试题' })
  async getList(@Body() body: any) {
    const { ids } = body;
    const idsArr = [...new Set(ids)];
    const res = await this.questionService.findQuestionsByFilter(
      { idArray: idsArr },
      null,
      null,
      { createdAt: -1 }
    );
    return res;
  }

  @Post('/get_filter_ids', { summary: '获取可替换的试题' })
  async getFilterIds(@Query() query: any, @Body() body: any) {
    return await this.service.getFilterIds(body, query);
  }

  @Post('/get_ids', { summary: '批量获取小节中的题目' })
  async getIds(@Body() body: any) {
    return await this.service.getIds(body);
  }

  @Get('/question_banks_list', { summary: '关联相似题查询题目列表' })
  async getQuestionList(@Query() query: any) {
    const { offset, limit, ...queryInfo } = query;
    return await this.service.getQuestionList(queryInfo, offset, limit);
  }

  @Get('/default_similar_question/:questionId', { summary: '获取默认相似题' })
  async getDefaultSimilarQuestion(@Param('questionId') questionId: string) {
    if (!questionId) {
      throw new CustomError('请先指定题目');
    }
    return await this.service.getDefaultSimilarQuestion(questionId);
  }

  @Post('/similar_question_count', { summary: '相似题数量' })
  async similarQuestionCount(@Body() body: any) {
    const { searchList } = body;
    if (!searchList || !searchList.length) {
      throw new CustomError('请补全筛选条件');
    }
    const list = [];
    for (const item of searchList) {
      const { grade, subject, unit, baseType, type, points } = item;
      if (
        !grade ||
        grade === '' ||
        !subject ||
        subject === '' ||
        !unit ||
        unit === '' ||
        !baseType ||
        baseType === '' ||
        !type ||
        type === '' ||
        !points ||
        points === ''
      ) {
        throw new CustomError('请补全筛选条件');
      } else {
        let count = await this.service.similarQuestionCount(item);
        // 相似题最多返3个
        if (count > 3) {
          count = 3;
        }
        list.push({ count });
      }
    }
    return list;
  }

  @Post('/similar_question_detail1', {
    summary: '获取相似题详情/随机取3个，分组且返回html',
  })
  async similarQquestionDetail1(@Body() body: any) {
    const { grade, subject, period, baseType, type, points } = body;
    if (
      !grade ||
      grade === '' ||
      !subject ||
      subject === '' ||
      !period ||
      period === '' ||
      !baseType ||
      baseType === '' ||
      !type ||
      type === '' ||
      !points ||
      points === ''
    ) {
      throw new CustomError('请补全筛选条件');
    }
    const res = await this.service.similarQuestionDetail(body);
    const list = [];
    for (const item of res) {
      list.push({ baseType: item.baseType, type: item.type, arr: [item] });
    }
    const l = [];
    for (let index = 0; index < list.length; index++) {
      const item = list[index];
      const html =
        '<div  class="tm" style="line-height: 2.8"><style> .dotted-underline {position: relative;} .dotted-underline::after{position: absolute; bottom: -5px; /* 调整点的位置 */left: 50%; /* 居中点 */transform: translateX(-33%); /* 居中点 */width: 4px;height: 4px;border-radius: 50%; /* 创建圆点 */background-color: #333; /* 圆点颜色 */}</style><div class="additional">';
      let str = `<div class="tm" style="line-height: 2.8"><style> .dotted-underline {position: relative;}.dotted-underline::after {position: absolute;bottom: -5px; /* 调整点的位置 */left: 50%; /* 居中点 */transform: translateX(-33%); /* 居中点 */width: 4px;height: 4px;border-radius: 50%; /* 创建圆点 */background-color: #333; /* 圆点颜色 */}</style><div style="color: #888">${item.baseType}<div>`;
      let answer = html; //答案
      let pointstr = html; //知识点
      let analysis = html; //解析
      const ids = [];
      for (let j = 0; j < item.arr.length; j++) {
        const aitem = item.arr[j];
        const point = [];
        ids.push(aitem._id);
        for (let k = 0; k < aitem.points.length; k++) {
          const z = aitem.points[k];
          point.push(z);
        }

        const points = point.join('@');
        pointstr =
          pointstr +
          `<div class="zsd"><div style="vertical-align: middle">${
            j + 1
          } ${points}</div></div>`;
        analysis =
          analysis +
          `<div style="vertical-align: middle">(${j + 1}) ${
            aitem.analysis
          }</div>`;
        answer =
          answer +
          `<div class="da"><div style="vertical-align: middle; font-family: Times New Roman; color: #000000">(${
            j + 1
          }) ${aitem.answer}</div></div>`;
        str = str + `<div><div>(${j + 1})</div>${aitem.name}</div>`;
      }
      str = str + '<div>';
      answer = answer + '</div></div>';
      pointstr = pointstr + '<div></div>';
      l.push({ name: str, answer, points, analysis, id: ids.join(',') });
    }
    return l;
  }

  @Post('/similar_question_detail', { summary: '获取相似题详情' })
  async similarQquestionDetail(@Body() body: any) {
    const { grade, subject, unit, baseType, type, points } = body;
    if (
      !grade ||
      grade === '' ||
      !subject ||
      subject === '' ||
      !unit ||
      unit === '' ||
      !baseType ||
      baseType === '' ||
      !type ||
      type === '' ||
      !points ||
      points === ''
    ) {
      throw new CustomError('请补全筛选条件');
    }
    return await this.service.similarQuestionDetail(body);
  }

  // @Post('/similar_question_html', { summary: '获取相似题html' })
  // async goTohtml(@Body() body: any) {
  //   // TODO
  // }

  @Post('/similar_question_htmlbyIds', {
    summary: '根据相似题id获取相似题html片段',
  })
  async byIdsGoToHtml(@Body() body: any) {
    const { ids } = body;
    const idsArr = [...new Set(ids)];
    const res = await this.service.findAllByIds(idsArr);
    const list = [];
    for (const item of res) {
      list.push({ baseType: item.baseType, type: item.type, arr: [item] });
    }
    const l = Util.toHtml(list);
    return l;
  }

  // @Post('/open_similar_question', { summary: '对外开放查找相似题' })
  // async openSimilarQuestion(@Body() body: any) {
  //   // TODO
  // }

  // @Post('/open_similar_question_by_zj_dy', {
  //   summary: '获取教辅内指定的单元和课时的题',
  // })
  // async openSimilarQquestionByKsAndDy(@Body() body: any) {
  //   // TODO
  // }

  @Get('/question/list', { summary: '选择章节获取题目' })
  async openIndex(@Query() query: any) {
    const { offset, limit, ...queryInfo } = query;
    const { total, list } = await this.service.index(queryInfo, offset, limit);
    return { total, list };
  }

  @Get('/question_similar', { summary: '根据题号获取相似题' })
  async openGetSimilarQuestions(@Query() query: any) {
    const { ids, count } = query; //count表示返回总数
    if (!ids) {
      throw new CustomError('未指定题目id');
    }
    return await this._openGetSimilarQuestions(ids, count);
  }

  /**
   * 根据题号获取相似题
   * 先推手动，无手动推送默认
   */
  async _openGetSimilarQuestions(ids, count) {
    const returnCount = count ? parseInt(count) : 3;
    const idsArr = isArray(ids)
      ? ids
      : ids.includes(',')
      ? ids.split(',')
      : [ids];

    // 获取手动相似题，如果没有手动相似题，则获取默认相似题
    const { list } = await this.similarQuestionService.getList(
      {
        questionId: idsArr,
      },
      0,
      returnCount
    );

    let res = [];
    if (list.length) {
      const similarIds = list
        .map(item => [
          item.toJSON().similarQuestionId,
          item.toJSON().questionId,
        ])
        .flat();
      // 过滤出不在idsArr数组中的id
      const tempIds = similarIds.filter(id => !includes(idsArr, id));
      res = await this.service.findAllByIds(tempIds);
    } else {
      // 获取默认相似题
      res = await this.service.getDefaultSimilarQuestions(idsArr, returnCount);
    }
    return res;
  }

  @Post('/similar_question_html/basic', {
    summary: '根据试题id获取相似题html片段',
  })
  async openGetHtmlByIds(@Body() body: any) {
    const { ids, count } = body;
    // 查询给定题目的相似题
    const questionInfoArrs = await this._openGetSimilarQuestions(ids, count);
    const list = questionInfoArrs.map(item => {
      return { baseType: item.baseType, type: item.type, arr: [item] };
    });
    const l = Util.toHtml(list);
    return l;
  }

  // @Post('/open_api/similar_question_html', {
  //   summary: '根据试题id获取完整相似题html',
  // })
  // async openGetHtml(@Body() body: any) {
  //   // TODO
  // }

  @Get('/question_info', { summary: '获取试题详情' })
  async openGetQuestionInfo(@Query() query: any) {
    const { id } = query;
    if (!id) {
      throw new CustomError('未指定题目id');
    }
    return await this.service.openGetQuestionInfo(id);
  }

  @Post('/question_similar/by_rule', {
    summary: '根据相似题匹配规则推送相似题',
  })
  async openGetSimilarQuestionsByRule(@Body() body: any) {
    const { ids, count, enterpriseCode, semester } = body;
    if (!ids) {
      throw new CustomError('未指定题目id');
    }
    return await this._openGetSimilarQuestionsByRule(
      ids,
      count,
      enterpriseCode,
      semester
    );
  }

  /**
   * 获取相似题
   * @param ids 题目ids
   * @param count 推送相似题数量
   * @param enterpriseCode 学校code
   * @param semester 学年学期信息
   */
  async _openGetSimilarQuestionsByRule(ids, count, enterpriseCode, semester) {
    const idsArr = isArray(ids)
      ? ids
      : ids.includes(',')
      ? ids.split(',')
      : [ids];

    // 获取手动相似题，如果存在，优先推送手动相似题，如果没有推送默认相似题
    const { list } = await this.similarQuestionService.getList(
      {
        questionId: idsArr,
      },
      0,
      count
    );
    let res = [];
    if (list.length) {
      // 获取手动相似题
      const similarIds = list
        .map(item => [
          item.toJSON().similarQuestionId,
          item.toJSON().questionId,
        ])
        .flat();
      const tempIds = similarIds.filter(id => !includes(idsArr, id));
      res = await this.service.findAllByIds(tempIds);
    } else {
      // 获取默认相似题
      res = await this.service.getDefaultSimilarQuestionsByRule(
        idsArr,
        count,
        enterpriseCode,
        semester
      );
    }
    return res;
  }

  @Post('/similar_question_html/basic/by_rule', {
    summary: '根据相似题推送规则获取相似题html片段',
  })
  async openGetHtmlByIdsAndRule(@Body() body: any) {
    const { ids, count, enterpriseCode, semester } = body;
    // 查询给定题目的相似题
    const questionInfoArrs = await this._openGetSimilarQuestionsByRule(
      ids,
      count,
      enterpriseCode,
      semester
    );
    const list = questionInfoArrs.map(item => {
      return { baseType: item.baseType, type: item.type, arr: [item] };
    });
    const l = Util.toHtml(list);
    return l;
  }

  // @Post('/similar_question_html/by_rule', {
  //   summary: '根据试题id获取完整相似题html',
  // })
  // async openGetHtmlByRule(@Body() body: any) {
  //   //TODO
  // }
}
