import { Column, DataType, Model, Table } from 'sequelize-typescript';

export interface SimilarQuestionAttributes {
  id: number;
  questionId: string;
  similarQuestionId: string;
  orderIndex: number;
  createdAt: Date;
  updatedAt: Date;
}
@Table({
  tableName: 'similar_question',
  timestamps: true,
  comment: '手动关联的相似题关系表',
})
export class SimilarQuestion
  extends Model<SimilarQuestionAttributes>
  implements SimilarQuestionAttributes
{
  @Column({
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
    type: DataType.INTEGER,
  })
  id: number;
  @Column({
    comment: '关联的题目id',
    type: DataType.STRING(32),
  })
  questionId: string;
  @Column({
    comment: '相似的题目id',
    type: DataType.STRING(32),
  })
  similarQuestionId: string;
  @Column({
    comment: '排序',
    type: DataType.INTEGER,
    allowNull: false,
  })
  orderIndex: number;
  @Column({
    comment: '创建时间',
    type: DataType.DATE,
  })
  createdAt: Date;
  @Column({
    comment: '更新时间',
    type: DataType.DATE,
  })
  updatedAt: Date;
}
