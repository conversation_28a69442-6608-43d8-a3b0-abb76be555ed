import { App, Inject, InjectClient, Provide } from '@midwayjs/core';
import {
  GetSSOTokenParams,
  RefreshSSOTokenParams,
  SSOToken,
  SSO_Config,
  SSOUserInfo,
} from '../interface';
import { Application } from '@midwayjs/koa';
import { HttpServiceFactory, HttpService } from '@midwayjs/axios';
import { JwtService } from '@midwayjs/jwt';
import { CustomError } from '../error/custom.error';

@Provide()
export class SsoService {
  @App()
  app: Application;

  @InjectClient(HttpServiceFactory, 'sso')
  ssoAxios: HttpService;

  @Inject()
  jwtService: JwtService;

  async getToken(code: string, redirect_uri: string) {
    const conf = this.app.getConfig('sso') as SSO_Config;
    const postdata: GetSSOTokenParams = {
      grant_type: 'authorization_code',
      code,
      client_id: conf.AppID,
      client_secret: conf.AppSecret,
      redirect_uri: redirect_uri,
    };
    const tokenResponse = await this.ssoAxios.post<{
      errCode: number;
      msg?: string;
      data?: SSOToken;
    }>('/oauth/token', postdata);
    const { errCode, msg, data } = tokenResponse.data;
    if (errCode !== 0) {
      console.log(tokenResponse);
      throw new CustomError(msg || '获取令牌失败');
    }
    return data;
  }

  async refreshToken(refresh_token: string) {
    const conf = this.app.getConfig('sso') as SSO_Config;
    const postdata: RefreshSSOTokenParams = {
      grant_type: 'refresh_token',
      refresh_token,
      client_id: conf.AppID,
      client_secret: conf.AppSecret,
    };
    const tokenResponse = await this.ssoAxios.post<{
      errCode: number;
      msg?: string;
      data?: SSOToken;
    }>('/oauth/token', postdata);
    const { errCode, msg, data } = tokenResponse.data;
    if (errCode !== 0) {
      console.log(tokenResponse);
      throw new CustomError(msg || '刷新令牌失败');
    }
    return data;
  }

  /**
   * 使用访问令牌获取用户信息
   *
   * @param {string} access_token
   * @return {string} res
   * @memberof SsoService
   */
  async getUser(access_token: string) {
    const userInfoResponse = await this.ssoAxios.get<{
      errCode: number;
      msg?: string;
      data?: SSOUserInfo;
    }>('/oauth/userinfo', {
      headers: {
        Authorization: `Bearer ${access_token}`,
      },
    });
    const { errCode, msg, data } = userInfoResponse.data;
    if (errCode !== 0) {
      console.log(userInfoResponse);
      throw new CustomError(msg || '获取用户信息失败');
    }
    return data;
  }

  async logout(access_token: string) {
    return await this.ssoAxios.get<SSOUserInfo>('/oauth/logout', {
      params: {
        access_token,
      },
    });
  }
}
