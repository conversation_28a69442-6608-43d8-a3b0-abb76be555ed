import { Inject, Provide } from '@midwayjs/core';
import { QuestionExtendType } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class QuestionExtendTypeService extends BaseService<QuestionExtendType> {
  @Inject()
  ctx: Context;

  constructor() {
    super('扩展题型');
  }
  getModel = () => {
    return QuestionExtendType;
  };
}
