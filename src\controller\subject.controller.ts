import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { SubjectService } from '../service/subject.service';
import { CosService } from '../service/cos.service';
import { SubjectAttributes } from '../entity';
import { Op } from 'sequelize';

@Controller('/subjects')
export class SubjectController {
  @Inject()
  ctx: Context;

  @Inject()
  service: SubjectService;

  @Inject()
  cosService: CosService;

  @Get('/', { summary: '查询学科列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      filter,
      sort,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // subject支持模糊查询
    if (queryInfo.subject) {
      queryInfo.subject = {
        [Op.like]: `%${queryInfo.subject}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      filter,
      sort,
      order: [['id', 'ASC']],
    });
  }

  @Get('/:id', { summary: '按ID查询学科' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定学科');
    }
    return res;
  }

  @Post('/', { summary: '新增学科' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新学科' })
  async update(
    @Param('id') id: number,
    @Body() body: Partial<SubjectAttributes>
  ) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除学科' })
  async destroy(@Param('id') id: number) {
    // 这里需要先记录内容中的所有图片和视频，学科删除成功后，再删除cos中对应的图片和视频
    const subject = await this.service.findById(id);
    if (!subject) {
      throw new CustomError('未找到指定学科');
    }

    // 提取封面图片的 URL
    const imgUrlList = subject.cover ? subject.cover.split(',') : [];

    // 提取描述中的图片和视频的 src 属性
    const descriptionImages = (
      subject.description.match(/<img.*?src="([^"]+)".*?>/g) || []
    )
      .map(imgTag => imgTag.match(/src="([^"]+)"/)?.[1])
      .filter(src => src);

    const descriptionVideos = (
      subject.description.match(/<video.*?src="([^"]+)".*?<\/video>/g) || []
    )
      .map(videoTag => videoTag.match(/src="([^"]+)"/)?.[1])
      .filter(src => src);

    console.log([...imgUrlList, ...descriptionImages, ...descriptionVideos]);

    await this.service.delete({ id });

    const keys = [...imgUrlList, ...descriptionImages, ...descriptionVideos]
      .filter(item => item && item.includes('myqcloud.com/'))
      .map(item => item.split('myqcloud.com/')[1]);
    keys.forEach(key => {
      this.cosService.deleteObject(key);
    });
    return true;
  }
}
