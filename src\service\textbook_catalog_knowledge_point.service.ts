import { Inject, Provide } from '@midwayjs/core';
import { TextbookCatalogKnowledgePoint } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';

@Provide()
export class TextbookCatalogKnowledgePointService extends BaseService<TextbookCatalogKnowledgePoint> {
  @Inject()
  ctx: Context;

  constructor() {
    super('知识点教材目录关联');
  }
  getModel = () => {
    return TextbookCatalogKnowledgePoint;
  };

  /**
   * 根据课时id获取关联知识点数据
   * @param catalogId 课时id
   */
  async getList(catalogId) {
    const res = await TextbookCatalogKnowledgePoint.findAll({
      where: { catalogId },
    });
    const pointIds = res.map(item => item.pointId);
    return pointIds;
  }

  /**
   * 批量创建知识点教材目录关联数据
   * @param catalogId 章节id
   * @param info 知识点id
   * @returns
   */
  async bulkCreate(catalogId, info) {
    const transaction =
      await TextbookCatalogKnowledgePoint.sequelize.transaction();
    try {
      //先删除原有数据
      await TextbookCatalogKnowledgePoint.destroy({ where: { catalogId } });

      // 组装数据
      const data = info.map(pointId => ({
        catalogId,
        pointId: pointId,
      }));

      // 批量插入数据
      const res = await TextbookCatalogKnowledgePoint.bulkCreate(data, {
        transaction,
      });
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 根据课时id数组获取关联知识点数据
   * @param catalogId 课时id
   */
  async getListByCatalogIds(catalogIds: number[]) {
    const res = await TextbookCatalogKnowledgePoint.findAll({
      where: { catalogId: { [Op.in]: catalogIds } },
    });
    const pointIds = res.map(item => item.pointId);
    return pointIds;
  }
}
