import {
  Table,
  Column,
  Model,
  DataType,
  HasMany,
  Index,
} from 'sequelize-typescript';
import { User } from './';

export interface EnterpriseAttributes {
  /** 单位ID */
  id: number;
  /** 单位编号 */
  code: string;
  /** 单位名称 */
  name: string;
  /** 英文名称 */
  en_name?: string;
  /** 学制 */
  school_system?: string;
  /** 学制名称 */
  school_system_name?: string;
  /** LOGO */
  logo?: string;
  /** 单位类型 */
  type?: string;
  /** 省份编号 */
  province?: string;
  /** 省份名称 */
  province_name?: string;
  /** 城市编号 */
  city?: string;
  /** 城市名称 */
  city_name?: string;
  /** 区域编号 */
  area?: string;
  /** 区域名称 */
  area_name?: string;
  /** 联系人 */
  liaison?: string;
  /** 联系电话 */
  mobile?: string;
  /** 地址 */
  address?: string;
  /** 社会信用代码 */
  social_credit_code?: string;
  /** 官网地址 */
  official_website?: string;
  /** 邮箱地址 */
  email?: string;
  /** 传真地址 */
  fax?: string;
  /** 邮政编码 */
  postal_code?: string;
  /** 单位简介 */
  describe?: string;
  /** 备注信息 */
  remark?: string;
  /** 父级ID，集团校使用 */
  parent_id?: string;
  users?: User[];
}

@Table({ tableName: 'enterprises', timestamps: true, comment: '单位表' })
export class Enterprise
  extends Model<EnterpriseAttributes>
  implements EnterpriseAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '单位ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '单位编号',
  })
  @Index({ name: 'code', unique: true })
  code: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '单位名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(200),
    allowNull: true,
    comment: '英文名称',
  })
  en_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学制',
  })
  school_system: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学制名称',
  })
  school_system_name?: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: 'LOGO',
  })
  logo: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '单位类型',
  })
  type: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '省编码',
  })
  province: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '省名称',
  })
  province_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '市编码',
  })
  city: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '市名称',
  })
  city_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '区县编码',
  })
  area: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '区县名称',
  })
  area_name: string;

  @Column({
    type: DataType.STRING(32),
    allowNull: true,
    comment: '联系人',
  })
  liaison: string;

  @Column({
    type: DataType.STRING(32),
    allowNull: true,
    comment: '联系电话',
  })
  mobile: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '地址',
  })
  address: string;

  @Column({
    type: DataType.STRING(200),
    allowNull: true,
    comment: '统一社会信用代码',
  })
  social_credit_code: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '官网地址',
  })
  official_website: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '邮箱地址',
  })
  email: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '传真地址',
  })
  fax: string;

  @Column({
    type: DataType.STRING(10),
    allowNull: true,
    comment: '邮政编码',
  })
  postal_code: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '单位简介',
  })
  describe: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '备注信息',
  })
  remark: string;

  @Column({
    type: DataType.CHAR(36),
    allowNull: true,
    comment: '父级ID，集团校使用',
  })
  parent_id: string;

  @HasMany(() => User)
  users: User[];
}
