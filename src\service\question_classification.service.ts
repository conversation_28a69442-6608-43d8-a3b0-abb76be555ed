import { Inject, Provide } from '@midwayjs/core';
import { QuestionClassification } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { Op } from 'sequelize';
import { CustomError } from '../error/custom.error';

@Provide()
export class QuestionClassificationService extends BaseService<QuestionClassification> {
  @Inject()
  ctx: Context;

  constructor() {
    super('试题分类');
  }
  getModel = () => {
    return QuestionClassification;
  };

  /**
   * 批量删除
   * @param ids
   */
  async bulkDestroy(ids: number[]) {
    const transaction = await QuestionClassification.sequelize.transaction();
    try {
      await QuestionClassification.destroy({
        where: {
          id: { [Op.in]: ids },
        },
        transaction,
      });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw new CustomError('批量删除分类数据失败！');
    }
  }
}
