import { Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { User } from '../entity/user.entity';
import { Enterprise } from '../entity/enterprise.entity';

@Provide()
export class DashboardService {
  @Inject()
  ctx: Context;

  /**
   * 获取系统统计概览数据
   * @param query 查询条件
   * @returns 统计数据
   */
  async getOverview(query: any = {}) {
    const { area_code, city_code, province_code, enterprise_type } = query;

    // 构建企业查询条件
    const enterpriseWhere: any = {};
    if (province_code) {
      enterpriseWhere.province = province_code;
    }
    if (city_code) {
      enterpriseWhere.city = city_code;
    }
    if (area_code) {
      enterpriseWhere.area = area_code;
    }
    if (enterprise_type) {
      enterpriseWhere.type = enterprise_type;
    }

    // 构建用户查询条件
    const userWhere: any = {};

    // 如果有地区或企业类型条件，需要通过关联查询
    let userInclude = undefined;
    if (Object.keys(enterpriseWhere).length > 0) {
      userInclude = [
        {
          model: Enterprise,
          where: enterpriseWhere,
          attributes: [],
        },
      ];
    }

    // 并行查询统计数据
    const [enterpriseCount, userCount] = await Promise.all([
      Enterprise.count({ where: enterpriseWhere }),
      User.count({
        where: userWhere,
        include: userInclude,
        distinct: true,
      }),
    ]);

    return {
      enterprise_count: enterpriseCount,
      user_count: userCount,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取企业统计数据
   * @param query 查询条件
   * @returns 企业统计数据
   */
  async getEnterpriseStatistics(query: any = {}) {
    const { area_code, city_code, province_code, type } = query;

    const where: any = {};
    if (province_code) {
      where.province = province_code;
    }
    if (city_code) {
      where.city = city_code;
    }
    if (area_code) {
      where.area = area_code;
    }
    if (type) {
      where.type = type;
    }

    // 总数统计
    const total = await Enterprise.count({ where });

    // 按类型分组统计
    const typeStats = await Enterprise.findAll({
      where,
      attributes: [
        'type',
        [Enterprise.sequelize.fn('COUNT', Enterprise.sequelize.col('id')), 'count'],
      ],
      group: ['type'],
      raw: true,
    });

    // 按地区分组统计
    const areaStats = await Enterprise.findAll({
      where,
      attributes: [
        'province_name',
        'city_name',
        'area_name',
        [Enterprise.sequelize.fn('COUNT', Enterprise.sequelize.col('id')), 'count'],
      ],
      group: ['province_name', 'city_name', 'area_name'],
      raw: true,
    });

    return {
      total,
      by_type: typeStats,
      by_area: areaStats,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 获取用户统计数据
   * @param query 查询条件
   * @returns 用户统计数据
   */
  async getUserStatistics(query: any = {}) {
    const {
      area_code,
      city_code,
      province_code,
      enterprise_type,
      grade_section_code,
      subject_id,
      is_active
    } = query;

    // 构建用户查询条件
    const userWhere: any = {};
    if (grade_section_code) {
      userWhere.grade_section_code = grade_section_code;
    }
    if (subject_id) {
      userWhere.subject_id = subject_id;
    }
    if (is_active !== undefined) {
      userWhere.isActive = is_active;
    }

    // 构建企业关联查询条件
    const enterpriseWhere: any = {};
    if (province_code) {
      enterpriseWhere.province = province_code;
    }
    if (city_code) {
      enterpriseWhere.city = city_code;
    }
    if (area_code) {
      enterpriseWhere.area = area_code;
    }
    if (enterprise_type) {
      enterpriseWhere.type = enterprise_type;
    }

    const include = Object.keys(enterpriseWhere).length > 0 ? [
      {
        model: Enterprise,
        where: enterpriseWhere,
        attributes: [],
      },
    ] : undefined;

    // 总数统计
    const total = await User.count({
      where: userWhere,
      include,
      distinct: true,
    });

    // 按学段分组统计
    const gradeStats = await User.findAll({
      where: userWhere,
      include,
      attributes: [
        'grade_section_name',
        [User.sequelize.fn('COUNT', User.sequelize.col('User.id')), 'count'],
      ],
      group: ['grade_section_name'],
      raw: true,
    });

    // 按学科分组统计
    const subjectStats = await User.findAll({
      where: userWhere,
      include,
      attributes: [
        'subject_name',
        [User.sequelize.fn('COUNT', User.sequelize.col('User.id')), 'count'],
      ],
      group: ['subject_name'],
      raw: true,
    });

    // 按状态分组统计
    const statusStats = await User.findAll({
      where: userWhere,
      include,
      attributes: [
        'isActive',
        [User.sequelize.fn('COUNT', User.sequelize.col('User.id')), 'count'],
      ],
      group: ['isActive'],
      raw: true,
    });

    return {
      total,
      by_grade_section: gradeStats,
      by_subject: subjectStats,
      by_status: statusStats,
      timestamp: new Date().toISOString(),
    };
  }
}
