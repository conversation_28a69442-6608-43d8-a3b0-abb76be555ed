export type SSO_Config = {
  AppID: string;
  AppSecret: string;
  Host: string;
  state: string;
};

export type GetSSOTokenParams = {
  /**
   * 授权类型，例如 `authorization_code`
   */
  grant_type: string;
  /**
   * 授权码
   */
  code: string;
  /**
   * 重定向 URI
   */
  redirect_uri: string;
  /**
   * 客户端标识符
   */
  client_id: string;
  /**
   * 客户端密钥
   */
  client_secret: string;
};

export type RefreshSSOTokenParams = {
  /**
   * 授权类型，例如 `refresh_token`
   */
  grant_type: string;
  /**
   * 刷新令牌
   */
  refresh_token: string;
  /**
   * 客户端标识符
   */
  client_id: string;
  /**
   * 客户端密钥
   */
  client_secret: string;
};

export type SSOToken = {
  /**
   * 访问令牌
   */
  access_token: string;
  /**
   * 令牌类型（通常为 `Bearer`）
   */
  token_type: string;
  /**
   * 令牌有效期（秒）
   */
  expires_in: number;
  /**
   * 刷新令牌
   */
  refresh_token: string;
};

export type SSOUserInfo = {
  id: string;
  username: string;
  email: string;
  avatar: string;
};

/**
 * 排序参数
 */
export interface Order {
  [key: string]: 1 | -1 | number;
}
/**
 * 分页参数
 */
export interface Page {
  offset: number;
  limit: number;
}

/**
 * mongodb字段筛选参数
 */
export interface Projection {
  [key: string]:
    | 1
    | -1
    | {
        [key: string]: string;
      };
}
