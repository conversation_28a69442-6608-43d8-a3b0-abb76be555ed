import { Inject, Provide } from '@midwayjs/core';
import { PlanTeachingTime } from '../entity/plan_teaching_time.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class PlanTeachingTimeService extends BaseService<PlanTeachingTime> {
  @Inject()
  ctx: Context;

  constructor() {
    super('教学时间');
  }
  getModel = () => {
    return PlanTeachingTime;
  };
}
