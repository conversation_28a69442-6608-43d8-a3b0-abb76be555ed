import { Table, Column, Model, DataType } from 'sequelize-typescript';
import { CHECK_STATUS } from '../common/Constants';

export interface QuestionCheckRecordAttributes {
  /** 主键ID */
  id: number;
  /** 学段code */
  gradeSectionCode: string;
  /** 学段名称 */
  gradeSectionName: string;
  /**学科ID */
  subjectId: number;
  /** 学科名称 */
  subjectName: string;
  /** 题型code */
  questionTypeCode: string;
  /** 题型名称 */
  questionTypeName: string;
  /** 题目难度code */
  questionDifficultyCode: string;
  /** 题目难度 */
  questionDifficultyName: string;
  /** 创建人ID */
  authorId: number;
  /** 创建人名称 */
  authorName: string;
  /** 提交时间 */
  commitAt: Date;
  /** 审核人名称 */
  checker?: string;
  /** 审核人ID */
  checkerId?: number;
  /** 试题ID */
  questionBankId: string;
  /** 审核状态 */
  checkStatus: CHECK_STATUS;
  /** 修改意见 */
  suggestion?: string;
  /** 企业id */
  enterpriseCode: string;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({
  tableName: 'question_check_record',
  timestamps: true,
  comment: '试题审核记录表',
})
export class QuestionCheckRecord
  extends Model<QuestionCheckRecordAttributes>
  implements QuestionCheckRecordAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
    field: 'id',
  })
  id: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '学段code',
    field: 'grade_section_code',
  })
  gradeSectionCode: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '学段名称',
    field: 'grade_section_name',
  })
  gradeSectionName: string;
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '学科ID',
    field: 'subject_id',
  })
  subjectId: number;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '学科名称',
    field: 'subject_name',
  })
  subjectName: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '题型code',
    field: 'question_type_code',
  })
  questionTypeCode: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '题型名称',
    field: 'question_type_name',
  })
  questionTypeName: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '题目难度code',
    field: 'question_difficulty_code',
  })
  questionDifficultyCode: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '题目难度',
    field: 'question_difficulty_name',
  })
  questionDifficultyName: string;
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '创建人ID',
    field: 'author_id',
  })
  authorId: number;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '创建人名称',
    field: 'author_name',
  })
  authorName: string;
  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '提交时间',
    field: 'commit_at',
  })
  commitAt: Date;
  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '审核人名称',
    field: 'checker',
  })
  checker?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '审核人ID',
    field: 'checker_id',
  })
  checkerId?: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '试题ID',
    field: 'question_bank_id',
  })
  questionBankId: string;

  @Column({
    type: DataType.ENUM(
      CHECK_STATUS.DEFAULT,
      CHECK_STATUS.PASSED,
      CHECK_STATUS.REJECTED
    ),
    allowNull: false,
    defaultValue: CHECK_STATUS.DEFAULT,
    comment: '审核结果',
    field: 'check_status',
  })
  checkStatus: CHECK_STATUS;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '修改意见',
    field: 'suggestion',
  })
  suggestion?: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '企业code',
    field: 'enterprise_code',
  })
  enterpriseCode: string;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '创建时间',
    field: 'created_at',
  })
  createdAt?: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '更新时间',
    field: 'updated_at',
  })
  updatedAt?: Date;
}
