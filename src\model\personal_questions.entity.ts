import { Index, ModelOptions, prop } from '@typegoose/typegoose';
import { BaseObject } from './reference_type';
import { Types } from 'mongoose';
/**
 * 个人题库
 */
@ModelOptions({
  schemaOptions: {
    collection: 'personal_questions',
    timestamps: true,
  },
})
@Index({ catalog: 1 })
@Index({ pid: 1 })
@Index({ points: 1 })
@Index({ enterpriseCode: 1, author: 1 })
export class PersonalQuestions {
  /**
   * 名称，必填，最大长度为 32，题干
   */
  @prop({ required: true })
  name: string;

  /**
   * 选项
   */
  @prop()
  options: any[];

  /**
   * 试题难度
   */
  @prop({ required: true, _id: false })
  difficulty: BaseObject;

  /**
   * 年级
   */
  @prop({ _id: false })
  grade: BaseObject;

  /**
   * 教材版本
   */
  @prop({ _id: false })
  textbookVersion: BaseObject;

  /**
   * 学段
   */
  @prop({ _id: false })
  gradeSection: BaseObject;

  /**
   * 学科
   */
  @prop({ _id: false })
  subject: BaseObject;

  /**
   * 单元
   */
  @prop({ _id: false })
  unit: BaseObject;

  /**
   * 课时
   */
  @prop({ _id: false })
  period: BaseObject;

  /**
   * 最底层节点
   */
  @prop({ _id: false })
  catalog: BaseObject;

  /**
   * 册次
   */
  @prop({ _id: false })
  volume: BaseObject;

  /**
   * 基础题型
   */
  @prop({ required: true, _id: false })
  baseType: BaseObject;

  /**
   * 试题扩展题型
   */
  @prop({ _id: false })
  type: BaseObject;

  /**
   * 作者
   */
  @prop({ _id: false })
  author: BaseObject;

  /**
   * 来源
   */
  @prop({ _id: false })
  source: BaseObject;

  /**
   * 年份，最大长度为 64
   */
  @prop()
  year: string;

  /**
   * 题目分层
   */
  @prop({ _id: false })
  tier: BaseObject;

  /**
   * 认知层次text
   */
  @prop({ _id: false })
  cognitiveHierarchy: BaseObject;

  /**
   * 核心素养text
   */
  @prop({ _id: false })
  coreQuality: BaseObject;

  /**
   * 考察能力text
   */
  @prop({ _id: false })
  investigationAbility: BaseObject;

  /**
   * 知识点
   */
  @prop({ type: () => [BaseObject], _id: false })
  points: BaseObject[];

  /**
   * 类题标签
   */
  @prop({ type: () => [BaseObject], _id: false })
  tags: BaseObject[];

  /**
   * 答案，必填
   */
  @prop()
  answer: string;

  /**
   * 解析
   */
  @prop()
  analysis: string;

  /**
   * 页码
   */
  @prop()
  pagination: number;

  /**
   * 父题干
   */
  @prop()
  parentStem: string;

  /**
   * 父题干ID，最大长度为 64
   */
  @prop({ index: 1, default: null, ref: 'PersonalQuestions' })
  pid: Types.ObjectId;

  /**
   * 是否是组合题
   */
  @prop({ default: false })
  isCompose: boolean;

  /**
   * 来源试题表
   */
  @prop()
  sourceTable: string;

  /**
   * 来源试题id
   */
  @prop()
  sourceId: string;

  /**
   * 企业code
   */
  @prop()
  enterpriseCode: string;

  /**
   * 是否共享
   */
  @prop({ default: false })
  isShared: boolean;

  /**
   * 审核状态
   */
  @prop()
  checkStatus: string;

  /**
   * 分类
   */
  @prop({ _id: false })
  classification: BaseObject;

  /**
   * 所在表名称，最大长度为 64
   */
  @prop({ default: 'personal' })
  tableName: string;

  /** 试题提交时间 */
  @prop()
  commitAt: Date;

  /**
   * 审核意见
   */
  @prop()
  suggestion: string;

  /**
   * 建议时长，单位秒
   */
  @prop()
  duration: number;

  /**
   *  是否导入
   */
  @prop({ default: false })
  isImported: boolean;

  /**
   * 是否生效
   */
  @prop({ default: true })
  isEffective: boolean;

  /**
   * 试题题干
   */
  @prop()
  stem: string;
  /**
   * 创建时间，默认为当前时间
   */
  @prop({ default: Date.now })
  createdAt: Date;

  /**
   * 更新时间，默认为当前时间
   */
  @prop({ default: Date.now })
  updatedAt: Date;
}
