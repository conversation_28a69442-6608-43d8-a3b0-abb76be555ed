import { Inject, Provide } from '@midwayjs/core';
import { ExcludeSimilarQuestion } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class ExcludeSimilarQuestionService extends BaseService<ExcludeSimilarQuestion> {
  @Inject()
  ctx: Context;

  constructor() {
    super('手动排除的相似题关系');
  }
  getModel = () => {
    return ExcludeSimilarQuestion;
  };

  /**
   * 设置排除的关联题目
   * @param {*} data 保存的数据集合
   * @param {string} questionId 题目id
   */
  async setExcludeQuestion(data: any, questionId: string) {
    // 删除之前的题目排除关联信息 重新保存
    const count = await ExcludeSimilarQuestion.count({
      where: { questionId },
    });
    if (count) {
      await ExcludeSimilarQuestion.destroy({
        where: { questionId },
      });
    }
    if (data && data.length > 0) {
      await ExcludeSimilarQuestion.bulkCreate(data);
    }
  }
}
