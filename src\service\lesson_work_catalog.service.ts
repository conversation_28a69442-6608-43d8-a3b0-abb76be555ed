import { Inject, Provide } from '@midwayjs/core';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import {
  LessonWorkCatalog,
  LessonWorkDesignDetail,
  LessonWorkDesignStruct,
  LessonWorkDesignQuestion,
  LessonWorkCatalogAttributes,
} from '../entity';
import { randomUUID } from '@midwayjs/core/dist/util/uuid';
import { Op } from 'sequelize';

@Provide()
export class LessonWorkCatalogService extends BaseService<LessonWorkCatalog> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课时作业目录');
  }
  getModel = () => {
    return LessonWorkCatalog;
  };

  /**
   * 复制教材目录信息及作业信息到课时作业目录中
   * @param sourceData 源目录数据
   * @param lessonWorkDesign_id 课时作业设计id
   * @param transaction 事务
   * @param classWorkDetail 课时范本作业详情 可不传 不传直接复制目录
   */
  async copyCatalog(
    sourceData,
    lessonWorkDesign_id,
    transaction = null,
    classWorkDetail = []
  ) {
    const idMapping = {};

    // 分离一级目录和子目录
    const firstLevelItems = sourceData.filter(item => item.parent_id === null);
    const childrenItems = sourceData.filter(item => item.parent_id !== null);

    // 定义通用的目录插入逻辑
    const insertCatalog = async (items, parentMapping = {}) => {
      for (const item of items) {
        const catalogInfo = {
          old_id: item.id, //原本的目录id
          old_title: item.title, //原本的目录名称
          lessonWorkDesign_id, //课时作业设计id
          textbookChecklist_id: item.textbookChecklist_id, //教材名录id
          title: item.title, //目录名称
          sort_order: item.sort_order, //排序
          parent_id: parentMapping[item.parent_id] || null, // 确保 parent_id 存在
        };

        const caltalog = await LessonWorkCatalog.create(catalogInfo, {
          transaction,
        });
        idMapping[item.id] = caltalog.id;

        // 如果课时作业详情信息存在，则复制课时作业详情信息
        if (classWorkDetail && classWorkDetail.length > 0) {
          // 根据目录信息获取课时作业详情
          const classWorkDetailArr = classWorkDetail.filter(
            v => v.textbookCatalog_id === item.id
          );
          if (classWorkDetailArr.length > 0) {
            for (const detail of classWorkDetailArr) {
              const { questions, structs, ...info } = detail.toJSON();
              // 复制课时作业信息
              const detailInfo = {
                design_id: lessonWorkDesign_id, //作业设计id
                catalog_id: caltalog.id, //作业设计目录id
                name: info.name, //作业名称
                sort_order: info.sort_order, //排序
                status: info.status, //状态 草稿 发布
              };
              const designDetail = await LessonWorkDesignDetail.create(
                detailInfo,
                { transaction }
              );
              // 批量复制课时作业结构信息
              if (structs && structs.length > 0) {
                const structData = structs.map(struct => ({
                  id: struct.id
                    ? `${struct.id}_${designDetail.id}`
                    : `hidden_${randomUUID()}`, //结构id
                  designDetail_id: designDetail.id, //作业设计详情id
                  name: struct.name, //结构名称
                  questionIds: struct.questionIds, //题目ids
                  sort_order: struct.sort_order, //排序
                }));
                await LessonWorkDesignStruct.bulkCreate(structData, {
                  transaction,
                });
              }
              // 批量复制课时作业题目信息
              if (questions && questions.length > 0) {
                const questionData = questions.map(question => ({
                  designDetail_id: designDetail.id, //作业设计详情id
                  question_id: question.question_id, //题目id
                  sort_order: question.sort_order, //排序
                  source_table: question.source_table, //题目来源表
                }));
                await LessonWorkDesignQuestion.bulkCreate(questionData, {
                  transaction,
                });
              }
            }
          }
        }
      }
    };

    // 插入第一层级的数据
    await insertCatalog(firstLevelItems);
    // 插入子目录的数据
    await insertCatalog(childrenItems, idMapping);
  }

  /**
   * 复制教材目录信息及作业信息到课时作业目录中 优化逻辑
   * @param sourceData 源目录数据
   * @param lessonWorkDesign_id 课时作业设计id
   * @param transaction 事务
   * @param classWorkDetail 课时范本作业详情 可不传 不传直接复制目录
   */
  async copyCatalogNew(
    sourceData,
    lessonWorkDesign_id,
    transaction = null,
    classWorkDetail = []
  ) {
    const idMapping = {};

    // 分离一级目录和子目录
    const firstLevelItems = sourceData.filter(item => item.parent_id === null);
    const childrenItems = sourceData.filter(item => item.parent_id !== null);

    // 定义通用的目录插入逻辑
    const insertCatalog = async (items, parentMapping = {}) => {
      const catalogData = items.map(item => ({
        old_id: item.id, //原本目录id
        old_title: item.title, //原本的目录名称
        lessonWorkDesign_id, //课时作业设计id
        textbookChecklist_id: item.textbookChecklist_id, //教材名录id
        title: item.title, //目录名称
        sort_order: item.sort_order, //排序
        parent_id: parentMapping[item.parent_id] || null, // 确保 parent_id 存在
      }));
      const catalogsRes = await LessonWorkCatalog.bulkCreate(catalogData, {
        transaction,
      });
      // 更新映射关系
      catalogsRes.forEach((catalog, index) => {
        idMapping[items[index].id] = catalog.id;
      });
    };

    // 插入第一层级的数据
    await insertCatalog(firstLevelItems);
    // 插入子目录的数据
    await insertCatalog(childrenItems, idMapping);

    if (classWorkDetail && classWorkDetail.length > 0) {
      // 批量创建作业详情信息
      const detailData = [];
      classWorkDetail.forEach(detail => {
        const newCatalogId = idMapping[detail.textbookCatalog_id];
        if (newCatalogId) {
          detailData.push({
            design_id: lessonWorkDesign_id, //作业设计id
            catalog_id: newCatalogId, //作业设计目录id
            name: detail.name, //作业名称
            sort_order: detail.sort_order, //排序
            status: detail.status, //状态 草稿 发布
            duration: detail.duration, //作业时长
          });
        }
      });
      const detailsRes = await LessonWorkDesignDetail.bulkCreate(detailData, {
        transaction,
      });

      // 创建detailIdMapping，用于后续的试题和结构信息创建
      const detailIdMapping = {};
      detailsRes.forEach((detail, index) => {
        const originalDetailId = classWorkDetail[index].id;
        detailIdMapping[originalDetailId] = detail.id;
      });

      // 批量创建试题信息和结构信息
      const questionData = [];
      const structData = [];
      classWorkDetail.forEach(detail => {
        const newDetailId = detailIdMapping[detail.id];
        if (newDetailId) {
          const { questions, structs } = detail.toJSON();
          if (questions && questions.length > 0) {
            questions.forEach(question => {
              questionData.push({
                designDetail_id: newDetailId, //作业设计详情id
                question_id: question.question_id, //题目id
                sort_order: question.sort_order, //排序
                source_table: question.source_table, //题目来源表
              });
            });
          }

          if (structs && structs.length > 0) {
            structs.forEach(struct => {
              structData.push({
                id: struct.id
                  ? `${struct.id}_${newDetailId}`
                  : `hidden_${randomUUID()}`, //结构id
                designDetail_id: newDetailId, //作业设计详情id
                name: struct.name, //结构名称
                questionIds: struct.questionIds, //题目ids
                sort_order: struct.sort_order, //排序
              });
            });
          }
        }
      });
      await LessonWorkDesignQuestion.bulkCreate(questionData, {
        transaction,
      });
      await LessonWorkDesignStruct.bulkCreate(structData, { transaction });
    }
  }

  /**
   * 插入节点
   * @param id 参照节点id
   * @param name 新节点名称
   * @param old_id 旧节点id，如果是根节点则为null，主要用于查询题库
   * @param old_title 旧节点名称，如果是根节点则为null，主要用于查询题库
   * @param action 操作类型，insert表示插入到参照节点前，append表示插入到参照节点后
   * @returns 新节点
   */
  async insert(
    id: number,
    title: string,
    old_id: number | null,
    old_title: string,
    action: 'insert' | 'append'
  ) {
    const catalogInfo = await LessonWorkCatalog.findOne({
      where: {
        id,
      },
    });
    if (!catalogInfo) {
      throw new Error('参照节点不存在');
    }
    const sort_order: number =
      action === 'append' ? catalogInfo.sort_order + 1 : catalogInfo.sort_order;
    const info: Omit<LessonWorkCatalogAttributes, 'id'> = {
      /** 教材名录ID */
      textbookChecklist_id: catalogInfo.textbookChecklist_id,
      lessonWorkDesign_id: catalogInfo.lessonWorkDesign_id,
      /** 名称 */
      title,
      /** 父ID */
      parent_id: catalogInfo.parent_id,
      old_id,
      old_title,
      /** 排序 */
      sort_order,
    };

    console.log(info);
    // 查询相同排序有没有被占用
    const isExist = await LessonWorkCatalog.findOne({
      where: {
        textbookChecklist_id: catalogInfo.textbookChecklist_id,
        lessonWorkDesign_id: catalogInfo.lessonWorkDesign_id,
        parent_id: catalogInfo.parent_id,
        sort_order,
      },
      attributes: ['id'],
    });
    if (isExist) {
      // 占用了，需要把后面的所有节点的排序都+1
      const todoList: LessonWorkCatalog[] = await LessonWorkCatalog.findAll({
        where: {
          textbookChecklist_id: catalogInfo.textbookChecklist_id,
          parent_id: catalogInfo.parent_id,
          sort_order: {
            [Op.gte]: sort_order,
          },
        },
      });
      for (const todo of todoList) {
        todo.sort_order = todo.sort_order + 1;
        await todo.save();
      }
    }
    const newCatalogInfo = await LessonWorkCatalog.create(info);
    return newCatalogInfo;
  }
}
