import { Inject, Provide } from '@midwayjs/core';
import { BaseService } from '../common/BaseService';
import { CustomError } from '../error/custom.error';
import {
  TextbookTemp,
  TextbookTempDetail,
  Textbook,
  Subject,
  SchoolHomework,
  HomeworkDetailTemplateMapping,
} from '../entity';
import { SystemQuestionService } from './system_question.service';
import { Op } from 'sequelize';

@Provide()
export class TextbookTempService extends BaseService<TextbookTemp> {
  constructor() {
    super('TextbookTemp');
  }

  getModel() {
    return TextbookTemp;
  }

  @Inject()
  systemQuestionService: SystemQuestionService;

  /**
   * 获取列表
   * @param query 请求参数
   * @param offset 分页，跳过
   * @param limit 分页，截取
   * @return result
   */
  async getList(query, offset, limit) {
    const queryOption: any = {};
    const result: { list: any[]; total?: number } = { list: [] };

    const hasPaging = offset !== undefined || limit !== undefined;
    if (hasPaging) {
      queryOption.offset = Number(offset) || 0;
      queryOption.limit = Number(limit) || 10;
    }

    const where = {
      ...query,
    };

    queryOption.where = where;
    queryOption.include = [{ model: Textbook, include: [{ model: Subject }] }];

    const res = await TextbookTemp.findAll(queryOption);
    result.list = res;
    if (hasPaging) {
      const total = await TextbookTemp.count({
        where,
      });
      result.total = total || 0;
    }
    return result;
  }

  /**
   * 查询单个
   * @param id id
   */
  async get(id) {
    const result = await TextbookTemp.findOne({
      where: { id },
      include: [{ model: Textbook, include: [{ model: Subject }] }],
    });
    return result;
  }

  /**
   * 删除范本
   * @param id id
   */
  async destroy(id) {
    try {
      // 检查是否有校本作业使用该范本
      const hasSchoolHomework = await this.checkSchoolHomeworkUsage(id);
      if (hasSchoolHomework) {
        throw new CustomError('该范本已被校本作业引用，无法删除');
      }

      // 获取范本下所有小题和他们的id
      const questions = await TextbookTempDetail.findAll({
        where: { textbook_temp_id: id },
        type: '小题',
        attributes: ['id', 'question_bank_id', 'status'],
      });
      if (questions.length > 0) {
        // 获取所有小题对应的题目id，并检查是否被其他范本或校本作业引用
        const allQuestionIds = await this.getQuestionIdsAndCheckReference(
          questions
        );
        // 删除小题
        if (allQuestionIds.length > 0) {
          await this.systemQuestionService.destroyByIds(
            allQuestionIds,
            'textbook'
          );
        }
      }
      // 删除范本
      await TextbookTemp.destroy({ where: { id } });
    } catch (error) {
      console.error(
        `Failed to execute destroy textbookTemp, message is:${error.message}`
      );
      throw new Error(error);
    }
  }

  /**
   * 检查校本作业是否使用该范本
   * @param id 范本id
   * @return {boolean} 是否有校本作业使用
   */
  async checkSchoolHomeworkUsage(id) {
    const hasSchoolHomework = await SchoolHomework.findOne({
      where: { templateHomeworkId: id },
    });
    return !!hasSchoolHomework;
  }

  /**
   *
   * @param questions 范本详情小题列表
   * @return {string[]} 所有小题id
   */
  async getQuestionIdsAndCheckReference(questions) {
    const allQuestionIds = [];
    const tempDetailIds = [];
    const promises = [];
    questions.forEach(item => {
      const { id, question_bank_id, status } = item.dataValues;
      tempDetailIds.push(id);
      if (question_bank_id) {
        allQuestionIds.push(question_bank_id);
        promises.push(
          this.systemQuestionService.findByFilter(
            { _id: question_bank_id },
            { deleted: 1 },
            'textbook',
            status
          )
        );
      }
    });

    try {
      if (promises.length === 0) return allQuestionIds;
      const importQuestions = await Promise.all(promises);
      const importQuestionIds = importQuestions.flat().map(item => item._id);
      const isReferenced = await this.checkQuestionReferences(
        tempDetailIds,
        importQuestionIds
      );
      if (isReferenced) {
        throw new CustomError('该范本中有题目已经被其他作业使用，无法删除');
      }
      return allQuestionIds.filter(item => item);
    } catch (error) {
      throw new CustomError(error);
    }
  }

  /**
   * 检查导入的小题是否被校本作业引用
   * @param tempDetailIds 范本详情小题ID列表
   * @param importQuestionIds 所有导入的小题题目Id列表
   * @return {boolean} 是否有引用
   */
  async checkQuestionReferences(tempDetailIds, importQuestionIds) {
    const otherAbstracts = await HomeworkDetailTemplateMapping.findOne({
      where: {
        tempDetailQuestionId: { [Op.in]: importQuestionIds },
        templateDetailId: { [Op.notIn]: tempDetailIds },
      },
    });
    return !!otherAbstracts;
  }
}
