import { Column, DataType, Model, Table } from 'sequelize-typescript';

export interface QuestionClassificationAttributes {
  /** 主键 */
  id: number;
  /** 分类名称 */
  name: string;
  /** 分类描述 */
  description: string;
  /** 企业编码 */
  enterpriseCode: string;
  /** 企业名称 */
  enterpriseName: string;
  /** 是否内置 */
  isBuiltIn: boolean;
  createdAt: Date;
  updatedAt: Date;
}

@Table({
  tableName: 'question_classification',
  timestamps: true,
  comment: '题目分类表',
})
export class QuestionClassification
  extends Model<QuestionClassificationAttributes>
  implements QuestionClassificationAttributes
{
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '名称',
  })
  name: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '描述',
  })
  description: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '企业编码',
    field: 'enterprise_code',
  })
  enterpriseCode: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '企业名称',
    field: 'enterprise_name',
  })
  enterpriseName: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否内置',
    field: 'is_built_in',
  })
  isBuiltIn: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '创建时间',
    field: 'created_at',
  })
  createdAt: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '更新时间',
    field: 'updated_at',
  })
  updatedAt: Date;
}
