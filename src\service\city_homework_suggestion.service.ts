import { Inject, Provide } from '@midwayjs/core';
import { CityHomeworkSuggestion } from '../entity/city_homework_suggestion.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class CityHomeworkSuggestionService extends BaseService<CityHomeworkSuggestion> {
  @Inject()
  ctx: Context;

  constructor() {
    super('市级作业设计建议');
  }
  getModel = () => {
    return CityHomeworkSuggestion;
  };
}
