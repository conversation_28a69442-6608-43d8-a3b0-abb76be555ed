import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Subject } from '.';

export interface PlanEduRuleAttributes {
  /** 主键 */
  id: number;
  /** 学段 */
  grade_section?: string;
  /** 科目id */
  subject_id?: number;
  /** 科目 */
  subject_name?: string;
  /** 周课时下限 */
  min_weekly?: number;
  /** 周课时上限 */
  max_weekly?: number;
  subject?: Subject;
}

@Table({
  tableName: 'plan_edu_rule',
  timestamps: true,
  comment: '教育规则表',
})
export class PlanEduRule
  extends Model<PlanEduRuleAttributes>
  implements PlanEduRuleAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学段',
  })
  grade_section?: string;

  @ForeignKey(() => Subject)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '科目id',
  })
  subject_id?: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '科目',
  })
  subject_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '周课时下限',
  })
  min_weekly?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '周课时上限',
  })
  max_weekly?: number;

  @BelongsTo(() => Subject, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  subject?: Subject;
}
