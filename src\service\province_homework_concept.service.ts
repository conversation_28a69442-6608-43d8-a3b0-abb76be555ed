import { Inject, Provide } from '@midwayjs/core';
import { ProvinceHomeworkConcept } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class ProvinceHomeworkConceptService extends BaseService<ProvinceHomeworkConcept> {
  @Inject()
  ctx: Context;

  constructor() {
    super('省级作业设计理念');
  }
  getModel = () => {
    return ProvinceHomeworkConcept;
  };
}
