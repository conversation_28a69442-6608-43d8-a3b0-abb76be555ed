import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Logger,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { QuestionTagService } from '../service/question_tag.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';

@Controller('/question_tag')
export class QuestionTagController {
  @Inject()
  ctx: Context;

  @Inject()
  service: QuestionTagService;

  @Logger()
  logger;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;
    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.getList(queryInfo, offset, limit);
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    const { name, ...restInfo } = info;
    if (!name) {
      throw new CustomError('请输入标签名称');
    }

    const tagQuery: any = {};
    // 去重操作
    const nameArr = [...new Set(name)];
    tagQuery.name = { [Op.in]: nameArr };
    if (info.xd_value) {
      tagQuery.xd_value = info.xd_value;
    }
    if (info.xk_value) {
      tagQuery.xk_value = info.xk_value;
    }
    const tagInfo = await this.service.findByFilter({ where: tagQuery });
    if (tagInfo.length > 0) {
      throw new CustomError('存在重复标签名称，请删除后再录入');
    }
    const data = nameArr.map(item => {
      return {
        name: item,
        ...restInfo,
      };
    });
    const res = await this.service.batchCreate(data);
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: string, @Body() body: any) {
    if (!body.name) {
      throw new CustomError('请输入标签名称');
    }
    const tagInfo = await this.service.findOne({
      where: { name: body.name },
    });
    if (tagInfo) {
      throw new CustomError('当前标签名称已存在，请重新编辑');
    }

    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }
}
