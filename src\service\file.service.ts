import { UploadStreamFileInfo } from '@midwayjs/busboy';
import { Config, Inject, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { unlinkSync, createWriteStream, existsSync, mkdirSync } from 'fs';
import { CustomError } from '../error/custom.error';
@Provide()
export class FileService {
  @Inject()
  ctx: Context;
  @Config('uploadFilePath')
  uploadFilePath: string;
  constructor() {}

  /**
   * stream方式持久化文件到服务器
   * @param fileInfo 文件信息
   * @param target 目标路径
   */
  async saveFile(fileInfo: UploadStreamFileInfo, target: string) {
    const { data, filename } = fileInfo;
    if (!existsSync(target)) {
      mkdirSync(target, { recursive: true });
    }
    const fileName = `${target}/${filename}`;
    if (existsSync(fileName)) {
      // 文件已存在 删除旧文件
      unlinkSync(fileName);
    }
    const fileStream = createWriteStream(fileName);
    try {
      return new Promise((resolve, reject) => {
        data.pipe(fileStream);
        fileStream.on('finish', () => {
          resolve(true);
        });
        fileStream.on('error', reject);
        data.on('error', reject);
      });
    } catch (error) {
      fileStream.destroy();
      throw new CustomError('上传文件失败,请稍后重试！');
    }
  }

  /**
   * 删除文件
   * @param filePath 文件路径
   */
  async deleteFile(filePath: string) {
    if (existsSync(filePath)) {
      unlinkSync(filePath);
    }
  }
}
