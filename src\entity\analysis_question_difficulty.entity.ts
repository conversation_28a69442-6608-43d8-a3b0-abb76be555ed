import { Table, Column, Model, DataType } from 'sequelize-typescript';

export interface AnalysisQuestionDifficultyAttributes {
  /** 主键ID */
  id: number;
  /** 学期编码 */
  semester_code?: string;
  /** 学期名称 */
  semester_name?: string;
  /** 省份编码 */
  province_code?: string;
  /** 省份名称 */
  province_name?: string;
  /** 市编码 */
  city_code?: string;
  /** 市名称 */
  city_name?: string;
  /** 区编码 */
  area_code?: string;
  /** 区名称 */
  area_name?: string;
  /** 学制编码 */
  school_system_code?: string;
  /** 学制名称 */
  school_system_name?: string;
  /** 学段编码 */
  grade_section_code?: string;
  /** 学段名称 */
  grade_section_name?: string;
  /** 科目id */
  subject_id?: number;
  /** 科目名称 */
  subject?: string;
  /** 题目难度 */
  difficulty_name: string;
  /** 难度编码 */
  difficulty_code?: string;
  /** 题目数量 */
  difficulty_number?: number;
}

@Table({
  tableName: 'analysis_question_difficulty',
  timestamps: true,
  comment: '题目难度分析',
})
export class AnalysisQuestionDifficulty
  extends Model<AnalysisQuestionDifficultyAttributes>
  implements AnalysisQuestionDifficultyAttributes
{
  @Column({
    comment: '主键ID',
    primaryKey: true,
    autoIncrement: true,
    type: DataType.INTEGER,
  })
  id: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '学期编码',
  })
  semester_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '学期名称',
  })
  semester_name?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '省份编码',
  })
  province_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '省份名称',
  })
  province_name?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '市编码',
  })
  city_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '市名称',
  })
  city_name?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '区编码',
  })
  area_code?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '区名称',
  })
  area_name?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学制编码',
  })
  school_system_code?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学制名称',
  })
  school_system_name?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学段编码',
  })
  grade_section_code?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学段名称',
  })
  grade_section_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '科目id',
  })
  subject_id?: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '科目名称',
  })
  subject?: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '题目难度',
  })
  difficulty_name: string;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '题目难度code',
  })
  difficulty_code: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '题目数量',
  })
  difficulty_number?: number;
}
