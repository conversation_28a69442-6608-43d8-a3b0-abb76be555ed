import { Inject, Provide } from '@midwayjs/core';
import { ProvinceHomeworkImplement } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class ProvinceHomeworkImplementService extends BaseService<ProvinceHomeworkImplement> {
  @Inject()
  ctx: Context;

  constructor() {
    super('省级作业课程实施要点');
  }
  getModel = () => {
    return ProvinceHomeworkImplement;
  };
}
