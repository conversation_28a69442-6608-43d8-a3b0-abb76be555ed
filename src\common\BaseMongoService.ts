import { Inject, Logger, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { MONGO_MODEL_KEY } from '../common/Constants';

import { DocumentType } from '@typegoose/typegoose/lib/types';
import {
  FilterQuery,
  UpdateQuery,
  UpdateWithAggregationPipeline,
} from 'mongoose';
import { MongoModelFactory, MongoModelKey } from '../common/MongoModelFactory';

@Provide()
export class BaseMongoService {
  @Inject()
  ctx: Context;

  @Logger()
  logger;

  @Inject()
  mongoModelFactory: MongoModelFactory<any>;

  /**
   * 创建
   * @param info 数据
   * @returns document
   */
  async create(info: any, modelKey: string = MONGO_MODEL_KEY.SYSTEM) {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const document = await CurrentModel.create(info);
    return document;
  }

  /**
   * 更新
   * @param {string} _id 试题id
   * @param {object} info 更新数据
   */
  async update(_id: string, info, modelKey: string = MONGO_MODEL_KEY.SYSTEM) {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const document = await CurrentModel.updateOne(
      { _id },
      { ...info, updatedAt: new Date() }
    );
    return document;
  }

  /**
   * 删除
   * @param {string} _id 试题id
   */
  async destroy(_id: string, modelKey: string = MONGO_MODEL_KEY.SYSTEM) {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    await CurrentModel.deleteOne({ _id });
  }
  /**
   * 批量删除
   * @param {array[string]} ids 需要删除的数组
   */
  async destroyBulk(ids: string[], modelKey: string = MONGO_MODEL_KEY.SYSTEM) {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    await CurrentModel.deleteMany({ _id: ids });
  }

  /**
   * 批量修改
   * @param ids 需要修改的试题id
   * @param data 修改的数据对象
   */
  async updateBulk(
    ids: string,
    updateData: UpdateQuery<any> | UpdateWithAggregationPipeline,
    modelKey: string = MONGO_MODEL_KEY.SYSTEM
  ) {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const document = await CurrentModel.updateMany(
      { _id: { $in: ids } },
      { $set: updateData }
    ).exec();
    return document;
  }
  /**
   * 分页查询
   * @param conditions 查询条件
   * @param page 页码
   * @param limit 每页数量
   * @param order 排序
   * @param modelKey 模型
   * @returns
   */
  async findByPage(
    conditions: FilterQuery<DocumentType<any>> = {},
    page = 1,
    limit = 10,
    order: object = { createdAt: -1 },
    modelKey: MONGO_MODEL_KEY = MONGO_MODEL_KEY.SYSTEM
  ): Promise<{ list: Array<DocumentType<any>>; total: number }> {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const [list, total] = await Promise.all([
      CurrentModel.find(conditions)
        .sort(order)
        .skip((page - 1) * limit)
        .limit(limit)
        .lean()
        .exec(),
      CurrentModel.countDocuments(conditions).exec(),
    ]);

    return {
      list,
      total,
    };
  }
  /**
   * 根据id更新数据
   * @param id 主键id
   * @param updateData
   * @returns
   */
  async updateById(
    id: string,
    updateData: UpdateQuery<DocumentType<any>>,
    modelKey: MongoModelKey = MONGO_MODEL_KEY.SYSTEM
  ): Promise<DocumentType<any> | null> {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const result = await CurrentModel.findByIdAndUpdate(id, updateData, {
      new: true,
    })
      .lean()
      .exec();

    return result as DocumentType<any> | null;
  }

  /**
   * 删除单条数据
   * @param id 主键id
   * @returns
   */
  async deleteById(
    id: string,
    modelKey: MongoModelKey = MONGO_MODEL_KEY.SYSTEM
  ): Promise<DocumentType<any> | null> {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const result = await CurrentModel.findByIdAndDelete(id).lean().exec();
    return result as DocumentType<any> | null;
  }

  /**
   * 判断数据是否存在
   * @param conditions 查询条件
   * @returns
   */
  async exists(
    conditions: FilterQuery<DocumentType<any>>,
    modelKey: MongoModelKey = MONGO_MODEL_KEY.SYSTEM
  ): Promise<boolean> {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const result = await CurrentModel.exists(conditions);
    return !!result;
  }

  /**
   * 条件更新单条
   * @param conditions 查询条件
   * @param updateData 数据
   * @returns
   */
  async updateOne(
    conditions: FilterQuery<DocumentType<any>>,
    updateData: UpdateQuery<DocumentType<any>>,
    modelKey: MongoModelKey = MONGO_MODEL_KEY.SYSTEM
  ): Promise<{ n: number; nModified: number; ok: number }> {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const result = await CurrentModel.updateOne(conditions, updateData).exec();
    return {
      n: result.n,
      nModified: result.nModified,
      ok: result.ok,
    };
  }
  /**
   * 查询单条记录
   * @param id 数据id
   * @returns
   */
  async findById(
    id: string,
    modelKey: MongoModelKey = MONGO_MODEL_KEY.SYSTEM
  ): Promise<DocumentType<any> | null> {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const document = await CurrentModel.findById(id).exec();
    return document ? document : null;
  }

  /**
   * 条件查询
   * @param conditions 查询条件
   * @returns
   */
  async findByFilter(
    conditions: FilterQuery<DocumentType<any>>,
    modelKey: MongoModelKey = MONGO_MODEL_KEY.SYSTEM
  ): Promise<DocumentType<any> | null> {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const result = await CurrentModel.find(conditions).lean().exec();
    return result as DocumentType<any> | null;
  }

  /**
   * 批量创建
   * @param data
   * @returns
   */
  async insertMany(
    data: Array<Partial<any>>,
    modelKey: MongoModelKey = MONGO_MODEL_KEY.SYSTEM
  ): Promise<Array<DocumentType<any>>> {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const result = await CurrentModel.insertMany(data);
    return result;
  }

  /**
   * 条件批量更新
   * @param conditions 查询条件
   * @param updateData 更新数据
   * @returns
   */
  async updateMany(
    conditions: FilterQuery<DocumentType<any>>,
    updateData: UpdateWithAggregationPipeline,
    modelKey: MongoModelKey = MONGO_MODEL_KEY.SYSTEM
  ): Promise<{ n: number; nModified: number; ok: number }> {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const result = await CurrentModel.updateMany(conditions, updateData).exec();
    return {
      n: result.n,
      nModified: result.nModified,
      ok: result.ok,
    };
  }

  /**
   * 条件批量删除
   * @param conditions 查询条件
   * @returns
   */
  async deleteMany(
    conditions: FilterQuery<DocumentType<any>>,
    modelKey: MongoModelKey = MONGO_MODEL_KEY.SYSTEM
  ): Promise<{ n?: number; ok?: number }> {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    const result = await CurrentModel.deleteMany(conditions).exec();
    return {
      n: result.n,
      ok: result.ok,
    };
  }

  /**
   * 聚合查询
   * @param pipeline
   * @returns
   */
  async aggregate<AggregationResult = any>(
    pipeline: object[],
    modelKey: MongoModelKey = MONGO_MODEL_KEY.SYSTEM
  ): Promise<AggregationResult[]> {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    return CurrentModel.aggregate<AggregationResult>(pipeline).exec();
  }

  /**
   * 统计数量
   * @param conditions 查询条件
   * @returns
   */
  async countDocuments(
    conditions: FilterQuery<DocumentType<any>>,
    modelKey: MongoModelKey = MONGO_MODEL_KEY.SYSTEM
  ): Promise<number> {
    const CurrentModel = this.mongoModelFactory.getModel(modelKey);
    return CurrentModel.countDocuments(conditions).exec();
  }
}
