import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ClassWorkDetail } from './';

export interface ClassWorkQuestionAttributes {
  /** 主键 */
  id: number;
  /** 课时作业详情id */
  classworkDetail_id: number;
  /** 题目id */
  question_id: string;
  /** 排序 */
  sort_order: number;
  /** 试题来源表 */
  source_table?: string;
  classWorkDetail?: ClassWorkDetail;
}

@Table({
  tableName: 'class_work_question',
  timestamps: true,
  comment: '课时作业范本题目表',
})
export class ClassWorkQuestion
  extends Model<ClassWorkQuestionAttributes>
  implements ClassWorkQuestionAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @ForeignKey(() => ClassWorkDetail)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '课时作业详情id',
  })
  classworkDetail_id: number;

  @BelongsTo(() => ClassWorkDetail, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  classWorkDetail?: ClassWorkDetail;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '题目id',
  })
  question_id: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '排序',
  })
  sort_order: number;

  @Column({
    type: DataType.ENUM('system', 'school', 'personal'),
    allowNull: true,
    comment: '试题来源表',
  })
  source_table?: string;
}
