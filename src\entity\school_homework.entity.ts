import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  HasMany,
  HasOne,
  Model,
  Table,
} from 'sequelize-typescript';
import {
  User,
  Enterprise,
  TextbookTemp,
  Textbook,
  SchoolHomeworkDetail,
} from './';

export interface SchoolHomeworkAttributes {
  id: number;
  name: string;
  textBookId: number;
  source: string;
  status: string;
  templateHomeworkId: number;
  enterpriseId: number;
  author: string;
  createdAt: Date;
  updatedAt: Date;
  authorUser?: User;
  enterprise?: Enterprise;
  homeworkDetails?: SchoolHomeworkDetail[];
}

@Table({
  tableName: 'school_homework',
  timestamps: true,
  comment: '校本作业表',
})
export class SchoolHomework
  extends Model<SchoolHomeworkAttributes>
  implements SchoolHomeworkAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '作业名称',
  })
  name: string;

  @ForeignKey(() => Textbook)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '作业id',
  })
  textBookId: number;
  @HasOne(() => Textbook, {
    foreignKey: 'id',
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  })
  textBook: Textbook;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '作业来源',
  })
  source: string;

  @Column({
    type: DataType.ENUM('draft', 'published', 'rejected', 'pass'),
    defaultValue: 'draft',
    allowNull: false,
    comment: '作业状态',
  })
  status: string;

  @ForeignKey(() => TextbookTemp)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '范本作业id',
  })
  templateHomeworkId: number;
  @HasOne(() => TextbookTemp, {
    foreignKey: 'id',
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  })
  templateHomework: TextbookTemp;

  @ForeignKey(() => Enterprise)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '企业id',
  })
  enterpriseId: number;
  @HasOne(() => Enterprise, {
    foreignKey: 'id',
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  })
  enterprise: Enterprise;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '作者id',
  })
  author: string;
  @BelongsTo(() => User, {
    onUpdate: 'CASCADE',
    onDelete: 'CASCADE',
  })
  authorUser: User;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '创建时间',
  })
  createdAt: Date;
  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '更新时间',
  })
  updatedAt: Date;
  @HasMany(() => SchoolHomeworkDetail, 'id')
  homeworkDetails: SchoolHomeworkDetail[];
}
