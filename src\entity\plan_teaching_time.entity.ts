import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { Enterprise } from './enterprise.entity'; // 假设存在一个 Enterprise 实体类

export interface PlanTeachingTimeAttributes {
  /** 主键 */
  id: number;
  /** 年级code */
  grade_code?: string;
  /** 年级名称 */
  grade_name?: string;
  /** 每周课时数 */
  weekly_hours?: number;
  /** 每课时 */
  class_hour?: string;
  /** 授课周数 */
  lessons_weeks?: number;
  /** 复习考试周数 */
  review_weeks?: number;
  /** 学校机动时间 */
  school_hours?: number;
  /** 学校id */
  enterprise_id?: number;
  /** 学年学期code */
  semester_code?: string;
  /** 学年学期 */
  semester_name?: string;
}

@Table({
  tableName: 'plan_teaching_time',
  timestamps: true,
  comment: '教学时间表',
})
export class PlanTeachingTime
  extends Model<PlanTeachingTimeAttributes>
  implements PlanTeachingTimeAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '年级code',
  })
  grade_code?: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '年级名称',
  })
  grade_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '每周课时数',
  })
  weekly_hours?: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '每课时',
  })
  class_hour?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '授课周数',
  })
  lessons_weeks?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '复习考试周数',
  })
  review_weeks?: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '学校机动时间',
  })
  school_hours?: number;

  @ForeignKey(() => Enterprise)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '学校id',
  })
  enterprise_id?: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '学年学期code',
  })
  semester_code?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学年学期',
  })
  semester_name?: string;

  @BelongsTo(() => Enterprise, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  enterprise?: Enterprise;
}
