import { Model, ModelCtor } from 'sequelize-typescript';
import { CustomError } from '../error/custom.error';
import {
  Attributes,
  FindAttributeOptions,
  FindOptions,
  Includeable,
  Op,
  Order,
  Transaction,
  WhereOptions,
} from 'sequelize';
import { flattenDeep } from 'lodash';

export abstract class BaseService<T extends Model> {
  protected modelTitle: string;

  /**
   * Creates an instance of BaseService.
   * @param {string} modelTitle 模型名称，用于优化提示信息
   * @memberof BaseService
   */
  constructor(modelTitle: string) {
    this.modelTitle = modelTitle;
  }

  /**
   * 获取模型
   *
   * @abstract
   * @return {*}  {ModelCtor<T>} model
   * @memberof BaseService
   */
  protected abstract getModel(): ModelCtor<T>;

  /**
   * 查询列表
   *
   * @param {object} params params
   * @param {WhereOptions<Attributes<T>>} params.query 查询条件
   * @param {Includeable | Includeable[]} [params.include] 级联信息
   * @param {number} [params.offset] 分页
   * @param {number} [params.limit] 分页
   * @param {Order} [params.order] 分页
   * @return {Promise<{ list: T[]; total?: number }>}  res
   * @memberof BaseService
   */
  async findAll(params: {
    query: WhereOptions<Attributes<T>>;
    attributes?: FindAttributeOptions;
    include?: Includeable | Includeable[];
    offset?: number;
    limit?: number;
    order?: Order;
    sort?: any;
    filter?: any;
  }): Promise<{ list: T[]; total?: number }> {
    const { query, attributes, include, offset, limit, order, filter, sort } =
      params;
    const CurrentModel = this.getModel();
    const queryOption: FindOptions<Attributes<T>> = {};
    const result: { list: T[]; total?: number } = { list: [] };

    if (attributes) {
      queryOption.attributes = attributes;
    }

    const hasPaging = offset !== undefined || limit !== undefined;
    if (hasPaging) {
      queryOption.offset = Number(offset) || 0;
      queryOption.limit = Number(limit) || 10;
    }

    Object.keys(query).forEach(key => {
      if (typeof query[key] === 'string' && query[key].includes(',')) {
        query[key] = query[key].split(',');
      }
    });
    if (filter) {
      try {
        const filterObj = JSON.parse(filter);
        for (const key in filterObj) {
          const item = filterObj[key];
          if (Array.isArray(item)) {
            query[key] = {
              [Op.in]: item,
            };
          } else {
            if (item !== null) {
              query[key] = item;
            }
          }
        }
      } catch (error) {
        console.warn('filter 解析失败', error);
      }
    }
    const where = {
      ...query,
      // 自定义查询参数参数
    };

    queryOption.where = where;

    if (include) {
      queryOption.include = include;
    }
    if (order) {
      queryOption.order = order;
    } else if (sort) {
      try {
        const sortObj = JSON.parse(sort);
        queryOption.order = Object.keys(sortObj).map(key => [
          key,
          sortObj[key].includes('desc') ? 'DESC' : 'ASC',
        ]);
      } catch (error) {
        console.error('sort 解析失败', error);
      }
    }
    // else {
    //   const attributes = CurrentModel.getAttributes();
    //   if (attributes['createdAt']) {
    //     queryOption.order = [['createdAt', 'DESC']];
    //   }
    //   if (!attributes['createdAt'] && attributes['id']) {
    //     queryOption.order = [['id', 'DESC']];
    //   }
    //   if (!attributes['createdAt'] && !attributes['id']) {
    //     const first = Object.keys(attributes)[0];
    //     queryOption.order = [[first, 'DESC']];
    //   }
    // }

    const res = await CurrentModel.findAll(queryOption);
    result.list = res;

    if (hasPaging) {
      // 获取总数
      const total = await CurrentModel.count({
        where,
      });
      result.total = total || 0;
    }
    return result;
  }

  /**
   * 根据id查询
   *
   * @param {any} id id
   * @return {*}  {(Promise<T | null>)} res
   * @memberof BaseService
   */
  async findById(id: any): Promise<T | null> {
    const CurrentModel = this.getModel();
    const res = await CurrentModel.findByPk(id);
    return res;
  }

  /**
   * 根据条件查询单个
   *
   * @param {Record<string, any>} where 查询条件
   * @return {*}  {(Promise<T | null>)} res
   * @memberof BaseService
   */
  async findOne(where: Record<string, any>): Promise<T | null> {
    const CurrentModel = this.getModel();
    const res = await CurrentModel.findOne({ where });
    return res;
  }

  /**
   * 创建
   *
   * @param {Partial<T>} info 创建数据
   * @return {*}  {Promise<T>} res
   * @memberof BaseService
   */
  async create(info: Partial<T>): Promise<T> {
    const CurrentModel = this.getModel();
    const model = new CurrentModel();
    model.setAttributes(info);
    const res = await model.save();

    return res;
  }

  /**
   * 批量创建，数据量过大时，分批插入
   * @param info 数据
   * @param transaction 事务 可选参数
   * @param size 批量大小，默认200 可选参数
   */
  async batchCreate(info: any[], transaction = null, size = 200) {
    const CurrentModel = this.getModel();
    try {
      if (info.length > size) {
        const promises = [];
        for (let i = 0; i < info.length; i += size) {
          const infoArr = info.slice(i, i + size);
          promises.push(CurrentModel.bulkCreate(infoArr, { transaction }));
        }
        const results = await Promise.all(promises);
        return flattenDeep(results);
      } else {
        return await CurrentModel.bulkCreate(info, { transaction });
      }
    } catch (error) {
      throw new CustomError(error.message);
    }
  }

  /**
   * 更新
   *
   * @param {Record<string, any>} where 查询条件
   * @param {Partial<T>} data 更新数据
   * @memberof BaseService
   */
  async update(where: Record<string, any>, data: Partial<T>) {
    const record = await this.findOne(where);
    if (!record) throw new CustomError(`指定${this.modelTitle}不存在`);
    await record.update(data);
  }

  /**
   * 删除
   *
   * @param {*} {Record<string, any>} where 查询条件
   * @return {*}  {Promise<number>} res
   * @memberof BaseService
   */
  async delete(where: Record<string, any>): Promise<number> {
    const CurrentModel = this.getModel();
    return await CurrentModel.destroy({ where });
  }

  /**
   * 批量更新，数据量过大时，分批更新
   * @param info 数据
   * @param updateOption
   * @param size 批量大小，默认200 可选参数
   */
  async batchUpdate(
    info: any[],
    updateOption: {
      transaction?: Transaction | null;
      updateOnDuplicate: (keyof Attributes<T>)[];
    },
    size = 200
  ): Promise<void> {
    const CurrentModel = this.getModel();
    if (info.length > size) {
      for (let i = 0; i < info.length; i += size) {
        const infoArr = info.slice(i, i + size);
        await CurrentModel.bulkCreate(infoArr, updateOption);
      }
    } else {
      await CurrentModel.bulkCreate(info, updateOption);
    }
  }
}
