import { Inject, Provide } from '@midwayjs/core';
import { StandardCourseContent } from '../entity/standard_course_content.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class StandardCourseContentService extends BaseService<StandardCourseContent> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课程内容');
  }
  getModel = () => {
    return StandardCourseContent;
  };
}
