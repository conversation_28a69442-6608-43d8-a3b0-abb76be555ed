import { Configuration, App } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import * as validate from '@midwayjs/validate';
import * as info from '@midwayjs/info';
import * as typegoose from '@midwayjs/typegoose';
import { join } from 'path';
import { DatabaseFilter } from './filter/database.filter';
import { AxiosFilter } from './filter/axios.filter';
import * as busboy from '@midwayjs/busboy';
import { NotFoundFilter } from './filter/notfound.filter';
import { NoAuthFilter } from './filter/noauth.filter';
import { CustomErrorFilter } from './filter/custom.filter';
import { DefaultErrorFilter } from './filter/default.filter';
import { JWTMiddleware } from './middleware/jwt.middleware';
import { RequestLoggerMiddleware } from './middleware/requestlogger.middleware';
import { FormatMiddleware } from './middleware/format.middleware';
import * as sequelize from '@midwayjs/sequelize';
import * as jwt from '@midwayjs/jwt';
import * as axios from '@midwayjs/axios';
import * as view from '@midwayjs/view-ejs';

@Configuration({
  imports: [
    koa,
    validate,
    sequelize,
    jwt,
    axios,
    busboy,
    view,
    {
      component: info,
      enabledEnvironment: ['local'],
    },
    typegoose,
  ],
  importConfigs: [join(__dirname, './config')],
})
export class MainConfiguration {
  @App('koa')
  app: koa.Application;

  async onReady() {
    const env = this.app.getEnv();
    // add middleware
    this.app.useMiddleware(
      ['local', 'unittest'].includes(env)
        ? [RequestLoggerMiddleware, FormatMiddleware]
        : [JWTMiddleware, FormatMiddleware]
    );
    // add filter
    this.app.useFilter([
      NotFoundFilter,
      NoAuthFilter,
      DatabaseFilter,
      AxiosFilter,
      CustomErrorFilter,
      DefaultErrorFilter,
    ]);
  }
}
