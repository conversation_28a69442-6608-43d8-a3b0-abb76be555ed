import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';

export interface ProvinceHomeworkConceptAttributes {
  /** 主键ID */
  id: number;
  /** 学科名称 */
  subject_name?: string;
  /** 学科id */
  subject_id?: number;
  /** 学段 */
  grade_section_code?: string;
  /** 学段名称 */
  grade_section_name?: string;
  /** 学科设计理念 */
  design_concept?: string;
  /** 设计理念细目 */
  design_details?: string;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({
  tableName: 'province_homework_concept',
  timestamps: true,
  comment: '设计理念表',
})
export class ProvinceHomeworkConcept
  extends Model<ProvinceHomeworkConceptAttributes>
  implements ProvinceHomeworkConceptAttributes
{
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '学科名称',
  })
  subject_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '学科id',
  })
  subject_id?: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学段',
  })
  grade_section_code?: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学段名称',
  })
  grade_section_name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '学科设计理念',
  })
  design_concept?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '设计理念细目',
  })
  design_details?: string;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    comment: '创建时间',
  })
  createdAt?: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    comment: '更新时间',
  })
  updatedAt?: Date;
}
