import { MidwayConfig } from '@midwayjs/core';

export default {
  sequelize: {
    dataSource: {
      default: {
        dialect: 'mysql',
        host: '**********',
        port: 3306,
        username: 'root',
        password: 'Ysp@1234',
        database: 'homework-design',
        // 模型设置
        define: {
          timestamps: true,
          createdAt: 'createdAt',
          updatedAt: 'updatedAt',
          // 禁用驼峰转换
          underscored: false,
          charset: 'utf8mb4',
          collate: 'utf8mb4_bin',
        },
        // 时区设置
        timezone: '+08:00',
        // 连接池配置
        pool: {
          max: 5,
          min: 0,
          idle: 10000,
        },
        entities: ['entity'],
        logging: false,
      },
    },
  },
  mongoose: {
    dataSource: {
      default: {
        uri: 'mongodb://*************:27017/homework-design',
        options: {
          useNewUrlParser: true,
          useUnifiedTopology: true,
          // user: '***********',
          // pass: '***********',
        },
        // 关联实体
        entities: ['model'],
      },
    },
  },
} as MidwayConfig;
