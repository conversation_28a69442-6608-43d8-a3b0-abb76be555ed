import { Inject, Provide } from '@midwayjs/core';
import { ComposerQuestion } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { col, fn } from 'sequelize';
import { sumBy } from 'lodash';

@Provide()
export class ComposerQuestionService extends BaseService<ComposerQuestion> {
  @Inject()
  ctx: Context;

  constructor() {
    super('试题篮');
  }
  getModel = () => {
    return ComposerQuestion;
  };

  /**
   * 计算分类类型数量
   *
   * @returns 返回包含问题类型及其数量的数组
   */
  async groupCount(paperId: number) {
    const groupResults = await ComposerQuestion.findAll({
      where: {
        composerPaperId: paperId,
      },
      attributes: [[fn('count', col('type')), 'count'], 'type'],
      group: ['type'],
    });
    const questionIds = await ComposerQuestion.findAll({
      where: {
        composerPaperId: paperId,
      },
      attributes: ['questionBankId', 'sourceTable'],
    });
    return {
      ids: questionIds,
      list: groupResults,
      total: sumBy(JSON.parse(JSON.stringify(groupResults)), 'count'),
    };
  }

  /**
   * 批量创建试题篮
   *
   * @param questions 试题类型数组
   * @param composerPaperId 方案ID
   * @param transaction 数据库事务对象，默认为null
   * @returns Promise<void>
   */
  async bulkCreate(questions, composerPaperId, transaction = null) {
    // 对data进行处理，例如为每个题目添加composerPaperId
    const processedData = questions.map(item => ({
      ...item,
      composerPaperId: composerPaperId,
    }));

    // 使用事务批量创建题目
    const promises = processedData.map(item =>
      ComposerQuestion.upsert(item, { transaction })
    );
    await Promise.all(promises);
  }

  /**
   *
   * @param filter 查询条件
   * @returns
   */
  async findByFilter(filter: any) {
    return await ComposerQuestion.findAll({ where: filter });
  }
}
