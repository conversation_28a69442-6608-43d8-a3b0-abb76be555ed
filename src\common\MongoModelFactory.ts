import { Init, Provide, Scope, ScopeEnum } from '@midwayjs/core';
import { InjectEntityModel } from '@midwayjs/typegoose';
import { ReturnModelType } from '@typegoose/typegoose';
import { SystemQuestions, SchoolQuestions, PersonalQuestions } from '../model';
import { MONGO_MODEL_KEY } from './Constants';
import { AnyParamConstructor } from '@typegoose/typegoose/lib/types';
import { CustomError } from '../error/custom.error';
export type MongoModelKey =
  (typeof MONGO_MODEL_KEY)[keyof typeof MONGO_MODEL_KEY];

/**
 * mongo model工厂
 */
@Provide()
@Scope(ScopeEnum.Singleton, { allowDowngrade: true })
export class MongoModelFactory<T> {
  private modelMap: Map<
    MongoModelKey | string,
    ReturnModelType<AnyParamConstructor<T>>
  >;
  @InjectEntityModel(SystemQuestions)
  system: ReturnModelType<typeof SystemQuestions>;
  @InjectEntityModel(SchoolQuestions)
  school: ReturnModelType<typeof SchoolQuestions>;
  @InjectEntityModel(PersonalQuestions)
  personal: ReturnModelType<typeof PersonalQuestions>;

  @Init()
  init() {
    if (!this.modelMap) {
      this.modelMap = new Map();
    }
    for (const [key, value] of Object.entries(MONGO_MODEL_KEY)) {
      void key;
      this.modelMap.set(
        value,
        this[value.toLowerCase() as keyof this] as ReturnModelType<
          AnyParamConstructor<T>
        >
      );
    }
  }

  getModel(source: MongoModelKey | string) {
    const model = this.modelMap.get(source);
    if (!model) {
      throw new CustomError(`未找到${source}!`);
    }
    return model;
  }
}
