import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ComposerPaper } from './composer_paper.entity';

export interface ComposerPaperDetailAttributes {
  /** 主键ID */
  id: number;
  /** 组卷方案ID */
  composerPaperId: number;
  /** 单元 */
  unit: string;
  /** 课时 */
  period?: string;
  /** 来源 */
  source: string;
  /** 题目难度 */
  difficulty: string;
  /** 试题类型 */
  questionType: string;
  /** 试题扩展类型 */
  questionExtendType: string;
  /** 试题来源类型 */
  questionSourceType: string;
  /** 试题数量 */
  num: number;
  /** 试题分数 */
  score: number;
  /** 顺序 */
  orderIndex: number;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({
  tableName: 'composer_paper_detail',
  timestamps: true,
  comment: '组卷详情表',
})
export class ComposerPaperDetail
  extends Model<ComposerPaperDetailAttributes>
  implements ComposerPaperDetailAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
    field: 'id',
  })
  id: number;

  @ForeignKey(() => ComposerPaper)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '组卷方案ID',
    field: 'composer_paper_id',
  })
  composerPaperId: number;

  @BelongsTo(() => ComposerPaper, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  composerPaper?: ComposerPaper;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '来源',
    field: 'source',
  })
  source: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '单元',
    field: 'unit',
  })
  unit: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '课时',
    field: 'period',
  })
  period: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '题目难度',
    field: 'difficulty',
  })
  difficulty: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '试题类型',
    field: 'question_type',
  })
  questionType: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '试题扩展类型',
    field: 'question_extend_type',
  })
  questionExtendType: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '试题来源类型',
    field: 'question_source_type',
  })
  questionSourceType: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '试题数量',
    field: 'num',
  })
  num: number;

  @Column({
    type: DataType.FLOAT,
    allowNull: false,
    comment: '试题分数',
    field: 'score',
  })
  score: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '顺序',
    field: 'order_index',
  })
  orderIndex: number;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '创建时间',
    field: 'created_at',
  })
  createdAt?: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '更新时间',
    field: 'updated_at',
  })
  updatedAt?: Date;
}
