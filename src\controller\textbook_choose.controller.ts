import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { TextbookChooseService } from '../service/textbook_choose.service';
import { CustomError } from '../error/custom.error';
import {
  Subject,
  Textbook,
  TextbookChooseAttributes,
  TextbookChooseDetail,
} from '../entity';

@Controller('/textbook_choose')
export class TextbookChooseController {
  @Inject()
  ctx: Context;

  @Inject()
  service: TextbookChooseService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      include: [Subject, Textbook, TextbookChooseDetail],
    });
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(
    @Query() query: any,
    @Body() body: Partial<TextbookChooseAttributes>
  ) {
    const { items, ...info } = body;
    const choose = await this.service.findOne(query);
    if (!choose) {
      throw new CustomError('未找到指定信息');
    }
    await choose.update(info);
    if (items && Array.isArray(items)) {
      await this.service.setItems(
        choose.id,
        items.map(item => item.area_code)
      );
    }
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() body: TextbookChooseAttributes) {
    const { items, ...info } = body;
    const res = await this.service.create(info);
    if (items && Array.isArray(items)) {
      await this.service.setItems(
        res.id,
        items.map(item => item.area_code)
      );
    }
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(
    @Param('id') id: number,
    @Body() body: Partial<TextbookChooseAttributes>
  ) {
    const { items, ...info } = body;
    await this.service.update({ id }, info);
    if (items && Array.isArray(items)) {
      await this.service.setItems(
        id,
        items.map(item => item.area_code)
      );
    }
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }
}
