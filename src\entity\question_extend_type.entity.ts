import { Column, DataType, Model, Table } from 'sequelize-typescript';

export interface QuestionExtendTypeAttributes {
  id: number;
  name: string;
  code: string;
  sourceCode: string;
  sourceName: string;
  isCompose: boolean;
  /** 所属学科ID */
  subjectId: string;
  gradeSection: string;
  createdAt: Date;
  updatedAt: Date;
}

@Table({
  tableName: 'question_extend_type',
  timestamps: true,
  comment: '扩展题型表',
})
export class QuestionExtendType
  extends Model<QuestionExtendTypeAttributes>
  implements QuestionExtendTypeAttributes
{
  @Column({
    primaryKey: true,
    autoIncrement: true,
    type: DataType.INTEGER,
  })
  id: number;

  @Column({
    allowNull: false,
    comment: '题型名称',
    type: DataType.STRING(100),
  })
  name: string;

  @Column({
    allowNull: false,
    comment: '题型编号',
    type: DataType.STRING(50),
    unique: {
      name: 'unique_extend_code',
      msg: '已存在相同编号的题型',
    },
  })
  code: string;

  @Column({
    allowNull: false,
    comment: '源题型编号',
    type: DataType.STRING(50),
    field: 'source_code',
  })
  sourceCode: string;

  @Column({
    allowNull: false,
    comment: '源题型名称',
    type: DataType.STRING(100),
    field: 'source_name',
  })
  sourceName: string;

  @Column({
    allowNull: false,
    comment: '是否为组合题型',
    type: DataType.BOOLEAN,
    defaultValue: false,
    field: 'is_compose',
  })
  isCompose: boolean;
  @Column({
    allowNull: true,
    comment: '所属学科ID',
    type: DataType.TEXT,
    field: 'subject_id',
    get() {
      const newValue = this.getDataValue('subjectId');
      return newValue ? newValue.split(',').map(v => Number(v)) : [];
    },
    set(value: string[]) {
      this.setDataValue('subjectId', value.join(','));
    },
  })
  subjectId: string;
  @Column({
    allowNull: true,
    comment: '学段',
    type: DataType.STRING(50),
    field: 'grade_section',
  })
  gradeSection: string;
  @Column({
    allowNull: false,
    comment: '创建时间',
    type: DataType.DATE,
    field: 'created_at',
  })
  createdAt: Date;
  @Column({
    allowNull: false,
    comment: '更新时间',
    type: DataType.DATE,
    field: 'updated_at',
  })
  updatedAt: Date;
}
