import {
  Table,
  Column,
  Model,
  DataType,
  // ForeignKey,
  // BelongsTo,
} from 'sequelize-typescript';
// import { StandardCourseContent } from './standard_course_content.entity'; // 假设存在一个 StandardCourseContent 实体类
// import { Enterprise } from './enterprise.entity'; // 假设存在一个 Enterprise 实体类

export interface StandardAcademicQualityAttributes {
  /** 主键ID */
  id: number;
  /** 名称 */
  name?: string;
  /** 描述 */
  describe?: string;
  /** 解析 */
  analy?: string;
  // /** 父级学业质量ID（树形结构） */
  // parent_id?: number;
  // /** 学段 */
  // grade_section_code?: string;
  // /** 学段名称 */
  // grade_section_name?: string;
  // /** 核心素养要求 */
  // core_competence?: string;
  // /** 学业成就表现描述 */
  // achievement_description?: string;
  // /** 相关课程内容id */
  // course_content_id?: number;
  // /** 关键表现 */
  // key_performance?: string;
  // /** 学校id */
  // enterprise_id?: number;
}

@Table({
  tableName: 'standard_academic_quality',
  timestamps: true,
  comment: '学业质量表',
})
export class StandardAcademicQuality
  extends Model<StandardAcademicQualityAttributes>
  implements StandardAcademicQualityAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质名称',
  })
  name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质描述',
  })
  describe?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质解析',
  })
  analy?: string;

  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '父级学业质量ID（树形结构）',
  // })
  // parent_id?: number;

  // @Column({
  //   type: DataType.STRING(64),
  //   allowNull: true,
  //   comment: '学段',
  // })
  // grade_section_code?: string;

  // @Column({
  //   type: DataType.STRING(64),
  //   allowNull: true,
  //   comment: '学段名称',
  // })
  // grade_section_name?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '核心素养要求',
  // })
  // core_competence?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '学业成就表现描述',
  // })
  // achievement_description?: string;

  // @ForeignKey(() => StandardCourseContent)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '相关课程内容id',
  // })
  // course_content_id?: number;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '关键表现',
  // })
  // key_performance?: string;

  // @ForeignKey(() => Enterprise)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '学校id',
  // })
  // enterprise_id?: number;

  // @BelongsTo(() => StandardCourseContent, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // courseContent?: StandardCourseContent;

  // @BelongsTo(() => Enterprise, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // enterprise?: Enterprise;
}
