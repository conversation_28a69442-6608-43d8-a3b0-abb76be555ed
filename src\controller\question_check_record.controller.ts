import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { QuestionCheckRecordService } from '../service/question_check_record.service';
import { CustomError } from '../error/custom.error';

@Controller('/question_check_record')
export class QuestionCheckRecordController {
  @Inject()
  ctx: Context;

  @Inject()
  service: QuestionCheckRecordService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;
    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return await this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['createdAt', 'DESC']],
    });
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }

  @Post('/bulkCreate', { summary: '创建审核记录' })
  async bulkCreate(@Body() body: any) {
    const { questions, ...checkInfo } = body;
    await this.service.bulkCreate(questions, checkInfo);
    return true;
  }

  @Del('/question/:questionId', { summary: '删除试题审核记录' })
  async destroyByQuestionId(@Param('questionId') id: string) {
    await this.service.delete({ questionBankId: id });
    return true;
  }

  @Put('/revoke/:questionId', { summary: '撤销试题审核记录' })
  async revokeQuestionCheck(@Param('questionId') questionId: string) {
    if (!questionId) {
      throw new CustomError('未指定试题！');
    }
    return await this.service.cleanAndUpdateCheckRecords(questionId);
  }
}
