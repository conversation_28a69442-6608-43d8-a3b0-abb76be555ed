import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { EnterpriseService } from '../service/enterprise.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { EnterpriseAttributes } from '../entity';

@Controller('/enterprises')
export class EnterpriseController {
  @Inject()
  ctx: Context;

  @Inject()
  service: EnterpriseService;

  @Get('/', { summary: '查询单位列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      filter,
      sort,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // name支持模糊查询
    if (queryInfo.name) {
      queryInfo.name = {
        [Op.like]: `%${queryInfo.name}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      filter,
      sort,
      order: [['name', 'ASC']],
    });
  }

  @Get('/:id', { summary: '按ID查询单位' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定单位');
    }
    return res;
  }

  @Post('/', { summary: '新增单位' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新单位' })
  async update(
    @Param('id') id: string,
    @Body() body: Partial<EnterpriseAttributes>
  ) {
    if (!body.province) {
      body.province = null;
      body.province_name = null;
      body.city = null;
      body.city_name = null;
      body.area = null;
      body.area_name = null;
    }
    if (!body.city) {
      body.city = null;
      body.city_name = null;
      body.area = null;
      body.area_name = null;
    }
    if (!body.area) {
      body.area = null;
      body.area_name = null;
    }
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除单位' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }
}
