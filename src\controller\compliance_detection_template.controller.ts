import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ComplianceDetectionTemplateService } from '../service/compliance_detection_template.service';
import { CustomError } from '../error/custom.error';
import { Enterprise } from '../entity';
import { Op } from 'sequelize';

@Controller('/compliance_detection_template')
export class ComplianceDetectionTemplateController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ComplianceDetectionTemplateService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      enterprise_id,
      ...queryInfo
    } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // 如果是学校查询范本，要支持查看共享的范本内容
    if (enterprise_id) {
      queryInfo[Op.or] = [
        { enterprise_id },
        { is_share: true }, //学校要能查看共享的范本
      ];
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['createdAt', 'DESC']],
      include: [{ model: Enterprise, attributes: ['id', 'code', 'name'] }],
    });
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    const { subject_id, textbook_version, volume } = info;
    if (!subject_id || !textbook_version || !volume) {
      throw new CustomError('缺少必要参数');
    }

    return await this.service.create(info);
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update(id, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }
}
