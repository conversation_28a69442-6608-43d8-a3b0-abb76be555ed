import { Column, DataType, Model, Table } from 'sequelize-typescript';

export interface ExcludeSimilarQuestionAttributes {
  id: number;
  questionId: string;
  excludeSimilarQuestionId: string;
  createdAt: Date;
  updatedAt: Date;
}

@Table({
  tableName: 'exclude_similar_question',
  timestamps: true,
  comment: '手动排除的相似题关联关系表',
})
export class ExcludeSimilarQuestion
  extends Model<ExcludeSimilarQuestionAttributes>
  implements ExcludeSimilarQuestionAttributes
{
  @Column({
    primaryKey: true,
    autoIncrement: true,
    comment: 'id',
    type: DataType.INTEGER,
  })
  id: number;
  @Column({
    comment: '题目id',
    type: DataType.STRING(36),
  })
  questionId: string;
  @Column({
    comment: '排除的相似题id',
    type: DataType.STRING(36),
  })
  excludeSimilarQuestionId: string;
  @Column({
    comment: '创建时间',
    type: DataType.DATE,
  })
  createdAt: Date;
  @Column({
    comment: '更新时间',
    type: DataType.DATE,
  })
  updatedAt: Date;
}
