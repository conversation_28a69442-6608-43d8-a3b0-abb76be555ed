import { Inject, Provide } from '@midwayjs/core';
import { CityHomeworkConcept } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class CityHomeworkConceptService extends BaseService<CityHomeworkConcept> {
  @Inject()
  ctx: Context;

  constructor() {
    super('市级作业设计理念');
  }
  getModel = () => {
    return CityHomeworkConcept;
  };
}
