import { Inject, Provide } from '@midwayjs/core';
import { ProvinceHomeworkGoal } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class ProvinceHomeworkGoalService extends BaseService<ProvinceHomeworkGoal> {
  @Inject()
  ctx: Context;

  constructor() {
    super('省级作业目标');
  }
  getModel = () => {
    return ProvinceHomeworkGoal;
  };
}
