import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ClassWorkQuestionService } from '../service/class_work_question.service';
import { CustomError } from '../error/custom.error';
import { QuestionService } from '../service/question.service';

@Controller('/class_work_question')
export class ClassWorkQuestionController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ClassWorkQuestionService;

  @Inject()
  questionService: QuestionService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    const res = await this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['sort_order', 'ASC']],
    });

    // 如果试题列表为空，直接返回空数组
    if (res.list.length === 0) {
      return [];
    }
    // 组装试题信息，根据试题id及试题表名查找对应试题详情
    const questions = res.list.map(item => {
      return {
        question_id: item.question_id,
        source_table: item.source_table,
      };
    });
    const result = await this.questionService.findQuestions(questions);
    return result;
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/', { summary: '删除单个或多个' })
  async destroy(@Body() body: any) {
    const { id, classworkDetail_id } = body;
    if (id) {
      // 根据试题id删除单个
      await this.service.delete({ id });
    } else if (classworkDetail_id) {
      // 根据范本作业id删除多个
      await this.service.delete({ classworkDetail_id });
    }
    return true;
  }

  @Post('/bulkCreate/:classworkDetail_id', { summary: '批量创建' })
  async bulkCreate(
    @Param('classworkDetail_id') classworkDetail_id: string,
    @Body() info: any
  ) {
    if (!classworkDetail_id) {
      throw new CustomError('缺少必要参数');
    }
    return this.service.bulkCreate(classworkDetail_id, info);
  }

  @Post('/savePaper/:classworkDetail_id', { summary: '保存试卷' })
  async savePaper(
    @Param('classworkDetail_id') classworkDetail_id: string,
    @Body() info: any
  ) {
    if (!classworkDetail_id) {
      throw new CustomError('缺少必要参数');
    }
    return this.service.savePaper(classworkDetail_id, info);
  }
}
