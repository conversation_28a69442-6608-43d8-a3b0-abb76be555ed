import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ComposerTestPaperDetailService } from '../service/composer_test_paper_detail.service';
import { CustomError } from '../error/custom.error';

@Controller('/composer_test_paper_detail')
export class ComposerTestPaperDetailController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ComposerTestPaperDetailService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;
    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({ query: queryInfo, offset, limit });
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: number) {
    await this.service.delete({ id });
    return true;
  }

  @Get('/paper_detail', { summary: '查询试卷下所有试题' })
  async getPaperStructureDetail(@Query('id') id: number) {
    if (id === undefined) {
      throw new CustomError('未指定试卷大题！');
    }
    const res = await this.service.getPaperDetail(id);
    return res;
  }
}
