import { Inject, Logger, Provide } from '@midwayjs/core';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import {
  // ClassWork,
  ClassWorkDetail,
  ClassWorkQuestion,
  ClassWorkStruct,
  // TextbookCatalog,
} from '../entity';
import { SystemQuestions } from '../model';
import { InjectEntityModel } from '@midwayjs/typegoose';
import { ReturnModelType } from '@typegoose/typegoose';
import { CustomError } from '../error/custom.error';
import { QuestionService } from './question.service';
import { randomUUID } from '@midwayjs/core/dist/util/uuid';

@Provide()
export class ClassWorkDetailService extends BaseService<ClassWorkDetail> {
  @Inject()
  ctx: Context;

  @Logger()
  logger;

  @Inject()
  questionService: QuestionService;

  constructor() {
    super('课时作业范本详情');
  }

  getModel = () => {
    return ClassWorkDetail;
  };

  @InjectEntityModel(SystemQuestions)
  systemQuestions: ReturnModelType<typeof SystemQuestions>;

  /**
   * 获取单个
   * @param id id
   * @memberof ClassWorkDetailService
   */
  async show(id) {
    return await ClassWorkDetail.findOne({
      where: { id },
      include: [{ model: ClassWorkQuestion }, { model: ClassWorkStruct }],
    });
  }

  /**
   * 创建课时作业范本详情
   * @param {*} body
   * @return {*} 创建结果
   * @memberof ClassWorkDetailService
   */
  async create(body: any) {
    const { questions, structs, ...info } = body;
    const transaction = await ClassWorkDetail.sequelize.transaction();
    try {
      // 获取最大排序索引
      const maxIndex = await this.getMaxIndex(
        info.class_work_id,
        info.textbookCatalog_id
      );
      // 创建作业范本详情
      const res = await ClassWorkDetail.create(
        { ...info, sort_order: maxIndex + 1 },
        { transaction }
      );
      // 创建作业时有题目信息，一并创建
      if (questions && questions.length > 0) {
        const data = questions.map((item, index) => {
          return {
            ...item,
            classworkDetail_id: res.id,
            sort_order: index + 1,
          };
        });
        await ClassWorkQuestion.bulkCreate(data, { transaction });
      }
      // 创建作业时有结构信息，一并创建
      if (structs && structs.length > 0) {
        const structArr = structs.map((item, index) => {
          return {
            ...item,
            classworkDetail_id: res.id,
            sort_order: index + 1,
          };
        });
        await ClassWorkStruct.bulkCreate(structArr, { transaction });
      }

      //  else {
      //   // 获取课时作业范本信息
      //   const classwork = await ClassWork.findOne({
      //     where: { id: info.class_work_id },
      //   });
      //   // 批量创建课时作业题目
      //   await this.createBulk(
      //     JSON.stringify(classwork),
      //     JSON.stringify(res),
      //     transaction
      //   );
      // }
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 获取最大排序索引
   * @param {*} class_work_id 课时作业范本id
   * @param {*} textbookCatalog_id 教材目录id
   * @return {*} 最大排序索引
   * @memberof ClassWorkDetailService
   */
  async getMaxIndex(class_work_id, textbookCatalog_id) {
    const maxIndex = await ClassWorkDetail.max('sort_order', {
      where: {
        class_work_id,
        textbookCatalog_id,
      },
    });
    return (maxIndex as number) || 0;
  }
  /**
   * 导入课时范本详情
   * @param filePath 文件路径
   * @param questionInfo 试题信息
   * @param info 详情数据
   * @returns
   */
  async importClassWorkDetails(filePath: string, questionInfo: any, info: any) {
    const transaction = await ClassWorkDetail.sequelize.transaction();
    // 创建作业详情
    try {
      // 获取最大排序索引
      const maxIndex = await this.getMaxIndex(
        info.class_work_id,
        info.textbookCatalog_id
      );
      // 创建作业范本详情
      const res = await ClassWorkDetail.create(
        { ...info, sort_order: maxIndex + 1 },
        { transaction }
      );
      // 先导入试题
      const questions = await this.questionService.importQuestions(
        filePath,
        questionInfo
      );
      // 作业详情试题
      // 作业详情结构
      if (questions && questions.length > 0) {
        const workQuestions = [];
        const questionIds = [];
        questions.forEach((item, index) => {
          questionIds.push(item._id);
          workQuestions.push({
            classworkDetail_id: res.id,
            sort_order: index + 1,
            question_id: item._id,
            source_table: item.tableName,
          });
        });
        const defaultStruct = {
          id: `hidden_${randomUUID()}`,
          name: '暂无分组',
          classworkDetail_id: res.id,
          sort_order: 1,
          questionIds: questionIds,
        };
        await ClassWorkQuestion.bulkCreate(workQuestions, { transaction });
        await ClassWorkStruct.create(defaultStruct, { transaction });
      }
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      this.logger.error(
        `Failed to importClassWorkDetails, message is ${error.message},stack is ${error.stack}`
      );
      throw new CustomError(error.message);
    }
  }
}
