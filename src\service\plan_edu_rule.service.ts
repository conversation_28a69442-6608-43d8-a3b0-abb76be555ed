import { Inject, Provide } from '@midwayjs/core';
import { PlanEduRule } from '../entity/plan_edu_rule.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class PlanEduRuleService extends BaseService<PlanEduRule> {
  @Inject()
  ctx: Context;

  constructor() {
    super('教育部门规则');
  }
  getModel = () => {
    return PlanEduRule;
  };
}
