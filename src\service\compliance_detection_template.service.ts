import { Inject, Provide } from '@midwayjs/core';
import {
  ComplianceDetectionTemplate,
  ComplianceDetectionTemplateDetail,
  ComplianceDetectionTemplateQuestion,
  ComplianceDetectionTemplateStruct,
  TextbookCatalog,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { randomUUID } from '@midwayjs/core/dist/util/uuid';

@Provide()
export class ComplianceDetectionTemplateService extends BaseService<ComplianceDetectionTemplate> {
  @Inject()
  ctx: Context;

  constructor() {
    super('达标检测范本');
  }
  getModel = () => {
    return ComplianceDetectionTemplate;
  };

  async create(info) {
    const transaction =
      await ComplianceDetectionTemplate.sequelize.transaction();
    try {
      if (info.template_id) {
        const res = await ComplianceDetectionTemplate.create(info, {
          transaction,
        });
        // 根据范本id，获取引用范本的作业详情、试题、结构信息
        const details = await ComplianceDetectionTemplateDetail.findAll({
          where: { template_id: info.template_id },
          order: [['id', 'ASC']],
          include: [
            { model: ComplianceDetectionTemplateQuestion },
            { model: ComplianceDetectionTemplateStruct },
          ],
        });
        // 批量复制作业详情、试题、结构信息
        // await this.copyDetails(details, res, transaction);
        await this.copyDetailsNew(details, res, transaction);

        await transaction.commit();
        return res;
      } else {
        const res = await ComplianceDetectionTemplate.create(info, {
          transaction,
        });
        await transaction.commit();
        return res;
      }
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 复制达标检测范本作业详情、试题、结构信息
   * @param details 达标检测范本详情
   * @param detectionTemplate 当前新创建的达标检测范本
   * @param transaction 事务
   */
  async copyDetails(details, detectionTemplate, transaction) {
    if (details && details.length > 0) {
      for (const item of details) {
        const { questions, structs, ...info } = item.toJSON();
        // 复制达标检测作业信息
        const detailInfo = {
          template_id: detectionTemplate.id, //达标检测范本id
          catalogIds: info.catalogIds, //教材目录ids
          catalogs: info.catalogs, //教材目录
          name: info.name, //名称
          detection_type: info.detection_type, //检测类型 单元 期中 期末
          status: info.status, //状态 草稿 发布
        };
        const templateDetail = await ComplianceDetectionTemplateDetail.create(
          detailInfo,
          { transaction }
        );

        // 批量复制达标检测试题信息
        if (questions && questions.length > 0) {
          const questionData = questions.map(question => ({
            detail_id: templateDetail.id, //范本详情id
            question_id: question.question_id, //试题id
            sort_order: question.sort_order, //排序
            source_table: question.source_table, //试题来源表
          }));
          await ComplianceDetectionTemplateQuestion.bulkCreate(questionData, {
            transaction,
          });
        }

        // 批量复制达标检测作业结构信息
        if (structs && structs.length > 0) {
          const structData = structs.map(struct => ({
            id: struct.id
              ? `${struct.id}_${templateDetail.id}`
              : `hidden_${randomUUID()}`, //结构id
            detail_id: templateDetail.id, //范本详情id
            name: struct.name, //结构名称
            questionIds: struct.questionIds, //试题ids
            sort_order: struct.sort_order, //排序
          }));

          await ComplianceDetectionTemplateStruct.bulkCreate(structData, {
            transaction,
          });
        }
      }
    }
  }

  /**
   * 复制达标检测范本作业详情、试题、结构信息  性能优化
   * @param details 作业详情信息
   * @param detectionTemplate 当前新建的达标检测范本信息
   * @param transaction 事务
   */
  async copyDetailsNew(details, detectionTemplate, transaction) {
    // 定义一个映射表
    const idMapping = {};

    if (details && details.length > 0) {
      // 批量创建作业详情信息
      const detailArr = await details.map(item => {
        return {
          template_id: detectionTemplate.id, //达标检测范本id
          catalogIds: item.catalogIds, //教材目录ids
          catalogs: item.catalogs, //教材目录
          name: item.name, //名称
          detection_type: item.detection_type, //检测类型 单元 期中 期末
          // status: item.status, //状态 草稿 发布 使用默认 草稿 状态
          textbookChecklist_id: item.textbookChecklist_id, //教材名录id
        };
      });
      const detailRes = await ComplianceDetectionTemplateDetail.bulkCreate(
        detailArr,
        { transaction }
      );

      // 创建映射关系
      detailRes.forEach((item, index) => {
        const oldId = details[index].id;
        idMapping[oldId] = item.id;
      });

      // 批量复制达标检测试题信息
      const allQuestions = [];
      details.forEach(detail => {
        const newDetailId = idMapping[detail.id];
        if (Array.isArray(detail.questions)) {
          detail.questions.forEach(question => {
            allQuestions.push({
              detail_id: newDetailId, //范本详情id
              question_id: question.question_id, //试题id
              sort_order: question.sort_order, //排序
              source_table: question.source_table, //试题来源表
            });
          });
        }
      });
      await ComplianceDetectionTemplateQuestion.bulkCreate(allQuestions, {
        transaction,
      });

      // 批量复制达标检测作业结构信息
      const allStructs = [];
      details.forEach(detail => {
        const newDetailId = idMapping[detail.id];
        if (Array.isArray(detail.struct)) {
          detail.struct.forEach(struct => {
            allStructs.push({
              id: struct.id
                ? `${struct.id}_${newDetailId}`
                : `hidden_${randomUUID()}`, //结构id
              detail_id: newDetailId, //范本详情id
              name: struct.name, //结构名称
              questionIds: struct.questionIds, //试题ids
              sort_order: struct.sort_order, //排序
            });
          });
        }
      });
      await ComplianceDetectionTemplateStruct.bulkCreate(allStructs, {
        transaction,
      });
    }
  }

  /**
   * 更新达标检测范本
   * @param id 达标检测范本id
   * @param info 更新数据
   */
  async update(id, info) {
    const transaction =
      await ComplianceDetectionTemplate.sequelize.transaction();
    try {
      // 获取范本详情，拿到教材名录id
      const templateInfo = await ComplianceDetectionTemplate.findOne({
        where: { id },
        attributes: ['textbookChecklist_id', 'template_type'],
      });
      // 发布前先判断单元范本下是否有作业，如果没有则提示用户是否继续发布
      if (
        info.status === '发布' &&
        info.flag === false &&
        templateInfo.template_type === '单元'
      ) {
        // 根据教材名录id，获取当前范本的教材目录
        const catalogs = await TextbookCatalog.findAll({
          where: {
            textbookChecklist_id: templateInfo.textbookChecklist_id,
            parent_id: null, // 只获取一级目录
          },
          order: [['id', 'ASC']],
        });

        const catalogIds = catalogs.map(v => v.id.toString());
        // 判断对应目录下是否存在未创建的作业
        // 根据范本id获取范本详情信息，判断哪个单元没有创建作业
        const templateDetails = await ComplianceDetectionTemplateDetail.findAll(
          {
            where: { template_id: id },
          }
        );

        // 如果没有创建作业，提示用户是否继续发布
        if (templateDetails.length === 0) {
          throw new CustomError('该范本下未创建作业，是否继续发布');
        }
        // 提取所有作业目录节点的 catalogIds
        const existingCatalogIds = templateDetails
          .map(detail => detail.catalogIds)
          .flat();

        // 筛选出没有作业的目录节点
        const missingCatalogIds = catalogIds.filter(
          id => !existingCatalogIds.includes(id)
        );
        if (missingCatalogIds.length > 0) {
          throw new CustomError('该范本下存在未创建作业的单元，是否继续发布');
        }
        // 如果校验不存在问题，直接更新范本状态及范本下的作业状态
        await ComplianceDetectionTemplate.update(info, {
          where: { id },
          transaction,
        });
        await ComplianceDetectionTemplateDetail.update(
          { status: '发布' },
          { where: { template_id: id }, transaction }
        );
      } else if (
        info.status === '发布' &&
        info.flag === false &&
        templateInfo.template_type === '期中期末'
      ) {
        // 如果期中期末范本，并且是发布，则去判断该范本下是否有作业
        const templateDetails = await ComplianceDetectionTemplateDetail.findAll(
          {
            where: { template_id: id },
          }
        );

        // 如果没有创建作业，提示用户是否继续发布
        if (templateDetails.length === 0) {
          throw new CustomError('该范本下未创建作业，是否继续发布');
        }

        // 如果校验不存在问题，直接更新范本状态及范本下的作业状态
        await ComplianceDetectionTemplate.update(info, {
          where: { id },
          transaction,
        });
        await ComplianceDetectionTemplateDetail.update(
          { status: '发布' },
          { where: { template_id: id }, transaction }
        );
      } else if (info.status === '发布' && info.flag === true) {
        // 如果是发布范本更新范本状态以及范本下的作业状态
        await ComplianceDetectionTemplate.update(info, {
          where: { id },
          transaction,
        });
        await ComplianceDetectionTemplateDetail.update(
          { status: '发布' },
          { where: { template_id: id }, transaction }
        );
      } else {
        await ComplianceDetectionTemplate.update(info, {
          where: { id },
          transaction,
        });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }
}
