import { Inject, Provide } from '@midwayjs/core';
import { KnowledgePoint } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { isEmpty, isNil, isUndefined } from 'lodash';
import { Util } from '../common/Util';
import { CustomError } from '../error/custom.error';

@Provide()
export class KnowledgePointService extends BaseService<KnowledgePoint> {
  @Inject()
  ctx: Context;

  constructor() {
    super('知识点');
  }
  getModel = () => {
    return KnowledgePoint;
  };

  /**
   * 逐级查询学科下的知识点
   * @param subjectId 学科id
   * @param parentId 上级id
   */
  async getKnowledgePointList(subjectId: number, parentId?: number) {
    if (isNil(subjectId) || isUndefined(subjectId)) {
      throw new CustomError('未指定学科！');
    }
    const pid = isNil(parentId) || isUndefined(parentId) ? null : parentId;
    return KnowledgePoint.findAll({
      where: {
        subjectId,
        pid,
      },
      order: [['id', 'ASC']],
      attributes: ['id', 'name', 'pid'],
    });
  }

  /**
   * 查询学科下知识点树
   * @param subjectId 学科id
   */
  async getKnowledgePointTreeBySubject(subjectId: number) {
    if (isNil(subjectId) || isUndefined(subjectId)) {
      throw new CustomError('未指定学科！');
    }
    const knowledgePointList = await KnowledgePoint.findAll({
      where: { subjectId },
      attributes: ['id', 'name', 'pid'],
      order: [['id', 'ASC']],
    });
    if (isEmpty(knowledgePointList)) return [];
    return Util.buildTree(
      knowledgePointList.map(k => k.toJSON()),
      'pid'
    );
  }
}
