import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import {
  Textbook,
  TextbookChecklist,
  Subject,
  Enterprise,
  ComplianceDetectionTemplate,
} from '.';
import { TEMPLATE_STATUS } from '../common/Constants';

export interface ComplianceDetectionDesignAttributes {
  /** id，主键 */
  id: number;
  /** 教材版本id */
  textbook_id: number;
  /** 教材名录ID */
  textbookChecklist_id: number;
  /** 达标检测设计名称 */
  name: string;
  /** 学段code */
  grade_section_code: string;
  /** 学段 */
  grade_section_name: string;
  /** 学科id */
  subject_id: number;
  /** 学科 */
  subject_name: string;
  /** 年级code */
  grade_code: string;
  /** 年级 */
  grade_name: string;
  /** 教材版本 */
  textbook_version: string;
  /** 册次 */
  volume: string;
  /** 来源code */
  source_code?: string;
  /** 来源 */
  source?: string;
  /** 达标检测范本id */
  template_id?: number;
  /** 企业id */
  enterprise_id?: number;
  /** 检测类型 */
  template_type: string;
  /** 状态 */
  status: string;
  /** 创建人id */
  creator_id: number;
  /** 创建人 */
  creator_name: string;
  textbook?: Textbook;
  textbookChecklist?: TextbookChecklist;
  subject?: Subject;
  template?: ComplianceDetectionTemplate;
  enterprise?: Enterprise;
}

@Table({
  tableName: 'compliance_detection_design',
  timestamps: true,
  comment: '达标检测设计表',
})
export class ComplianceDetectionDesign
  extends Model<ComplianceDetectionDesignAttributes>
  implements ComplianceDetectionDesignAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'id，主键',
  })
  id: number;

  @ForeignKey(() => Textbook)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材版本id',
  })
  textbook_id: number;

  @BelongsTo(() => Textbook, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  textbook?: Textbook;

  @ForeignKey(() => TextbookChecklist)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材名录ID',
  })
  textbookChecklist_id: number;

  @BelongsTo(() => TextbookChecklist, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  textbookChecklist?: TextbookChecklist;

  @Column({
    type: DataType.STRING(255),
    allowNull: false,
    comment: '达标检测设计名称',
  })
  name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '学段code',
  })
  grade_section_code: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '学段',
  })
  grade_section_name: string;

  @ForeignKey(() => Subject)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '学科id',
  })
  subject_id: number;

  @BelongsTo(() => Subject, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  subject?: Subject;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '学科',
  })
  subject_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '年级code',
  })
  grade_code: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: false,
    comment: '年级',
  })
  grade_name: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '教材版本',
  })
  textbook_version: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: false,
    comment: '册次',
  })
  volume: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '来源code',
  })
  source_code?: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '来源',
  })
  source?: string;

  @ForeignKey(() => Enterprise)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '企业id',
  })
  enterprise_id?: number;

  @BelongsTo(() => Enterprise)
  enterprise?: Enterprise;

  @ForeignKey(() => ComplianceDetectionTemplate)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '达标检测范本id',
  })
  template_id?: number;

  @BelongsTo(() => ComplianceDetectionTemplate, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  template?: ComplianceDetectionTemplate;

  @Column({
    type: DataType.ENUM('单元', '期中期末'),
    allowNull: false,
    defaultValue: '单元',
    comment: '检测类型',
  })
  template_type: string;

  @Column({
    type: DataType.ENUM(TEMPLATE_STATUS.DRAFT, TEMPLATE_STATUS.PUBLISHED),
    allowNull: false,
    defaultValue: TEMPLATE_STATUS.DRAFT,
    comment: '状态',
  })
  status: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '创建人id',
  })
  creator_id: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '创建人',
  })
  creator_name: string;
}
