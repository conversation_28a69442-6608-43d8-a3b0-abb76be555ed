import {
  Body,
  Controller,
  Del,
  Fields,
  Files,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { QuestionService } from '../service/question.service';
import { CustomError } from '../error/custom.error';
import { MONGO_MODEL_KEY } from '../common/Constants';
import { UploadFileInfo, UploadMiddleware } from '@midwayjs/busboy';

@Controller('/common_question')
export class CommonQuestionController {
  @Inject()
  ctx: Context;

  @Inject()
  service: QuestionService;

  @Post('/list', { summary: '查询列表' })
  async findQuestions(@Query() query: any, @Body() questions: any) {
    if (!questions || !questions.length) {
      throw new CustomError('参数错误');
    }
    const result = await this.service.findQuestions(questions, query);
    return result;
  }

  @Get('/page', { summary: '分页查询指定题库试题列表' })
  async getQuestionsByPage(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    const result = await this.service.findQuestionsByFilter(
      queryInfo,
      Number(offset),
      Number(limit)
    );
    return result;
  }

  @Post('/associate/:type', { summary: '创建父子题' })
  async createAssociateQuestion(
    @Body() body: any,
    @Param('type') sourceTable: string
  ) {
    if (!body) {
      throw new CustomError('请先设置题目内容');
    }
    return await this.service.createAssociateQuestion(body, sourceTable);
  }

  @Post('/associate_update/:pid/:type', { summary: '编辑父子题' })
  async updateAssociateQuestion(
    @Param('pid') pid: string,
    @Param('type') sourceTable: string,
    @Body() body: any
  ) {
    if (!body) {
      throw new CustomError('请先设置题目内容');
    }
    return await this.service.updateAssociateQuestion(body, pid, sourceTable);
  }

  @Del('/associate/:pid/:type', { summary: '根据父题id删除关联的子题' })
  async deleteAssociateQuestion(
    @Param('pid') pid: string,
    @Param('type') sourceTable: string
  ) {
    if (!pid) {
      throw new CustomError('未指定要删除的题目');
    }
    await this.service.deleteQuestionByPid(pid, sourceTable);
    return true;
  }

  @Get('/:id/:type', { summary: '按ID查询单个' })
  async show(@Param('id') id: string, @Param('type') sourceTable: string) {
    const res = await this.service.showQuestion(id, sourceTable);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/:type', { summary: '新增' })
  async create(@Body() info: any, @Param('type') sourceTable: string) {
    const res = await this.service.create(info, sourceTable);
    return res;
  }

  @Put('/:id/:type', { summary: '更新' })
  async update(
    @Param('id') id: string,
    @Param('type') sourceTable: string,
    @Body() body: any
  ) {
    await this.service.update(id, body, sourceTable);
    return true;
  }

  @Del('/:id/:type', { summary: '删除' })
  async destroy(@Param('id') id: string, @Param('type') sourceTable: string) {
    await this.service.destroy(id, sourceTable);
    return true;
  }

  @Del('/bulk/:type', { summary: '批量删除' })
  async destroyBulk(@Body() body: any, @Param('type') sourceTable: string) {
    const { ids } = body;
    await this.service.destroyBulk(ids, sourceTable);
    return true;
  }

  @Post('/bulk/:type', { summary: '批量修改' })
  async updateBulk(@Body() body: any, @Param('type') sourceTable: string) {
    const { ids, data } = body;
    return await this.service.updateBulk(ids, data, sourceTable);
  }

  @Post('/analysis', { summary: '分析试题列表信息' })
  async analysisQuestions(@Body() body: any) {
    const { type, questions, query } = body;
    if (!questions || !questions.length) {
      throw new CustomError('参数错误');
    }
    const result = await this.service.analysisQuestions(questions, type, query);
    return result;
  }

  @Post('/get_filter_ids/:type', { summary: '获取可替换的试题' })
  async findReplaceQuestions(
    @Param('type') sourceTable: string,
    @Body() body: any
  ) {
    return await this.service.findReplaceQuestions(body, sourceTable);
  }

  @Get('/get_count/:type', { summary: '条件查询试题个数' })
  async findQuestionCountByFilter(
    @Query() query: any,
    @Param('type') sourceTable: string
  ) {
    return await this.service.findQuestionCountByFilter(query, sourceTable);
  }

  @Post('/get_list/:type', { summary: '获取不同题库试题列表' })
  async getQuestionByTable(@Param('type') sourceTable: any, @Body() body: any) {
    let { offset, limit } = body;
    const {
      offset: o,
      limit: l,
      current,
      pageSize,
      order,
      ...queryInfo
    } = body;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }

    // 如果catalogIds 数组中存在新增的目录, old_id为空字符串, 则根据册次id查询整本书所有试题
    if (queryInfo.catalogIds && queryInfo.catalogIds.includes('')) {
      delete queryInfo.catalogIds;
    }

    return await this.service.findQuestionsByFilter(
      queryInfo,
      offset,
      limit,
      order,
      sourceTable
    );
  }

  @Put('/complete/:type', { summary: '补全试题数据' })
  async completeQuestion(@Param('type') sourceTable: string) {
    await this.service.completeQuestionStem(
      sourceTable || MONGO_MODEL_KEY.SYSTEM
    );
  }

  @Post('/import', { middleware: [UploadMiddleware], summary: '导入试题' })
  async importQuestions(
    @Files() files: Array<UploadFileInfo>,
    @Fields() fieldRecord: Record<string, string>
  ) {
    if (!files.length) {
      throw new CustomError('请先选择文件！');
    }
    const file = files[0];
    const { fields } = fieldRecord;
    if (!fields) {
      throw new CustomError('缺少必要参数！');
    }
    const questionInfo = JSON.parse(fields);
    if (!questionInfo.tableName) {
      throw new CustomError('未指定试题表！');
    }
    if (!questionInfo.subject) {
      throw new CustomError('未指定学科！');
    }
    return await this.service.importQuestions(file.data, questionInfo);
  }

  @Put('/change_question_tier/:type', { summary: '更新题目分层数据' })
  async changeQuestionTier(@Param('type') sourceTable: string) {
    await this.service.changeQuestionTier(
      sourceTable || MONGO_MODEL_KEY.SYSTEM
    );
    return true;
  }

  @Get('/get_user_tag/:userId', { summary: '根据用户id获取自定义的试题标记' })
  async getUserTag(@Param('userId') userId: string) {
    if (!userId) {
      throw new CustomError('用户id不能为空');
    }
    return await this.service.getUserTag(userId);
  }

  @Post('/bulk_add_user_tag', { summary: '批量添加试题标记' })
  async bulkAddQuestionTag(@Body() body: any) {
    return await this.service.bulkAddUserTag(body);
  }
}
