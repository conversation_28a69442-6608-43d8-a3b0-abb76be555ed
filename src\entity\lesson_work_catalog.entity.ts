import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
  HasMany,
} from 'sequelize-typescript';
import { TextbookChecklist, LessonWorkDesign } from '.';

export interface LessonWorkCatalogAttributes {
  /** id */
  id: number;
  /** 原本目录id */
  old_id?: number;
  /** 原本的目录名称 */
  old_title?: string;
  /** 课时作业设计id */
  lessonWorkDesign_id: number;
  /** 教材名录id */
  textbookChecklist_id: number;
  /** 名称 */
  title: string;
  /** 父id */
  parent_id?: number;
  /** 排序 */
  sort_order: number;
  lessonWorkDesign?: LessonWorkDesign;
  textbookChecklist?: TextbookChecklist;
  parent?: LessonWorkCatalog;
  children?: LessonWorkCatalog[];
}

@Table({
  tableName: 'lesson_work_catalogs',
  timestamps: true,
  comment: '课时作业设计目录表',
})
export class LessonWorkCatalog
  extends Model<LessonWorkCatalogAttributes>
  implements LessonWorkCatalogAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'id',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '原本目录id',
  })
  old_id?: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '原本的目录名称',
  })
  old_title?: string;

  @ForeignKey(() => LessonWorkDesign)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '课时作业设计id',
  })
  lessonWorkDesign_id: number;

  @BelongsTo(() => LessonWorkDesign, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  lessonWorkDesign?: LessonWorkDesign;

  @ForeignKey(() => TextbookChecklist)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材名录id',
  })
  textbookChecklist_id: number;

  @BelongsTo(() => TextbookChecklist)
  textbookChecklist?: TextbookChecklist;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '名称',
  })
  title: string;

  @ForeignKey(() => LessonWorkCatalog)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '父id',
  })
  parent_id?: number;

  @BelongsTo(() => LessonWorkCatalog, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  parent?: LessonWorkCatalog;

  @HasMany(() => LessonWorkCatalog, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  children?: LessonWorkCatalog[];

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '排序',
  })
  sort_order: number;
}
