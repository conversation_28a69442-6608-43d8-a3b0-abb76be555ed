import { Inject, Provide } from '@midwayjs/core';
import {
  ClassWork,
  ClassWorkDetail,
  ClassWorkQuestion,
  ClassWorkStruct,
  TextbookCatalog,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { randomUUID } from '@midwayjs/core/dist/util/uuid';

@Provide()
export class ClassWorkService extends BaseService<ClassWork> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课时作业范本');
  }
  getModel = () => {
    return ClassWork;
  };

  /**
   * 创建课时作业范本
   * @param info 数据信息
   */
  async create(info) {
    const transaction = await ClassWork.sequelize.transaction();
    try {
      // 如果使用系统内置范本，则需要将该范本对应的作业详情、试题、结构信息一并复制一份，否则直接创建范本信息即可
      if (info.template_id) {
        const res = await ClassWork.create(info, { transaction });
        // 根据范本id，获取引用范本的作业详情、试题、结构信息
        const details = await ClassWorkDetail.findAll({
          where: { class_work_id: info.template_id },
          order: [['sort_order', 'ASC']],
          include: [{ model: ClassWorkQuestion }, { model: ClassWorkStruct }],
          transaction,
        });
        // 批量复制作业详情、试题、结构信息
        // await this.copyDetails(details, res, transaction);
        await this.copyDetailsNew(details, res, transaction);

        await transaction.commit();
        return res;
      } else {
        const res = await ClassWork.create(info, { transaction });
        await transaction.commit();
        return res;
      }
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 批量复制作业详情、试题、结构信息 弃用
   * @param details 作业详情信息
   * @param classWork 当前新创建的范本信息
   * @param transaction 事务
   */
  async copyDetails(details, classWork, transaction) {
    if (details && details.length > 0) {
      for (const item of details) {
        const { questions, structs, ...info } = item.toJSON();
        // 复制课时作业信息
        const detailInfo = {
          class_work_id: classWork.id,
          textbookCatalog_id: info.textbookCatalog_id,
          name: info.name,
          sort_order: info.sort_order,
          status: info.status,
          creator_id: classWork.creator_id,
          creator_name: classWork.creator_name,
        };
        const detail = await ClassWorkDetail.create(detailInfo, {
          transaction,
        });

        // 批量插入结构信息
        if (structs && structs.length > 0) {
          const structData = structs.map(struct => ({
            id: struct.id
              ? `${struct.id}_${detail.id}`
              : `hidden_${randomUUID()}`, //结构id
            classworkDetail_id: detail.id, //作业详情id
            name: struct.name, //结构名称
            questionIds: struct.questionIds, //试题ids
            sort_order: struct.sort_order, //排序
          }));
          await ClassWorkStruct.bulkCreate(structData, { transaction });
        }

        // 批量插入试题信息
        if (questions && questions.length > 0) {
          const questionData = questions.map(question => ({
            classworkDetail_id: detail.id, //课时作业详情id
            question_id: question.question_id, //试题id
            sort_order: question.sort_order, //排序
            source_table: question.source_table, //试题来源表
          }));
          await ClassWorkQuestion.bulkCreate(questionData, { transaction });
        }
      }
    }
  }

  /**
   * 批量复制作业详情、试题、结构信息  性能优化
   * @param details 作业详情信息
   * @param classWork 当前课时作业范本信息
   * @param transaction 事务
   */
  async copyDetailsNew(details, classWork, transaction) {
    // 定义一个映射表
    const idMapping = {};

    if (details && details.length > 0) {
      // 批量创建作业详情信息
      const detailArr = await details.map(item => {
        return {
          class_work_id: classWork.id, //课时作业范本id
          textbookCatalog_id: item.textbookCatalog_id, //教材目录id
          name: item.name, //名称
          sort_order: item.sort_order, //排序
          // status: item.status, //状态 使用默认的 草稿
          creator_id: classWork.creator_id, //创建人id
          creator_name: classWork.creator_name, //创建人
        };
      });
      const detailRes = await ClassWorkDetail.bulkCreate(detailArr, {
        transaction,
      });

      // 创建映射关系
      detailRes.forEach((item, index) => {
        const oldId = details[index].id;
        idMapping[oldId] = item.id;
      });

      // 批量创建试题信息
      const allQuestions = [];
      details.forEach(detail => {
        const newDetailId = idMapping[detail.id];
        if (Array.isArray(detail.questions)) {
          detail.questions.forEach(question => {
            allQuestions.push({
              classworkDetail_id: newDetailId, //课时作业详情id
              question_id: question.question_id, //试题id
              sort_order: question.sort_order, //排序
              source_table: question.source_table, //试题来源表
            });
          });
        }
      });
      await ClassWorkQuestion.bulkCreate(allQuestions, { transaction });

      // 批量创建结构信息
      const allStructs = [];
      details.forEach(detail => {
        const newDetailId = idMapping[detail.id];
        if (Array.isArray(detail.structs)) {
          detail.structs.forEach(struct => {
            allStructs.push({
              id: struct.id
                ? `${struct.id}_${newDetailId}`
                : `hidden_${randomUUID()}`, //结构id
              classworkDetail_id: newDetailId, //作业详情id
              name: struct.name, //结构名称
              questionIds: struct.questionIds, //试题ids
              sort_order: struct.sort_order, //排序
            });
          });
        }
      });
      await ClassWorkStruct.bulkCreate(allStructs, { transaction });
    }
  }

  /**
   * 更新课时作业范本
   * @param id 范本
   * @param info 更新数据
   */
  async update(id, info) {
    const transaction = await ClassWork.sequelize.transaction();
    try {
      if (info.status === '发布' && info.flag === false) {
        // 获取范本详情，拿到教材名录id
        const classWorkInfo = await ClassWork.findOne({
          where: { id },
          attributes: ['textbookChecklist_id'],
        });
        // 根据教材名录id，获取当前范本的教材目录
        const catalogs = await TextbookCatalog.findAll({
          where: {
            textbookChecklist_id: classWorkInfo.textbookChecklist_id,
            parent_id: { [Op.not]: null }, //排除根节点，即排除单元目录
          },
          order: [['id', 'ASC']],
        });
        // 判断对应目录下是否存在为创建的作业
        for (const item of catalogs) {
          const count = await ClassWorkDetail.count({
            where: { textbookCatalog_id: item.id },
          });
          if (count === 0) {
            throw new CustomError(
              `${item.title}目录下未创建作业，是否继续发布`
            );
          }
        }
        // 如果校验不存在问题，直接更新范本状态以及范本下的作业状态
        await ClassWork.update(info, { where: { id }, transaction });
        await ClassWorkDetail.update(
          { status: info.status },
          { where: { class_work_id: id }, transaction }
        );
      } else if (info.status === '发布' && info.flag === true) {
        // 如果是发布范本更新范本状态以及范本下的作业状态
        await ClassWork.update(info, { where: { id }, transaction });
        await ClassWorkDetail.update(
          { status: info.status },
          { where: { class_work_id: id }, transaction }
        );
      } else {
        await ClassWork.update(info, { where: { id }, transaction });
      }
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }
}
