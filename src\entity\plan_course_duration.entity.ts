import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { PlanCourseSubject, Enterprise } from '.';

export interface PlanCourseDurationAttributes {
  /** 主键 */
  id: number;
  /** 课程科目id */
  course_subject_id?: number;
  /** 课程名称 */
  course_name?: string;
  /** 每周课时数 */
  weekly_hours?: number;
  /** 每节课时长 */
  class_hour?: string;
  /** 年级code */
  grade_code?: string;
  /** 年级名称 */
  grade_name?: string;
  /** 学校id */
  enterprise_id?: number;
  /** 学年学期code */
  semester_code?: string;
  /** 学年学期 */
  semester_name?: string;
}

@Table({
  tableName: 'plan_course_duration',
  timestamps: true,
  comment: '课程时长表',
})
export class PlanCourseDuration
  extends Model<PlanCourseDurationAttributes>
  implements PlanCourseDurationAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @ForeignKey(() => PlanCourseSubject)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '课程科目id',
  })
  course_subject_id?: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '课程名称',
  })
  course_name?: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '每周课时数',
  })
  weekly_hours?: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '每节课时长',
  })
  class_hour?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '年级code',
  })
  grade_code?: string;

  @Column({
    type: DataType.STRING(100),
    allowNull: true,
    comment: '年级名称',
  })
  grade_name?: string;

  @ForeignKey(() => Enterprise)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '学校id',
  })
  enterprise_id?: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: true,
    comment: '学年学期code',
  })
  semester_code?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学年学期',
  })
  semester_name?: string;

  @BelongsTo(() => PlanCourseSubject, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  course?: PlanCourseSubject;

  @BelongsTo(() => Enterprise, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  enterprise?: Enterprise;
}
