import { Table, Column, Model, DataType } from 'sequelize-typescript';

export interface PlanTrainTargetAttributes {
  /** 主键 */
  id: number;
  /** 目标名称 */
  name?: string;
  /** 目标描述 */
  describe?: string;
  /** 目标解析 */
  analy?: string;
}

@Table({
  tableName: 'plan_train_target',
  timestamps: true,
  comment: '培养目标表',
})
export class PlanTrainTarget
  extends Model<PlanTrainTargetAttributes>
  implements PlanTrainTargetAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
  })
  name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '目标描述',
  })
  describe?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '目标解析',
  })
  analy?: string;
}
