import { createApp, close, createHttpRequest } from '@midwayjs/mock';
import { Framework } from '@midwayjs/koa';

describe('test/controller/dashboard.test.ts', () => {

  it('should GET /dashboard/overview', async () => {
    // create app
    const app = await createApp<Framework>();

    // make request
    const result = await createHttpRequest(app).get('/dashboard/overview');

    // use expect by jest
    expect(result.status).toBe(200);
    expect(result.body).toHaveProperty('enterprise_count');
    expect(result.body).toHaveProperty('user_count');
    expect(result.body).toHaveProperty('timestamp');
    expect(typeof result.body.enterprise_count).toBe('number');
    expect(typeof result.body.user_count).toBe('number');

    // close app
    await close(app);
  });

  it('should GET /dashboard/enterprises/count', async () => {
    // create app
    const app = await createApp<Framework>();

    // make request
    const result = await createHttpRequest(app).get('/dashboard/enterprises/count');

    // use expect by jest
    expect(result.status).toBe(200);
    expect(result.body).toHaveProperty('count');
    expect(result.body).toHaveProperty('timestamp');
    expect(typeof result.body.count).toBe('number');

    // close app
    await close(app);
  });

  it('should GET /dashboard/users/count', async () => {
    // create app
    const app = await createApp<Framework>();

    // make request
    const result = await createHttpRequest(app).get('/dashboard/users/count');

    // use expect by jest
    expect(result.status).toBe(200);
    expect(result.body).toHaveProperty('count');
    expect(result.body).toHaveProperty('timestamp');
    expect(typeof result.body.count).toBe('number');

    // close app
    await close(app);
  });

  it('should GET /dashboard/enterprises/statistics', async () => {
    // create app
    const app = await createApp<Framework>();

    // make request
    const result = await createHttpRequest(app).get('/dashboard/enterprises/statistics');

    // use expect by jest
    expect(result.status).toBe(200);
    expect(result.body).toHaveProperty('total');
    expect(result.body).toHaveProperty('by_type');
    expect(result.body).toHaveProperty('by_area');
    expect(result.body).toHaveProperty('timestamp');
    expect(typeof result.body.total).toBe('number');
    expect(Array.isArray(result.body.by_type)).toBe(true);
    expect(Array.isArray(result.body.by_area)).toBe(true);

    // close app
    await close(app);
  });

  it('should GET /dashboard/users/statistics', async () => {
    // create app
    const app = await createApp<Framework>();

    // make request
    const result = await createHttpRequest(app).get('/dashboard/users/statistics');

    // use expect by jest
    expect(result.status).toBe(200);
    expect(result.body).toHaveProperty('total');
    expect(result.body).toHaveProperty('by_grade_section');
    expect(result.body).toHaveProperty('by_subject');
    expect(result.body).toHaveProperty('by_status');
    expect(result.body).toHaveProperty('timestamp');
    expect(typeof result.body.total).toBe('number');
    expect(Array.isArray(result.body.by_grade_section)).toBe(true);
    expect(Array.isArray(result.body.by_subject)).toBe(true);
    expect(Array.isArray(result.body.by_status)).toBe(true);

    // close app
    await close(app);
  });

  it('should GET /dashboard/overview with query parameters', async () => {
    // create app
    const app = await createApp<Framework>();

    // make request with query parameters
    const result = await createHttpRequest(app)
      .get('/dashboard/overview')
      .query({ 
        province_code: '110000',
        enterprise_type: 'school'
      });

    // use expect by jest
    expect(result.status).toBe(200);
    expect(result.body).toHaveProperty('enterprise_count');
    expect(result.body).toHaveProperty('user_count');
    expect(result.body).toHaveProperty('timestamp');

    // close app
    await close(app);
  });
});
