import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { TextbookCatalogService } from '../service/textbook_catalog.service';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';

@Controller('/textbook_catalog')
export class TextbookController {
  @Inject()
  ctx: Context;

  @Inject()
  service: TextbookCatalogService;

  @Get('/', { summary: '查询教材目录列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    // title支持模糊查询
    if (queryInfo.title) {
      queryInfo.title = {
        [Op.like]: `%${queryInfo.title}%`,
      };
    }
    return this.service.findAll({
      query: queryInfo,
      offset,
      limit,
      order: [['sort_order', 'ASC']],
    });
  }

  @Get('/:id', { summary: '按ID查询教材目录' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定教材目录');
    }
    return res;
  }

  @Post('/', { summary: '新增教材目录' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新教材目录' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除教材目录' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }

  @Post('/insert/:id', { summary: '插入节点' })
  async insert(
    @Param('id') id: number,
    @Body() body: { title: string; action: 'insert' | 'append' }
  ) {
    const { title, action } = body;
    if (!title || !action) {
      throw new CustomError('参数错误');
    }
    if (!['insert', 'append'].includes(action)) {
      throw new CustomError('操作类型错误');
    }
    return this.service.insert(id, title, action);
  }

  @Get('/points/:catalogId', { summary: '根据章节id获取对应知识点信息' })
  async findKnowledgePointsByCatalogId(@Param('catalogId') catalogId: number) {
    const res = await this.service.findAll({
      query: { id: catalogId },
      include: [
        {
          association: 'knowledgePoints',
          through: {
            attributes: [],
          },
        },
      ],
    });
    return res;
  }
}
