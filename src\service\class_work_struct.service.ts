import { Inject, Provide } from '@midwayjs/core';
import { ClassWorkQuestion, ClassWorkStruct } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';

@Provide()
export class ClassWorkStructService extends BaseService<ClassWorkStruct> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课时作业结构');
  }
  getModel = () => {
    return ClassWorkStruct;
  };

  /**
   * 获取最大排序索引
   * @param classworkDetail_id 课时作业范本详情id
   * @returns 最大排序索引
   */
  async getMaxIndex(classworkDetail_id) {
    const maxIndex = await ClassWorkStruct.max('sort_order', {
      where: {
        classworkDetail_id,
      },
    });
    return (maxIndex as number) || 0;
  }

  /**
   * 批量创建课时作业范本结构
   * @param detail_id 课时作业范本详情id
   * @param info 结构信息
   * @returns
   */
  async bulkCreate(detail_id, info) {
    const transaction = await ClassWorkStruct.sequelize.transaction();
    try {
      // 删除对应课时作业结构
      await ClassWorkStruct.destroy({
        where: {
          classworkDetail_id: detail_id,
        },
        transaction,
      });
      // 批量创建
      const data = info.map((item, index) => {
        return {
          ...item,
          classworkDetail_id: detail_id,
          sort_order: index + 1,
        };
      });
      const res = await ClassWorkStruct.bulkCreate(data, { transaction });
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 全部清空
   * @param classworkDetail_id 课时作业范本详情id
   * @returns
   */
  async bulkDelete(classworkDetail_id) {
    const transaction = await ClassWorkStruct.sequelize.transaction();
    try {
      // 清除对应课时作业结构
      await ClassWorkStruct.destroy({
        where: {
          classworkDetail_id,
        },
        transaction,
      });
      // 清除对应课时作业下的题目
      await ClassWorkQuestion.destroy({
        where: {
          classworkDetail_id,
        },
        transaction,
      });
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }
}
