import { Controller, Post, Body, Inject } from '@midwayjs/core';
import { AuthService } from '../service/auth.service';

@Controller('/auth')
export class AuthController {
  @Inject()
  authService: AuthService;

  @Post('/login', { summary: '账密登录' })
  async login(
    @Body() { username, password }: { username: string; password: string }
  ) {
    const token = await this.authService.login(username, password);
    return { token };
  }

  @Post('/refresh', { summary: '刷新token' })
  async refresh(@Body() { refreshToken }: { refreshToken: string }) {
    const token = await this.authService.refresh(refreshToken);
    return { token };
  }
}
