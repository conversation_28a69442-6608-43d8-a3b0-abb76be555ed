import {
  Table,
  Column,
  Model,
  DataType,
  // ForeignKey,
  // BelongsTo,
} from 'sequelize-typescript';
// import { PlanCourseSubject } from './plan_course_subject.entity'; // 假设存在一个 Course 实体类
// import { Enterprise } from './enterprise.entity'; // 假设存在一个 Enterprise 实体类

export interface StandardCourseImplementAttributes {
  /** 主键ID */
  id: number;
  /** 名称 */
  name?: string;
  /** 描述 */
  describe?: string;
  /** 解析 */
  analy?: string;
  // /** 课程id */
  // course_id?: number;
  // /** 课程名称 */
  // course_name?: string;
  // /** 实施维度（如教学建议、过程性评价等） */
  // implementation_dimension?: string;
  // /** 省级教育行政部门要求 */
  // provincial_requirements?: string;
  // /** 学校课程实施职责 */
  // school_requirements?: string;
  // /** 制度规范 */
  // system_specifications?: string;
  // /** 教学改革方向 */
  // teaching_reform_direction?: string;
  // /** 评价改革重点 */
  // evaluation_reform_focus?: string;
  // /** 培训要求 */
  // training_requirements?: string;
  // /** 教科研要求 */
  // teaching_research_requirements?: string;
  // /** 监测与督导要求 */
  // monitoring_supervision?: string;
  // /** 过程性评价 */
  // process_evaluation?: string;
  // /** 学业水平考试 */
  // academic_level_exam?: string;
  // /** 教材理解与学习 */
  // textbook_understanding?: string;
  // /** 学校id */
  // enterprise_id?: number;
}

@Table({
  tableName: 'standard_course_implement',
  timestamps: true,
  comment: '课程实施标准表',
})
export class StandardCourseImplement
  extends Model<StandardCourseImplementAttributes>
  implements StandardCourseImplementAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质名称',
  })
  name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质描述',
  })
  describe?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '课程性质解析',
  })
  analy?: string;

  // @ForeignKey(() => PlanCourseSubject)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '课程id',
  // })
  // course_id?: number;

  // @Column({
  //   type: DataType.STRING(64),
  //   allowNull: true,
  //   comment: '课程名称',
  // })
  // course_name?: string;

  // @Column({
  //   type: DataType.STRING(255),
  //   allowNull: true,
  //   comment: '实施维度（如教学建议、过程性评价等）',
  // })
  // implementation_dimension?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '省级教育行政部门要求',
  // })
  // provincial_requirements?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '学校课程实施职责',
  // })
  // school_requirements?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '制度规范',
  // })
  // system_specifications?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '教学改革方向',
  // })
  // teaching_reform_direction?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '评价改革重点',
  // })
  // evaluation_reform_focus?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '培训要求',
  // })
  // training_requirements?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '教科研要求',
  // })
  // teaching_research_requirements?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '监测与督导要求',
  // })
  // monitoring_supervision?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '过程性评价',
  // })
  // process_evaluation?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '学业水平考试',
  // })
  // academic_level_exam?: string;

  // @Column({
  //   type: DataType.TEXT,
  //   allowNull: true,
  //   comment: '教材理解与学习',
  // })
  // textbook_understanding?: string;

  // @ForeignKey(() => Enterprise)
  // @Column({
  //   type: DataType.INTEGER,
  //   allowNull: true,
  //   comment: '学校id',
  // })
  // enterprise_id?: number;

  // @BelongsTo(() => PlanCourseSubject, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // course?: PlanCourseSubject;

  // @BelongsTo(() => Enterprise, {
  //   onDelete: 'CASCADE',
  //   onUpdate: 'CASCADE',
  // })
  // enterprise?: Enterprise;
}
