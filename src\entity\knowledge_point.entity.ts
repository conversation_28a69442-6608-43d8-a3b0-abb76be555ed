import {
  BelongsTo,
  BelongsToMany,
  Column,
  DataType,
  ForeignKey,
  HasMany,
  Model,
  Table,
} from 'sequelize-typescript';
import { Subject } from './subject.entity';
import { TextbookCatalog } from './textbook_catalog.entity';
import { TextbookCatalogKnowledgePoint } from '.';

/**
 * 知识点实体属性接口
 */
export interface KnowledgePointAttributes {
  /** 知识点ID */
  id: number;
  /** 知识点名称 */
  name: string;
  /** 父级知识点ID */
  pid: number;
  /** 所属学科ID */
  subjectId: number;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

@Table({
  tableName: 'knowledge_point',
  timestamps: true,
  comment: '知识点表',
})
export class KnowledgePoint
  extends Model<KnowledgePointAttributes>
  implements KnowledgePointAttributes
{
  @Column({
    primaryKey: true,
    autoIncrement: true,
    comment: 'id',
    type: DataType.INTEGER,
  })
  id: number;

  @Column({
    comment: '知识点名称',
    allowNull: false,
    type: DataType.STRING(255),
  })
  name: string;

  @ForeignKey(() => KnowledgePoint)
  @Column({
    comment: '父级id',
    allowNull: true,
    type: DataType.INTEGER,
  })
  pid: number;

  @BelongsTo(() => KnowledgePoint)
  parent: KnowledgePoint;

  @HasMany(() => KnowledgePoint, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  children: KnowledgePoint[];

  @ForeignKey(() => Subject)
  @Column({
    comment: '学科id',
    allowNull: false,
    type: DataType.INTEGER,
    field: 'subject_id',
  })
  subjectId: number;
  @BelongsTo(() => Subject)
  subject: Subject;

  @Column({
    comment: '创建时间',
    type: DataType.DATE,
    field: 'created_at',
  })
  createdAt: Date;
  @Column({
    comment: '更新时间',
    type: DataType.DATE,
    field: 'updated_at',
  })
  updatedAt: Date;

  @BelongsToMany(() => TextbookCatalog, () => TextbookCatalogKnowledgePoint)
  textbookCatalogs!: TextbookCatalogKnowledgePoint[];
}
