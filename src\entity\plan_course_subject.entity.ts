import {
  Table,
  Column,
  Model,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { PlanCourseSystem, Subject } from '.';

export interface PlanCourseSubjectAttributes {
  /** 主键 */
  id: number;
  /** 课程学制表id */
  course_system_id?: number;
  /** 科目id */
  subject_id?: number;
  /** 课程科目名称 */
  subject_name?: string;
  /** 是否启用 */
  is_used?: boolean;
  /** 学校id */
  enterprise_id?: number;
  courseSystem?: PlanCourseSystem;
  subject?: Subject;
}

@Table({
  tableName: 'plan_course_subject',
  timestamps: true,
  comment: '课程科目表',
})
export class PlanCourseSubject
  extends Model<PlanCourseSubjectAttributes>
  implements PlanCourseSubjectAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @ForeignKey(() => PlanCourseSystem)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '课程学制表id',
  })
  course_system_id?: number;

  @BelongsTo(() => PlanCourseSystem, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  courseSystem?: PlanCourseSystem;

  @ForeignKey(() => Subject)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '科目id',
  })
  subject_id?: number;

  @BelongsTo(() => Subject, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  subject?: Subject;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '课程科目名称',
  })
  subject_name?: string;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: true,
    defaultValue: true,
    comment: '是否启用',
  })
  is_used?: boolean;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '学校id',
  })
  enterprise_id?: number;
}
