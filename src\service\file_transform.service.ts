import { Util } from '../common/Util';
import { Inject, Logger, Provide } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { render, Options } from 'ejs';
import { writeFile, readFile } from 'fs/promises';
import { tmpdir } from 'os';
import { CustomError } from '../error/custom.error';
import { randomUUID } from '@midwayjs/core/dist/util/uuid';
import { join } from 'path';
@Provide()
export class FileTransformService {
  @Inject()
  ctx: Context;
  @Logger()
  logger;

  /**
   * 使用模板引擎将JSON数据转换为HTML
   * @param templateName 模板名称
   * @param data 数据对象
   * @param options 模板选项
   * @returns 渲染后的HTML
   */
  async renderTemplate(
    templateName: string,
    data: any,
    options?: Options
  ): Promise<string> {
    try {
      // 模板目录路径
      const templateDir = join(__dirname, '../../src/view');
      const templatePath = join(templateDir, `${templateName}.ejs`);

      // 读取模板文件
      const templateContent = await readFile(templatePath, 'utf8');

      // 使用EJS渲染模板
      const html = render(templateContent, data, {
        ...options,
        filename: templatePath,
      });

      return html;
    } catch (error) {
      this.logger.error(
        `模板渲染失败: ${error.message}, stack: ${error.stack}`
      );
      throw new CustomError('模板渲染失败，请检查模板文件或数据格式');
    }
  }

  /**
   * 将试题JSON数据通过模板转换为HTML
   * @param data 试题数据
   * @param styleOptions 样式选项
   * @returns 渲染后的HTML
   */
  async jsonToHtmlByTemplate(
    data: any,
    styleOptions?: {
      fontSize?: string;
      lineHeight?: string;
      contentWidth?: string;
      contentHeight?: string;
      fontFamily?: string;
    }
  ): Promise<string> {
    // 数据预处理，可以在这里对数据结构进行转换或规范化
    const processedData = this.preprocessData(data);

    // 添加样式选项
    processedData.styles = {
      fontFamily: styleOptions?.fontFamily || '微软雅黑',
      fontSize: styleOptions?.fontSize || '16px',
      lineHeight: styleOptions?.lineHeight || '1.5',
    };

    // 使用模板渲染HTML
    return this.renderTemplate('exam-paper', processedData);
  }

  /**
   * 预处理试题数据
   * @param data 原始数据
   * @returns 处理后的数据
   */
  private preprocessData(data: any): any {
    if (!data || !data.name || !Array.isArray(data.infos)) {
      throw new CustomError('无效的试题数据结构');
    }

    // 深拷贝数据以避免修改原始数据
    const processedData = JSON.parse(JSON.stringify(data));

    // 处理每一个题型分组
    processedData.infos.forEach((section, sectionIndex) => {
      // 添加全局题号计数器
      let globalQuestionIndex = 1;
      // 添加中文序号
      const titleIndex = section.type === '暂无分组' ? 0 : sectionIndex;
      section.sectionTitle = Util.getChineseNumber(titleIndex);
      // 为每道题添加题号
      if (Array.isArray(section.questions)) {
        section.questions.forEach(question => {
          question.questionNumber = globalQuestionIndex++;

          // 处理子题
          if (Array.isArray(question.children)) {
            question.children.forEach((child, childIndex) => {
              child.childNumber = childIndex + 1;
            });
          }
        });
      }
    });

    return processedData;
  }

  /**
   * 将JSON通过模板转换为HTML后导出为Word文档
   * @param data 数据
   * @param styleOptions 样式选项
   * @returns 导出路径
   */
  async jsonToHtmlByTemplateSave(
    data: any,
    styleOptions?: {
      fontSize?: string;
      lineHeight?: string;
      fontFamily?: string;
    }
  ): Promise<string> {
    const htmlContent = await this.jsonToHtmlByTemplate(data, styleOptions);
    // 将htmlContent写入到临时文件中
    const tempPath = join(
      tmpdir(),
      `${Date.now()}-${randomUUID().slice(0, 8)}.html`
    );
    await writeFile(tempPath, htmlContent);
    return tempPath;
  }
}
