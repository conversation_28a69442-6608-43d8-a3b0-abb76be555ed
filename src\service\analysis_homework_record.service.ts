import { Inject, Provide } from '@midwayjs/core';
import {
  AnalysisHomeworkRecord,
  Dictionary,
  Enterprise,
  Semester,
  LessonWorkDesign,
  LessonWorkDesignDetail,
  LessonWorkDesignQuestion,
  ComplianceDetectionDesignDetail,
  ComplianceDetectionDesign,
  ComplianceDetectionDesignQuestion,
  Subject,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { Op, col, fn, literal } from 'sequelize';
import * as dayjs from 'dayjs';
import {
  SCHOOL_SYSTEM_TO_GRADE_SECTION_MAP,
  GRADE_SECTION_TO_GRADE_MAP,
} from '../common/Constants';
// import { groupBy } from 'lodash';
import { QuestionService } from './question.service';
import { SchoolQuestions, SystemQuestions } from '../model';
import { InjectEntityModel } from '@midwayjs/typegoose';
import { ReturnModelType } from '@typegoose/typegoose';

@Provide()
export class AnalysisHomeworkRecordService extends BaseService<AnalysisHomeworkRecord> {
  @Inject()
  ctx: Context;

  @Inject()
  questionService: QuestionService;

  @InjectEntityModel(SchoolQuestions)
  schoolQuestions: ReturnModelType<typeof SchoolQuestions>;

  @InjectEntityModel(SystemQuestions)
  systemQuestions: ReturnModelType<typeof SystemQuestions>;

  constructor() {
    super('作业分析记录表');
  }
  getModel = () => {
    return AnalysisHomeworkRecord;
  };

  /**
   * 按条件汇总学校总数、作业总数、平均时长、试题数量等数据
   * @param query 请求参数
   * @returns
   */
  async statistic(query) {
    const { semester_code, city_code, area_code } = query;
    // 组装查询条件
    const queryOption: any = {};
    if (semester_code) {
      queryOption.semester_code = semester_code;
    }
    if (city_code) {
      queryOption.city_code = city_code;
    }
    if (area_code) {
      queryOption.area_code = area_code;
    }

    const currentMonth = `${dayjs().month() + 1}月`; // month() 返回 0-11，所以要 +1
    queryOption.month = currentMonth;
    // 获取当前月份的 学校总数、作业总数、平均时长、试题数量
    const currentMonthRes = await AnalysisHomeworkRecord.findAll({
      where: queryOption,
      attributes: [
        [fn('COUNT', fn('DISTINCT', col('enterprise_code'))), 'school_count'],
        // 小学 学校数
        [
          fn(
            'COUNT',
            fn(
              'DISTINCT',
              literal(
                "CASE WHEN grade_section_name = '小学' THEN enterprise_code END"
              )
            )
          ),
          'primary_school_count',
        ],
        // 初中  学校数
        [
          fn(
            'COUNT',
            fn(
              'DISTINCT',
              literal(
                "CASE WHEN grade_section_name = '初中' THEN enterprise_code END"
              )
            )
          ),
          'middle_school_count',
        ],
        // 高中  学校数
        [
          fn(
            'COUNT',
            fn(
              'DISTINCT',
              literal(
                "CASE WHEN grade_section_name = '高中' THEN enterprise_code END"
              )
            )
          ),
          'high_school_count',
        ],
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        // [fn('AVG', col('average_time')), 'total_average_time'],
        [fn('ROUND', fn('AVG', col('average_time')), 2), 'total_average_time'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
    });

    // 查询当月按学段统计的 作业次数、平均作业时长、全市试题总量
    const currentMonthStageInfo = await AnalysisHomeworkRecord.findAll({
      attributes: [
        'grade_section_name',
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        [fn('AVG', col('average_time')), 'avg_average_time'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
      where: queryOption,
      group: ['grade_section_name'],
      order: [
        [
          literal(`
            CASE grade_section_name
              WHEN '小学' THEN 1
              WHEN '初中' THEN 2
              WHEN '高中' THEN 3
              ELSE 4
            END
          `),
          'ASC',
        ],
      ],
    });

    const lastMonth = `${dayjs().subtract(1, 'month').month() + 1}月`;
    queryOption.month = lastMonth;
    // 获取上月的 学校总数、作业总数、平均时长、试题数量
    const lastMonthRes = await AnalysisHomeworkRecord.findAll({
      where: queryOption,
      attributes: [
        [fn('COUNT', fn('DISTINCT', col('enterprise_code'))), 'school_count'],
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        [fn('AVG', col('average_time')), 'total_average_time'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
    });

    // 查询上月按学段统计 作业次数、平均作业时长、全市试题总量
    const lastMonthStageInfo = await AnalysisHomeworkRecord.findAll({
      attributes: [
        'grade_section_name',
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        // [fn('AVG', col('average_time')), 'avg_average_time'],
        [fn('ROUND', fn('AVG', col('average_time')), 2), 'total_average_time'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
      where: queryOption,
      group: ['grade_section_name'],
      order: [
        [
          literal(`
            CASE grade_section_name
              WHEN '小学' THEN 1
              WHEN '初中' THEN 2
              WHEN '高中' THEN 3
              ELSE 4
            END
          `),
          'ASC',
        ],
      ],
    });

    // 计算全市作业次数较上月增加的百分比
    const currentMonthTotalHomework = Number(
      currentMonthRes[0].get('total_homework_number') || 0
    );
    const lastMonthTotalHomework = Number(
      lastMonthRes[0].get('total_homework_number') || 0
    );

    const homework_number_last_month = Math.floor(
      ((currentMonthTotalHomework - lastMonthTotalHomework) /
        lastMonthTotalHomework) *
        100
    );

    // 计算全市作业时长较上月增加的百分比
    const currentMonthTotalAverageTime = Number(
      currentMonthRes[0].get('total_average_time') || 0
    );
    const lastMonthTotalAverageTime = Number(
      lastMonthRes[0].get('total_average_time') || 0
    );

    const average_time_last_month = Math.floor(
      ((currentMonthTotalAverageTime - lastMonthTotalAverageTime) /
        lastMonthTotalAverageTime) *
        100
    );

    // 计算全市试题数量较上月增加的百分比
    const currentMonthTotalQuestionNumber = Number(
      currentMonthRes[0].get('total_question_number') || 0
    );
    const lastMonthTotalQuestionNumber = Number(
      lastMonthRes[0].get('total_question_number') || 0
    );

    const question_number_last_month = Math.floor(
      ((currentMonthTotalQuestionNumber - lastMonthTotalQuestionNumber) /
        lastMonthTotalQuestionNumber) *
        100
    );

    // 计算各阶段作业次数较上月增加的百分比
    const growthRates = [];
    let growthObj = {};
    currentMonthStageInfo.forEach(currentItem => {
      const gradeSectionName = currentItem.get('grade_section_name');
      const lastItem = lastMonthStageInfo.find(
        item => item.get('grade_section_name') === gradeSectionName
      );

      if (!lastItem) return; // 如果没有上月数据，跳过

      // 转换为数字
      const currentHomework = Number(
        currentItem.get('total_homework_number') || 0
      );
      const lastHomework = Number(lastItem.get('total_homework_number') || 0);

      const currentAverageTime = Number(
        currentItem.get('avg_average_time') || 0
      );
      const lastAverageTime = Number(lastItem.get('avg_average_time') || 0);

      const currentQuestion = Number(
        currentItem.get('total_question_number') || 0
      );
      const lastQuestion = Number(lastItem.get('total_question_number') || 0);

      // 计算增长率
      const homeworkGrowth =
        lastHomework !== 0
          ? Math.floor(((currentHomework - lastHomework) / lastHomework) * 100)
          : 0;

      const averageTimeGrowth =
        lastAverageTime !== 0
          ? Math.floor(
              ((currentAverageTime - lastAverageTime) / lastAverageTime) * 100
            )
          : 0;

      const questionGrowth =
        lastQuestion !== 0
          ? Math.floor(((currentQuestion - lastQuestion) / lastQuestion) * 100)
          : 0;

      growthRates.push({
        grade_section_name: gradeSectionName,
        homework_growth_rate: `${homeworkGrowth}%`,
        average_time_growth_rate: `${averageTimeGrowth}%`,
        question_growth_rate: `${questionGrowth}%`,
      });

      if (growthRates.length > 0) {
        growthObj = growthRates.reduce(
          (acc, item) => {
            const section = item.grade_section_name;
            acc.homework_growth_rate[section] = item.homework_growth_rate;
            acc.average_time_growth_rate[section] =
              item.average_time_growth_rate;
            acc.question_growth_rate[section] = item.question_growth_rate;
            return acc;
          },
          {
            homework_growth_rate: {},
            average_time_growth_rate: {},
            question_growth_rate: {},
          }
        );
      }
    });

    return {
      school_count: currentMonthRes[0].get('school_count'),
      school_count_primary: currentMonthRes[0].get('primary_school_count'),
      school_count_middle: currentMonthRes[0].get('middle_school_count'),
      school_count_high: currentMonthRes[0].get('high_school_count'),
      total_homework_number: currentMonthTotalHomework,
      homework_number_last_month: `${homework_number_last_month}%`,
      total_average_time: currentMonthTotalAverageTime,
      average_time_last_month: `${average_time_last_month}%`,
      total_question_number: currentMonthTotalQuestionNumber,
      question_number_last_month: `${question_number_last_month}%`,
      growthObj,
    };
  }

  /**
   * 根据条件获取不同区县下的作业汇总数据
   */
  async statisticHomeworkAnalysis(query) {
    const {
      semester_code,
      city_code,
      area_code,
      grade_section_code,
      grade_code,
      subject_id,
      enterprise_code,
    } = query;
    // 组装查询条件
    const queryOption: any = {};
    if (semester_code) {
      queryOption.semester_code = semester_code;
    }
    if (city_code) {
      queryOption.city_code = city_code;
    }
    if (area_code) {
      queryOption.area_code = area_code;
    }
    if (grade_section_code) {
      queryOption.grade_section_code = grade_section_code;
    }
    if (grade_code) {
      queryOption.grade_code = grade_code;
    }
    if (subject_id) {
      queryOption.subject_id = subject_id;
    }
    if (enterprise_code) {
      queryOption.enterprise_code = enterprise_code;
    }

    // 如果市级code存在，说明是统计市级下的区县数据
    if (city_code && !area_code) {
      const res = await AnalysisHomeworkRecord.findAll({
        where: queryOption,
        attributes: [
          'city_code',
          'city_name',
          'area_code',
          'area_name',
          'subject',
          'suggested_time',
          [fn('SUM', col('homework_number')), 'total_homework_number'],
          [fn('AVG', col('average_time')), 'total_average_time'],
          [fn('SUM', col('question_number')), 'total_question_number'],
        ],
        group: [
          'city_code',
          'city_name',
          'area_code',
          'area_name',
          'subject',
          'suggested_time',
        ],
      });
      return res;
    }

    // 如果area_code存在，说明是统计区县下的学校
    if (city_code && area_code && !grade_section_code) {
      const res = await AnalysisHomeworkRecord.findAll({
        where: queryOption,
        attributes: [
          'area_code',
          'area_name',
          'subject',
          'suggested_time',
          [fn('SUM', col('homework_number')), 'total_homework_number'],
          [fn('AVG', col('average_time')), 'total_average_time'],
          [fn('SUM', col('question_number')), 'total_question_number'],
        ],
        group: ['area_code', 'area_name', 'subject', 'suggested_time'],
      });
      return res;
    }

    // 如果grade_section_code存在，根据学校类型获取 各学段 统计汇总数据
    if (grade_section_code && !grade_code) {
      const res = await AnalysisHomeworkRecord.findAll({
        where: queryOption,
        attributes: [
          'grade_section_code',
          'grade_section_name',
          'enterprise_code',
          'enterprise_name',
          'subject',
          'suggested_time',
          [fn('SUM', col('homework_number')), 'total_homework_number'],
          [fn('AVG', col('average_time')), 'total_average_time'],
          [fn('SUM', col('question_number')), 'total_question_number'],
        ],
        group: [
          'grade_section_code',
          'grade_section_name',
          'enterprise_code',
          'enterprise_name',
          'subject',
          'suggested_time',
        ],
      });
      return res;
    }

    // 统计各年级的统计汇总数据
    if (grade_code && !subject_id) {
      const res = await AnalysisHomeworkRecord.findAll({
        where: queryOption,
        attributes: [
          'grade_code',
          'grade_name',
          'enterprise_code',
          'enterprise_name',
          'subject',
          'suggested_time',
          [fn('SUM', col('homework_number')), 'total_homework_number'],
          [fn('AVG', col('average_time')), 'total_average_time'],
          [fn('SUM', col('question_number')), 'total_question_number'],
        ],
        group: [
          'grade_code',
          'grade_name',
          'enterprise_code',
          'enterprise_name',
          'subject',
          'suggested_time',
        ],
      });
      return res;
    }

    // 根据学科统计汇总数据
    if (subject_id) {
      const res = await AnalysisHomeworkRecord.findAll({
        where: queryOption,
        attributes: [
          'subject_id',
          'subject',
          'enterprise_code',
          'enterprise_name',
          'suggested_time',
          [fn('SUM', col('homework_number')), 'total_homework_number'],
          [fn('AVG', col('average_time')), 'total_average_time'],
          [fn('SUM', col('question_number')), 'total_question_number'],
        ],
        group: [
          'subject_id',
          'subject',
          'enterprise_code',
          'enterprise_name',
          'suggested_time',
        ],
      });
      return res;
    }

    // 根据学校统计全校作业次数、平均作业时长、全校试题总量
    if (enterprise_code) {
      const res = await AnalysisHomeworkRecord.findAll({
        where: queryOption,
        attributes: [
          'grade_code',
          'grade_name',
          'subject',
          'suggested_time',
          [fn('SUM', col('homework_number')), 'total_homework_number'],
          [fn('AVG', col('average_time')), 'total_average_time'],
          [fn('SUM', col('question_number')), 'total_question_number'],
        ],
        group: ['grade_code', 'grade_name', 'subject', 'suggested_time'],
      });
      return res;
    }
  }

  /**
   * 按月统计试题数量和作业数量
   * @param query
   */
  async statisticMonth(query) {
    const {
      semester_code,
      city_code,
      area_code,
      grade_section_code,
      enterprise_code,
    } = query;

    const queryOption = {};
    if (semester_code) {
      queryOption['semester_code'] = semester_code;
    }
    if (city_code) {
      queryOption['city_code'] = city_code;
    }
    if (area_code) {
      queryOption['area_code'] = area_code;
    }
    if (grade_section_code) {
      queryOption['grade_section_code'] = grade_section_code;
    }
    if (enterprise_code) {
      queryOption['enterprise_code'] = enterprise_code;
    }

    const res = await AnalysisHomeworkRecord.findAll({
      where: queryOption,
      attributes: [
        'month',
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
      group: ['month'],
    });
    // return res;
    const growthRates = await this.calculateGrowthRates(res);
    return growthRates;
  }

  /**
   * 计算各月份汇总数据之间的增长率
   * @param data 按月汇总数据
   */
  async calculateGrowthRates(data) {
    return data.map((item, index) => {
      const { month, total_homework_number, total_question_number } =
        item.toJSON();

      const currentHomework = Number(total_homework_number) || 0;
      const currentQuestion = Number(total_question_number) || 0;

      if (index === 0) {
        return {
          month,
          total_homework_number: currentHomework,
          homework_growth_rate: 0,
          total_question_number: currentQuestion,
          question_growth_rate: 0,
        };
      }

      const prevItem = data[index - 1]?.toJSON();
      const prevHomework = Number(prevItem?.total_homework_number) || 0;
      const prevQuestion = Number(prevItem?.total_question_number) || 0;

      const homeworkGrowthRate = prevHomework
        ? (((currentHomework - prevHomework) / prevHomework) * 100).toFixed(2)
        : 0;

      const questionGrowthRate = prevQuestion
        ? (((currentQuestion - prevQuestion) / prevQuestion) * 100).toFixed(2)
        : 0;

      return {
        month,
        total_homework_number: currentHomework,
        homework_growth_rate: Number(homeworkGrowthRate),
        total_question_number: currentQuestion,
        question_growth_rate: Number(questionGrowthRate),
      };
    });
  }

  /**
   * 查询学校教师总数、作业次数、平均作业时长、试题总数
   * @param query
   */
  async statisticBySchool(query) {
    const { enterpris_code } = query;

    const queryOption: any = {};
    if (enterpris_code) {
      queryOption['enterprise_code'] = enterpris_code;
    }

    const currentMonth = `${dayjs().month() + 1}月`; // month() 返回 0-11，所以要 +1
    queryOption.month = currentMonth;
    // 统计当月作业次数、平均作业时长、试题总量
    const currentMonthRes = await AnalysisHomeworkRecord.findAll({
      where: queryOption,
      attributes: [
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        // [fn('AVG', col('average_time')), 'total_average_time'],
        [fn('ROUND', fn('AVG', col('average_time')), 2), 'total_average_time'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
    });

    const lastMonth = `${dayjs().subtract(1, 'month').month() + 1}月`;
    queryOption.month = lastMonth;
    // 获取上月的 作业总数、平均时长、试题数量
    const lastMonthRes = await AnalysisHomeworkRecord.findAll({
      where: queryOption,
      attributes: [
        [fn('SUM', col('homework_number')), 'total_homework_number'],
        // [fn('AVG', col('average_time')), 'total_average_time'],
        [fn('ROUND', fn('AVG', col('average_time')), 2), 'total_average_time'],
        [fn('SUM', col('question_number')), 'total_question_number'],
      ],
    });

    // 计算该校作业次数较上月增加的百分比
    const currentMonthTotalHomework = Number(
      currentMonthRes[0].get('total_homework_number') || 0
    );
    const lastMonthTotalHomework = Number(
      lastMonthRes[0].get('total_homework_number') || 0
    );

    const homework_number_last_month = Math.floor(
      ((currentMonthTotalHomework - lastMonthTotalHomework) /
        lastMonthTotalHomework) *
        100
    );

    // 计算该校作业时长较上月增加的百分比
    const currentMonthTotalAverageTime = Number(
      currentMonthRes[0].get('total_average_time') || 0
    );
    const lastMonthTotalAverageTime = Number(
      lastMonthRes[0].get('total_average_time') || 0
    );

    const average_time_last_month = Math.floor(
      ((currentMonthTotalAverageTime - lastMonthTotalAverageTime) /
        lastMonthTotalAverageTime) *
        100
    );

    // 计算该校试题数量较上月增加的百分比
    const currentMonthTotalQuestionNumber = Number(
      currentMonthRes[0].get('total_question_number') || 0
    );
    const lastMonthTotalQuestionNumber = Number(
      lastMonthRes[0].get('total_question_number') || 0
    );

    const question_number_last_month = Math.floor(
      ((currentMonthTotalQuestionNumber - lastMonthTotalQuestionNumber) /
        lastMonthTotalQuestionNumber) *
        100
    );

    return {
      teacher_number: 0, //TODO 教师总数，目前先写死
      total_homework_number: currentMonthRes[0].get('total_homework_number'), //作业总次数
      total_average_time: currentMonthRes[0].get('total_average_time'), //平均作业时长
      total_question_number: currentMonthRes[0].get('total_question_number'), //试题总量
      homework_number_last_month: `${homework_number_last_month}%`, //作业次数较上月
      average_time_last_month: `${average_time_last_month}%`, //平均作业时长较上月
      question_number_last_month: `${question_number_last_month}%`, //试题数量较上月
    };
  }

  /**
   * 自动汇总作业记录
   */
  async autoSummaryHomeworkRecord() {
    // 获取字典表中学制信息
    const schoolSystemList = await Dictionary.findAll({
      where: { type: 'school_system' },
    });
    console.log('schoolSystemList: ', schoolSystemList);
    // 获取当前激活的学年学期
    const semester = await Semester.findOne({ where: { status: 1 } });
    if (!semester) {
      console.log('未找到激活的学年学期');
      return;
    }
    // 获取所有的学校信息
    const enterprises = await Enterprise.findAll({ where: { code: '95278' } });
    // const enterprises = await Enterprise.findAll();

    // 格式化当前学年学期的时间范围 方便后续查询使用
    const startTime = new Date(semester.start_date);
    const endTime = new Date(semester.end_date);

    for (const enterprise of enterprises) {
      if (!enterprise.id || !enterprise.school_system) {
        continue;
      }
      // 根据学校的学制信息 拆分不同学段的数据入库
      const gradeSection =
        SCHOOL_SYSTEM_TO_GRADE_SECTION_MAP[enterprise.school_system];
      if (!gradeSection) {
        continue;
      }

      // 遍历不同学段
      for (const gradeSectionItem of gradeSection) {
        // 根据学段查学科
        const subjectList = await Subject.findAll({
          where: {
            grade_section: gradeSectionItem.code,
          },
        });
        if (!subjectList.length) {
          continue;
        }
        // 根据学段获取该学段对应的年级
        const gradeList = GRADE_SECTION_TO_GRADE_MAP[gradeSectionItem.code];
        if (gradeList.length === 0) {
          continue;
        }

        for (const grade of gradeList) {
          for (const subject of subjectList) {
            const baseQuery = {
              createdAt: { [Op.between]: [startTime, endTime] },
              enterprise_id: enterprise.id,
              grade_section_code: gradeSectionItem.code,
              grade_code: grade.code,
              subject_id: subject.id,
            };

            // 获取对应年级、学科的课时作业和达标作业总数
            const [workDesignCount, detectionDesignCount] = await Promise.all([
              LessonWorkDesignDetail.count({
                include: [{ model: LessonWorkDesign, where: baseQuery }],
              }),
              ComplianceDetectionDesignDetail.count({
                include: [
                  { model: ComplianceDetectionDesign, where: baseQuery },
                ],
              }),
            ]);
            console.log(workDesignCount);
            console.log('达标作业数量', detectionDesignCount);

            const homework_number = workDesignCount + detectionDesignCount;

            // 计算对应时长
            // 获取对应年级、学科 的 课时、达标范本详情
            const [workDesignDetailList, detectionDesignDetailList] =
              await Promise.all([
                LessonWorkDesignDetail.findAll({
                  include: [{ model: LessonWorkDesign, where: baseQuery }],
                }),
                ComplianceDetectionDesignDetail.findAll({
                  include: [
                    { model: ComplianceDetectionDesign, where: baseQuery },
                  ],
                }),
              ]);
            // console.log(workDesignDetailList.length);
            // console.log(detectionDesignDetailList.length);

            // 获取对应作业的id数组
            const designDetailIds = workDesignDetailList.map(item => item.id);

            const detectionDesignIds = detectionDesignDetailList.map(
              item => item.id
            );

            console.log('课时作业id', designDetailIds);
            console.log('达标范本ids', detectionDesignIds);

            // 根据对应id数组获取对应的试题信息
            const [questionList, detectionQuestionList] = await Promise.all([
              LessonWorkDesignQuestion.findAll({
                where: {
                  designDetail_id: {
                    [Op.in]: designDetailIds,
                  },
                },
              }),
              ComplianceDetectionDesignQuestion.findAll({
                where: {
                  designDetail_id: {
                    [Op.in]: detectionDesignIds,
                  },
                },
              }),
            ]);
            console.log(questionList.length);
            console.log(detectionQuestionList.length);

            const questions = questionList.map(item => {
              return {
                question_id: item.question_id,
                source_table: item.source_table,
              };
            });

            // 计算所有试题时长
            const questionDuration =
              await this.questionService.calculateDuration(questions);
            console.log('课时作业总时长', questionDuration);

            const detectionQuestions = detectionQuestionList.map(item => {
              return {
                question_id: item.question_id,
                source_table: item.source_table,
              };
            });

            const detectionQuestionDuration =
              await this.questionService.calculateDuration(detectionQuestions);
            console.log('达标作业总时长', detectionQuestionDuration);

            const totalDuration = questionDuration + detectionQuestionDuration;
            // 计算平均时长
            const average_time =
              homework_number > 0 && totalDuration > 0
                ? parseFloat((totalDuration / homework_number).toFixed(2))
                : 0;
            console.log(average_time);

            // 3.组装条件查询对应试题数量
            const match = {};
            // match['enterpriseCode'] = enterprise.code; //暂时注释，目前试题没有企业code信息
            match['grade.code'] = grade.code;
            match['subject.id'] = subject.id;
            match['gradeSection.code'] = gradeSectionItem.code;
            match['createdAt'] = {
              $gte: startTime,
              $lte: endTime,
            };

            //TODO 这里暂时先用系统题库数量
            console.log(match);
            const question_number = await this.systemQuestions
              .countDocuments(match)
              .exec();
            console.log('试题总数', question_number);

            // 组装当前学校对应数据
            // const schoolData = {
            //   semester_code: semester.code,
            //   semester_name: semester.term,
            //   mongth: startTime.getMonth() + 1,
            //   // year: startTime.getFullYear(), //暂时注释 目前未定义年份信息
            //   province_code: enterprise.province,
            //   province_name: enterprise.province_name,
            //   city_code: enterprise.city,
            //   city_name: enterprise.city_name,
            //   area_code: enterprise.area,
            //   area_name: enterprise.area_name,
            //   school_system_code: schoolSystem.code,
            // };
          }
        }
      }
    }
  }
}
