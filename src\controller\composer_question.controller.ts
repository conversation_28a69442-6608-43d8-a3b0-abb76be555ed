import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ComposerQuestionService } from '../service/composer_question.service';
import { CustomError } from '../error/custom.error';
import { ComposerPaperService } from '../service/composer_paper.service';
import { QuestionService } from '../service/question.service';

@Controller('/composer_question')
export class ComposerQuestionController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ComposerQuestionService;

  @Inject()
  composerPaperService: ComposerPaperService;

  @Inject()
  questionService: QuestionService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;
    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({ query: queryInfo, offset, limit });
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: number) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() info: any) {
    const res = await this.service.create(info);
    return res;
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: number, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/filter', { description: '根据条件删除试题篮数据' })
  async destroyQuery(@Query() query: any) {
    const { composerPaperId, questionBankId, type, sourceTable } = query;
    const where: any = {};
    // 组卷方案id
    if (composerPaperId) {
      where.composerPaperId = composerPaperId;
    }
    // 试题id
    if (questionBankId) {
      where.questionBankId = questionBankId;
    }
    // 试题类型
    if (type) {
      where.type = type;
    }
    if (sourceTable) {
      where.sourceTable = sourceTable;
    }
    await this.service.delete(where);
    return true;
  }

  @Get('/count', { description: '获取组卷统计' })
  async groupCount(@Query('composerPaperId') paperId: number) {
    const res = await this.service.groupCount(paperId);
    return res;
  }

  @Post('/bulk', { description: '批量创建' })
  async bulkCreate(@Body() body: any) {
    const { composerPaperId, questions } = body;
    if (!composerPaperId) {
      throw new CustomError('未指定组卷方案！');
    }
    if (!questions) {
      throw new CustomError('未指定试题信息！');
    }
    const res = await this.composerPaperService.findById(composerPaperId);
    if (!res) {
      throw new CustomError('未找到指定方案！');
    }
    await this.service.bulkCreate(questions, composerPaperId);
    return true;
  }

  @Get('/details', { summary: '获取试题篮包含试题详情' })
  async findQuestionsByComposerPaperId(
    @Query('composerPaperId') composerPaperId: number
  ) {
    const res = await this.composerPaperService.findById(composerPaperId);
    if (!res) {
      throw new CustomError('未找到指定方案！');
    }
    const tempQuestions = await this.service.findByFilter({
      composerPaperId,
    });
    const questionFilter = tempQuestions.map(item => {
      return {
        question_id: item.questionBankId,
        source_table: item.sourceTable,
      };
    });
    return await this.questionService.findQuestions(questionFilter);
  }
}
