import { Provide } from '@midwayjs/core';
import { Area } from '../entity/area.entity';
import { BaseService } from '../common/BaseService';

@Provide()
export class AreaService extends BaseService<Area> {
  constructor() {
    super('行政区划');
  }

  getModel() {
    return Area;
  }

  /**
   * 根据传入的行政区划代码获取省市区
   *
   * @param code 行政区划代码
   * @returns 省市区信息
   */
  async getS_S_Q(code: string): Promise<{
    province: string | null;
    province_name: string | null;
    city: string | null;
    city_name: string | null;
    area: string | null;
    area_name: string | null;
  }> {
    if (!code) {
      return {
        province: null,
        province_name: null,
        city: null,
        city_name: null,
        area: null,
        area_name: null,
      };
    }
    const current = await Area.findOne({
      where: {
        code,
      },
    });
    if (!current) {
      return {
        province: null,
        province_name: null,
        city: null,
        city_name: null,
        area: null,
        area_name: null,
      };
    }
    // 判断当前的code是省、市还是区
    if (code.endsWith('0000')) {
      // 省
      return {
        province: code,
        province_name: current.name,
        city: null,
        city_name: null,
        area: null,
        area_name: null,
      };
    }
    const province = await Area.findOne({
      where: {
        code: code.substring(0, 2) + '0000',
      },
    });
    if (code.endsWith('00')) {
      // 市
      return {
        province: province.name,
        province_name: province.name,
        city: code,
        city_name: current.name,
        area: null,
        area_name: null,
      };
    }
    const city = await Area.findOne({
      where: {
        code: code.substring(0, 4) + '00',
      },
    });
    // 区
    return {
      province: province.name,
      province_name: province.name,
      city: city.name,
      city_name: city.name,
      area: code,
      area_name: current.name,
    };
  }
}
