import { Inject, Provide } from '@midwayjs/core';
import { SimilarQuestion } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { Op } from 'sequelize';
import { CustomError } from '../error/custom.error';

@Provide()
export class SimilarQuestionService extends BaseService<SimilarQuestion> {
  @Inject()
  ctx: Context;

  constructor() {
    super('手动设置的相似题关系');
  }
  getModel = () => {
    return SimilarQuestion;
  };
  /**
   * 查询列表
   *
   * @param {*} query 查询条件
   * @param {*} offset 分页，跳过
   * @param {*} limit 分页，截取
   * @return {*} res
   * @memberof SimilarQuestionService
   */
  async getList(query, offset, limit) {
    const queryOption: any = {};
    const result: any = {};

    const hasPaging = offset !== undefined || limit !== undefined;
    if (hasPaging) {
      queryOption.offset = Number(offset) || 0;
      queryOption.limit = Number(limit) || 10;
    }

    if (query.question_id) {
      query[Op.or] = [
        { questionId: query.question_id },
        { similarQuestionId: query.question_id },
      ];
      delete query.question_id;
    }

    const where = {
      ...query,
      // 自定义查询参数参数
    };

    queryOption.where = where;

    // 其他参数
    queryOption.order = [['orderIndex']];

    const res = await SimilarQuestion.findAll(queryOption);
    result.list = res;

    if (hasPaging) {
      // 获取总数
      const total = await SimilarQuestion.count({
        where,
      });
      result.total = total || 0;
    }
    return result;
  }

  /**
   * 创建
   *
   * @param {*} data 数据
   * @return {*} res
   * @memberof SimilarQuestionService
   */
  async create(data) {
    const { ctx } = this;
    const { SimilarQuestion } = ctx.sqmodel;
    if (!data.orderIndex) {
      const maxOrderIndex = await SimilarQuestion.max('orderIndex', {
        where: { question_id: data.question_id },
      });
      data.orderIndex = maxOrderIndex;
    }
    const res = await SimilarQuestion.create(data);
    return res;
  }

  /**
   *
   * @param {string[]} ids 删除的id
   */
  async batchDestroy(ids) {
    await SimilarQuestion.destroy({
      where: {
        id: ids,
      },
    });
  }

  /**
   *
   * 批量试题关联知识点
   * @param {string[]} [data=[]] 关联的相似题和排序值
   * @memberof SimilarQuestionService
   */
  async bulkCreate(data = []) {
    let transaction;
    try {
      transaction = await SimilarQuestion.sequelize.transaction();
      const newData = data.map((v, index) => ({ ...v, orderIndex: index }));
      const res = await SimilarQuestion.bulkCreate(newData, {
        transaction,
      });
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error);
    }
  }

  /**
   * 批量设置相似题
   * @param {object[]} questionInfo 批量设置的数据
   * @return {object[]} 设置结果
   */
  async batchSetQuestions(questionInfo) {
    let transaction;
    let res;
    try {
      transaction = await SimilarQuestion.sequelize.transaction();
      // 先删除之前的所有题目关联
      const { questionId, data } = questionInfo;
      const query = {};
      query[Op.or] = [{ questionId }, { similarQuestionId: questionId }];
      await SimilarQuestion.destroy({ where: query, transaction });
      // 批量创建
      if (data.length) {
        const newData = data.map((v, index) => ({ ...v, orderIndex: index }));
        res = await SimilarQuestion.bulkCreate(newData, { transaction });
      }
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error);
    }
  }

  /**
   * 查询数量
   * @param query 查询条件
   * @returns
   */
  async count(query) {
    return await SimilarQuestion.count({
      where: query,
      distinct: true,
    });
  }
}
