import {
  Table,
  Column,
  Model,
  DataType,
  BeforeCreate,
  BeforeUpdate,
  BelongsTo,
  BelongsToMany,
  ForeignKey,
  HasMany,
  Index,
} from 'sequelize-typescript';
import * as bcrypt from 'bcryptjs';
import { Role, Enterprise } from './';
import { UserRole } from './user_role.entity';
import { SchoolHomework } from './school_homework.entity';

export interface UserAttributes {
  /** 主键 */
  id: number;
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
  /** 昵称 */
  nickname?: string;
  /** 头像 */
  avatar?: string;
  /** 手机号 */
  phone?: string;
  /** 邮箱 */
  email?: string;
  /** 状态 */
  isActive?: boolean;
  /** 学段code */
  grade_section_code?: string;
  /** 学段 */
  grade_section_name?: string;
  /** 年级 */
  grade?: string[];
  /** 学科id */
  subject_id?: number;
  /** 学科 */
  subject_name?: string;
  /** 所属单位id */
  enterprise_id?: number;
  roles?: Role[];
  enterprise?: Enterprise;
  schoolHomeworks?: SchoolHomework[];
}

@Table({ tableName: 'users', timestamps: true, comment: '用户表' })
export class User extends Model<UserAttributes> implements UserAttributes {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  })
  id: number;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '用户名',
  })
  @Index({ name: 'username', unique: true })
  username: string;

  @Column({
    type: DataType.STRING,
    allowNull: false,
    comment: '密码',
  })
  password: string;

  @Column({
    type: DataType.STRING,
    comment: '昵称',
  })
  nickname: string;

  @Column({
    type: DataType.STRING,
    comment: '头像',
  })
  avatar: string;

  @Column({
    type: DataType.STRING,
    comment: '手机号',
  })
  phone?: string;

  @Column({
    type: DataType.STRING,
    comment: '邮箱',
  })
  email: string;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: true,
    comment: '是否激活',
  })
  isActive: boolean;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '学段code',
  })
  grade_section_code?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '学段',
  })
  grade_section_name?: string;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '年级',
    get() {
      const newValue = this.getDataValue('grade');
      return newValue ? newValue.split(',') : [];
    },
    set(value: string[]) {
      this.setDataValue('grade', value.join(','));
    },
  })
  grade?: string[];

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '学科id',
  })
  subject_id?: number;

  @Column({
    type: DataType.STRING,
    allowNull: true,
    comment: '学科',
  })
  subject_name?: string;

  @ForeignKey(() => Enterprise)
  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '所属单位id',
  })
  enterprise_id: number;

  @BelongsToMany(() => Role, () => UserRole)
  roles: Role[];

  @BelongsTo(() => Enterprise, { onDelete: 'SET NULL' })
  enterprise: Enterprise;

  @HasMany(() => SchoolHomework)
  schoolHomeworks: SchoolHomework[];

  @BeforeCreate
  static async hashPassword(instance: User) {
    instance.password = await bcrypt.hash(instance.password, 10);
  }

  @BeforeUpdate
  static async updatePassword(instance: User) {
    if (instance.changed('password')) {
      instance.password = await bcrypt.hash(instance.password, 10);
    }
  }

  async validatePassword(password: string) {
    return bcrypt.compare(password, this.password);
  }
}
