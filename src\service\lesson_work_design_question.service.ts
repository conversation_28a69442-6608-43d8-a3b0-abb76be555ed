import { Inject, Provide } from '@midwayjs/core';
import {
  LessonWorkDesignDetail,
  LessonWorkDesignQuestion,
  LessonWorkDesignStruct,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';

@Provide()
export class LessonWorkDesignQuestionService extends BaseService<LessonWorkDesignQuestion> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课时作业设计试题');
  }
  getModel = () => {
    return LessonWorkDesignQuestion;
  };

  /**
   * 批量创建
   * @param designDetail_id 课时作业设计详情id
   * @param body 试题详情
   */
  async bulkCreate(designDetail_id, body) {
    const transaction = await LessonWorkDesignQuestion.sequelize.transaction();
    try {
      // 先删除对应作业下之前创建的题目
      await LessonWorkDesignQuestion.destroy({
        where: { designDetail_id },
        transaction,
      });
      // 批量创建题目
      const data = body.map((item, index) => {
        return {
          ...item,
          sort_order: index + 1,
        };
      });
      const res = await LessonWorkDesignQuestion.bulkCreate(data, {
        transaction,
      });
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 保存试卷
   * @param designDetail_id 课时作业设计详情id
   * @param info 试题、结构详情
   */
  async savePaper(designDetail_id, info) {
    const { questions, structs, ...data } = info;
    const transaction = await LessonWorkDesignQuestion.sequelize.transaction();
    try {
      // 更新课时作业设计详情
      await LessonWorkDesignDetail.update(data, {
        where: { id: designDetail_id },
        transaction,
      });
      // 先删除对应作业下之前创建的题目
      await LessonWorkDesignQuestion.destroy({
        where: { designDetail_id },
        transaction,
      });
      // 批量创建题目
      const questionArr = questions.map((item, index) => {
        return {
          ...item,
          designDetail_id,
          sort_order: index + 1,
        };
      });
      await LessonWorkDesignQuestion.bulkCreate(questionArr, {
        transaction,
      });

      // 先删除对应作业下之前创建的结构
      await LessonWorkDesignStruct.destroy({
        where: { designDetail_id },
        transaction,
      });
      // 批量创建结构
      const structArr = structs.map((item, index) => {
        return {
          ...item,
          designDetail_id,
          sort_order: index + 1,
        };
      });
      await LessonWorkDesignStruct.bulkCreate(structArr, { transaction });
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }
}
