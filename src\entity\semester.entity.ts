import { Table, Column, Model, DataType } from 'sequelize-typescript';

export interface SemesterAttributes {
  /** ID，主键 */
  id: number;
  /** 学年 */
  year: string;
  /** 学期 */
  term: number;
  /** 编号 */
  code: string;
  /** 开始日期 */
  start_date: Date;
  /** 结束日期 */
  end_date: Date;
  /** 状态 */
  status: number;
}

@Table({ tableName: 'semesters', timestamps: false, comment: '学年学期表' })
export class Semester
  extends Model<SemesterAttributes>
  implements SemesterAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: 'ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(9),
    allowNull: false,
    unique: {
      name: 'unique_year_term',
      msg: '已存在相同的学年学期',
    },
    comment: '学年，格式为两个年份以-连接，如2020-2021',
  })
  year: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    unique: {
      name: 'unique_year_term',
      msg: '已存在相同的学年学期',
    },
    comment: '学期',
  })
  term: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    comment: '编号',
  })
  code: string;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '开始日期',
  })
  start_date: Date;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '结束日期',
  })
  end_date: Date;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: '状态',
  })
  status: number;
}
