import {
  Column,
  Model,
  Table,
  DataType,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ComplianceDetectionTemplateDetail } from '.';

export interface ComplianceDetectionTemplateQuestionAttributes {
  /** 主键 */
  id: number;
  /** 达标检测详情id */
  detail_id: number;
  /** 题目id */
  question_id: string;
  /** 排序 */
  sort_order: number;
  /** 试题来源表 */
  source_table?: string;
  detail?: ComplianceDetectionTemplateDetail;
}
@Table({
  tableName: 'compliance_detection_template_question',
  timestamps: true,
  comment: '达标检测范本题目表',
})
export class ComplianceDetectionTemplateQuestion
  extends Model<ComplianceDetectionTemplateQuestionAttributes>
  implements ComplianceDetectionTemplateQuestionAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键',
  })
  id: number;

  @ForeignKey(() => ComplianceDetectionTemplateDetail)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '达标检测详情id',
  })
  detail_id: number;

  @BelongsTo(() => ComplianceDetectionTemplateDetail, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  detail?: ComplianceDetectionTemplateDetail;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '题目id',
  })
  question_id: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '排序',
  })
  sort_order: number;

  @Column({
    type: DataType.ENUM('system', 'school', 'personal'),
    allowNull: true,
    comment: '试题来源表',
  })
  source_table?: string;
}
