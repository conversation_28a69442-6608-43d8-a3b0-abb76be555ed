import { Inject, Provide } from '@midwayjs/core';
import { SubjectDictionary } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class SubjectDictionaryService extends BaseService<SubjectDictionary> {
  @Inject()
  ctx: Context;

  constructor() {
    super('');
  }
  getModel = () => {
    return SubjectDictionary;
  };
}
