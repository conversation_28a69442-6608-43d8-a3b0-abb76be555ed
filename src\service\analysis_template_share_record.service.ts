import { Inject, Provide } from '@midwayjs/core';
import {
  AnalysisTemplateShareRecord,
  ClassWork,
  ComplianceDetectionTemplate,
  Dictionary,
  Enterprise,
  Semester,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { Op } from 'sequelize';
import { SCHOOL_SYSTEM_TO_GRADE_SECTION_MAP } from '../common/Constants';

@Provide()
export class AnalysisTemplateShareRecordService extends BaseService<AnalysisTemplateShareRecord> {
  @Inject()
  ctx: Context;

  constructor() {
    super('范本共享记录表');
  }
  getModel = () => {
    return AnalysisTemplateShareRecord;
  };

  /**
   * 自动汇总各个学校的范本共享次数
   */
  async autoSummary() {
    // 获取字典表中学制信息
    const schoolSystemList = await Dictionary.findAll({
      where: { type: 'school_system' },
    });
    // 获取当前激活的学年学期
    const semester = await Semester.findOne({ where: { status: 1 } });
    if (!semester) {
      console.log('未找到激活的学年学期');
      return;
    }
    // 获取所有的学校信息
    const enterprises = await Enterprise.findAll({ where: { code: '95278' } });

    // 格式化当前学年学期的时间范围 方便后续查询使用
    const startTime = new Date(semester.start_date);
    const endTime = new Date(semester.end_date);

    // 遍历每一个学校，计算该学校下的范本共享次数 范本包括课时范本和达标范本
    for (const enterprise of enterprises) {
      if (!enterprise.id || !enterprise.school_system) {
        continue;
      }
      // 根据学校的学制信息 拆分不同学段的数据入库
      const gradeSection =
        SCHOOL_SYSTEM_TO_GRADE_SECTION_MAP[enterprise.school_system];
      if (!gradeSection) {
        continue; // 没有查到对应的学段 直接跳过该学校的数据统计
      }

      for (const gradeSectionItem of gradeSection) {
        const baseQuery = {
          createdAt: { [Op.between]: [startTime, endTime] },
          enterprise_id: enterprise.id,
          grade_section_code: gradeSectionItem.code,
        };

        // 查询范本ID集合
        const [classWork, detectionTemplate] = await Promise.all([
          ClassWork.findAll({ where: baseQuery, attributes: ['id'] }),
          ComplianceDetectionTemplate.findAll({
            where: baseQuery,
            attributes: ['id'],
          }),
        ]);

        const classWorkIdArr = classWork.map(v => v.id);
        const detectionTemplateIdArr = detectionTemplate.map(v => v.id);

        // 统计共享次数
        const sharedQuery = {
          createdAt: { [Op.between]: [startTime, endTime] },
          enterprise_id: enterprise.id,
          grade_section_code: gradeSectionItem.code,
        };

        const [classWorkCount, detectionTemplateCount] = await Promise.all([
          ClassWork.count({
            where: {
              ...sharedQuery,
              template_id: { [Op.in]: classWorkIdArr },
            },
          }),
          ComplianceDetectionTemplate.count({
            where: {
              ...sharedQuery,
              template_id: { [Op.in]: detectionTemplateIdArr },
            },
          }),
        ]);
        const totalShareCount = classWorkCount + detectionTemplateCount;

        // 查询当前学校在当前学期下的共享记录 若存在则更新 若不存在则创建
        const shareRecord = await AnalysisTemplateShareRecord.findOne({
          where: {
            enterprise_code: enterprise.code,
            semester_code: semester.code,
            grade_section_code: gradeSectionItem.code,
          },
        });

        const schoolSystem = schoolSystemList.find(
          v => v.code === enterprise.school_system
        );

        const transaction =
          await AnalysisTemplateShareRecord.sequelize.transaction();
        try {
          if (shareRecord) {
            await shareRecord.update({
              template_cite_number: totalShareCount,
            });
          } else {
            await AnalysisTemplateShareRecord.create({
              semester_code: semester.code,
              semester_name: semester.term === 1 ? '第一学期' : '第二学期',
              province_code: enterprise.province,
              province_name: enterprise.province_name,
              city_code: enterprise.city,
              city_name: enterprise.city_name,
              area_code: enterprise.area,
              area_name: enterprise.area_name,
              school_system_code: enterprise.school_system,
              school_system_name: schoolSystem?.name ?? '',
              grade_section_code: gradeSectionItem.code,
              grade_section_name: gradeSectionItem.name,
              enterprise_code: enterprise.code,
              enterprise_name: enterprise.name,
              template_cite_number: totalShareCount,
            });
          }
        } catch (error) {
          await transaction.rollback();
          console.log(error);
        }
      }
    }
  }
}
