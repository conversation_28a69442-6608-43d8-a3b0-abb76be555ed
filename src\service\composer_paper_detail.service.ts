import { Inject, Provide } from '@midwayjs/core';
import { ComposerPaperDetail } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class ComposerPaperDetailService extends BaseService<ComposerPaperDetail> {
  @Inject()
  ctx: Context;

  constructor() {
    super('组卷方案详情');
  }
  getModel = () => {
    return ComposerPaperDetail;
  };

  /**
   * 批量创建题目
   *
   * @param {Array<Object>} data - 数据列表，包含要创建的题目信息
   * @param {string} data.name - 题目的名称
   * @param {string} data.questionType - 题目的基础类型
   * @param {string} data.questionExtendType - 题目的扩展类型
   * @param {string} data.difficulty - 题目的难度级别
   * @param {number} data.num - 题目的数量（如果是批量创建的题目，则可能不需要这个属性）
   * @param {number} data.score - 题目的分数
   * @param {number} data.orderIndex - 题目的排序索引，用于确定题目在试卷中的顺序
   * @param {number} composerPaperId - 方案ID，可能是试卷或题目的集合的ID
   * @param {Transaction} [transaction] - 数据库事务对象，用于确保操作的原子性
   */
  async bulkCreate(data, composerPaperId: number, transaction = null) {
    // 对data进行处理，例如为每个题目添加composerPaperId
    const processedData = data.map(item => ({
      ...item,
      composerPaperId,
    }));

    // 使用事务批量创建题目
    await ComposerPaperDetail.bulkCreate(processedData, { transaction });
  }

  /**
   * 获取最大索引
   *
   * @param composerPaperId 组卷方案ID
   */
  async getMaxOrderIndex(composerPaperId: number) {
    return await ComposerPaperDetail.max('orderIndex', {
      where: {
        composerPaperId,
      },
    });
  }
}
