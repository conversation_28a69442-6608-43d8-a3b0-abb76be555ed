import { Provide, Inject, Scope, ScopeEnum } from '@midwayjs/core';
import { JwtService } from '@midwayjs/jwt';
import * as bcrypt from 'bcryptjs';
import { User } from '../entity';
import { CustomError } from '../error/custom.error';

@Scope(ScopeEnum.Request, { allowDowngrade: true })
@Provide()
export class AuthService {
  @Inject()
  jwtService: JwtService;

  async validateUser(username: string, password: string): Promise<User | null> {
    const user = await User.findOne({ where: { username } });
    if (user && (await bcrypt.compare(password, user.password))) {
      return user;
    }
    return null;
  }

  async login(username: string, password: string): Promise<string> {
    const user = await User.findOne({ where: { username } });
    if (!user) {
      throw new CustomError('无效的用户名');
    }
    const passed = await bcrypt.compare(password, user.password);
    if (!passed) {
      throw new CustomError('密码错误');
    }
    // TODO 需要确认一下配置中sing.expiresIn是否自动生效
    // 生成 JWT
    return this.jwtService.sign({ id: user.id, username: user.username });
  }

  async refresh(refreshToken: string) {
    try {
      const decoded: any = this.jwtService.decodeSync(refreshToken);
      if (!decoded) {
        return null;
      }
      delete decoded.iat;
      delete decoded.exp;
      // 生成新的 JWT
      const newToken = this.jwtService.sign(decoded);
      return newToken;
    } catch (err) {
      console.error('refresh err: ', err);
      return null;
    }
  }
}
