import { Inject, Provide } from '@midwayjs/core';
import { ClassWorkDetail, ClassWorkQuestion, ClassWorkStruct } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';

@Provide()
export class ClassWorkQuestionService extends BaseService<ClassWorkQuestion> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课时作业题目');
  }
  getModel = () => {
    return ClassWorkQuestion;
  };

  /**
   * 批量创建课时作业题目
   * @param classworkDetail_id 课时作业详情id
   * @param info 试题信息
   * @returns
   */
  async bulkCreate(classworkDetail_id, info) {
    const transaction = await ClassWorkQuestion.sequelize.transaction();
    try {
      // 先删除对应作业下之前创建的题目
      await ClassWorkQuestion.destroy({
        where: { classworkDetail_id },
        transaction,
      });
      // 批量创建题目
      const data = info.map((item, index) => {
        return {
          ...item,
          sort_order: index + 1,
        };
      });
      const res = await ClassWorkQuestion.bulkCreate(data, { transaction });
      await transaction.commit();
      return res;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }

  /**
   * 保存试卷
   * @param classworkDetail_id
   * @param info
   */
  async savePaper(classworkDetail_id, info) {
    const { questions, structs, ...data } = info;
    const transaction = await ClassWorkQuestion.sequelize.transaction();
    try {
      // 更新课时作业详情
      await ClassWorkDetail.update(data, {
        where: { id: classworkDetail_id },
        transaction,
      });
      // 先删除对应作业下之前创建的题目
      await ClassWorkQuestion.destroy({
        where: { classworkDetail_id },
        transaction,
      });
      // 批量创建题目
      const questionArr = questions.map((item, index) => {
        return {
          ...item,
          classworkDetail_id,
          sort_order: index + 1,
        };
      });
      await ClassWorkQuestion.bulkCreate(questionArr, { transaction });

      // 先删除对应作业下之前创建的结构
      await ClassWorkStruct.destroy({
        where: { classworkDetail_id },
        transaction,
      });

      // 批量创建结构
      const structArr = structs.map((item, index) => {
        return {
          ...item,
          classworkDetail_id,
          sort_order: index + 1,
        };
      });
      await ClassWorkStruct.bulkCreate(structArr, { transaction });
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error.message);
    }
  }
}
