import { Inject, Provide } from '@midwayjs/core';
import { PlanCourseSetting } from '../entity/plan_course_setting.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class PlanCourseSettingService extends BaseService<PlanCourseSetting> {
  @Inject()
  ctx: Context;

  constructor() {
    super('课程设置');
  }
  getModel = () => {
    return PlanCourseSetting;
  };
}
