import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { Subject } from './subject.entity';
import { SUBJECT_DICTIONARY_TYPE } from '../common/Constants';

/**
 * 学科字典实体属性接口
 */
export interface SubjectDictionaryAttributes {
  /** 学科字典ID */
  id: number;
  /** 学科字典名称 */
  name: string;
  /** 学科字典类型 */
  type: string;
  /** 所属学科ID */
  subjectId: number;
  /** 所属学科名称 */
  subjectName: string;
  /** 所属学段code */
  gradeSectionCode: string;
  /** 所属学段名称 */
  gradeSectionName: string;
  /** 创建时间 */
  createdAt: Date;
  /** 更新时间 */
  updatedAt: Date;
}

@Table({
  tableName: 'subject_dictionary',
  timestamps: true,
  comment: '学科字典表',
})
export class SubjectDictionary
  extends Model<SubjectDictionaryAttributes>
  implements SubjectDictionaryAttributes
{
  @Column({
    primaryKey: true,
    autoIncrement: true,
    comment: 'id',
    type: DataType.INTEGER,
  })
  id: number;

  @Column({
    comment: '学科字典名称',
    allowNull: false,
    type: DataType.STRING(255),
  })
  name: string;

  @Column({
    comment: '学科字典类型',
    allowNull: false,
    type: DataType.ENUM(
      SUBJECT_DICTIONARY_TYPE.COGNITIVE_HIERARCHY,
      SUBJECT_DICTIONARY_TYPE.CORE_QUALITY,
      SUBJECT_DICTIONARY_TYPE.INVESTIGATION_ABILITY
    ),
  })
  type: string;

  @ForeignKey(() => Subject)
  @Column({
    comment: '学科id',
    allowNull: false,
    type: DataType.INTEGER,
    field: 'subject_id',
  })
  subjectId: number;
  @BelongsTo(() => Subject)
  subject: Subject;

  @Column({
    comment: '学科名称',
    allowNull: false,
    type: DataType.STRING(64),
    field: 'subject_name',
  })
  subjectName: string;

  @Column({
    comment: '所属学段code',
    allowNull: false,
    type: DataType.STRING(64),
    field: 'grade_section_code',
  })
  gradeSectionCode: string;
  @Column({
    comment: '所属学段名称',
    allowNull: false,
    type: DataType.STRING(64),
    field: 'grade_section_name',
  })
  gradeSectionName: string;

  @Column({
    comment: '创建时间',
    type: DataType.DATE,
    field: 'created_at',
  })
  createdAt: Date;
  @Column({
    comment: '更新时间',
    type: DataType.DATE,
    field: 'updated_at',
  })
  updatedAt: Date;
}
