import { Table, Column, Model, DataType, HasMany } from 'sequelize-typescript';
import { ComposerPaperDetail } from './composer_paper_detail.entity';
import { ComposerQuestion } from './composer_question.entity';
import { COMPOSER_TYPE } from '../common/Constants';

export interface ComposerPaperAttributes {
  /** 主键ID */
  id: number;
  /** 组卷方案名称 */
  name: string;
  /** 学段 */
  gradeSection: string;
  /** 学段Code */
  gradeSectionCode: string;
  /** 年级 */
  grade: string;
  /** 年级Code */
  gradeCode: string;
  /** 教材版本 */
  version: string;
  /** 教材版本ID */
  versionId: number;
  /** 册次 */
  volume: string;
  /** 册次ID */
  volumeId: number;
  /** 名录ID */
  textbookChecklistId: number;
  /** 所属科目ID */
  subjectId: number;
  /** 所属科目名称 */
  subjectName: string;
  /** 创建人ID */
  creatorId: number;
  /** 创建人名称 */
  creatorName: string;
  /** 企业code */
  enterpriseCode: string;
  /** 方案描述 */
  description: string;
  /** 试卷数量 */
  paperCount?: number;
  /** 是否在自动组卷中 */
  process: boolean;
  /** 组卷方案类型 */
  ruleType: COMPOSER_TYPE;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
  /** 组卷方案详情 */
  details?: ComposerPaperDetail[];
  /** 组卷方案试题篮 */
  composerQuestions?: ComposerQuestion[];
}

@Table({
  tableName: 'composer_paper',
  timestamps: true,
  comment: '组卷方案表',
  indexes: [{ fields: ['creator_id', 'enterprise_id'] }],
})
export class ComposerPaper
  extends Model<ComposerPaperAttributes>
  implements ComposerPaperAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '主键ID',
    field: 'id',
  })
  id: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '组卷方案名称',
    field: 'name',
  })
  name: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '学段',
    field: 'grade_section',
  })
  gradeSection: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '学段Code',
    field: 'grade_section_code',
  })
  gradeSectionCode: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '年级',
    field: 'grade',
  })
  grade: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '年级Code',
    field: 'grade_code',
  })
  gradeCode: string;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '教材版本',
    field: 'version',
  })
  version: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材版本ID',
    field: 'version_id',
  })
  versionId: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '教材名录ID',
    field: 'textbook_checklist_id',
  })
  textbookChecklistId: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '册次',
    field: 'volume',
  })
  volume: string;
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '册次ID',
    field: 'volume_id',
  })
  volumeId: number;
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '所属科目ID',
    field: 'subject_id',
  })
  subjectId: number;
  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '所属科目名称',
    field: 'subject_name',
  })
  subjectName: string;
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '创建人ID',
    field: 'creator_id',
  })
  creatorId: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: false,
    comment: '创建人名称',
    field: 'creator_name',
  })
  creatorName: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '企业code',
    field: 'enterprise_code',
  })
  enterpriseCode: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '方案描述',
    field: 'description',
  })
  description: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '试卷数量',
    field: 'paper_count',
  })
  paperCount?: number;

  @Column({
    type: DataType.BOOLEAN,
    allowNull: false,
    defaultValue: false,
    comment: '是否在自动组卷中',
    field: 'process',
  })
  process: boolean;
  @Column({
    type: DataType.ENUM(COMPOSER_TYPE.AUTO, COMPOSER_TYPE.MANUAL),
    allowNull: false,
    comment: '组卷方案类型',
    field: 'rule_type',
    defaultValue: COMPOSER_TYPE.MANUAL,
  })
  ruleType: COMPOSER_TYPE;
  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '创建时间',
    field: 'created_at',
  })
  createdAt?: Date;

  @Column({
    type: DataType.DATE,
    allowNull: true,
    defaultValue: DataType.NOW,
    comment: '更新时间',
    field: 'updated_at',
  })
  updatedAt?: Date;

  @HasMany(() => ComposerPaperDetail, {
    foreignKey: 'composerPaperId',
    as: 'details',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  details?: ComposerPaperDetail[];

  @HasMany(() => ComposerQuestion, {
    foreignKey: 'composerPaperId',
    as: 'composerQuestions',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  composerQuestions?: ComposerQuestion[];
}
