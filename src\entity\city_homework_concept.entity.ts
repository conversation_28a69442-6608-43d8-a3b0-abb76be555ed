import {
  Table,
  Column,
  Model,
  DataType,
  PrimaryKey,
  AutoIncrement,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';

export interface CityHomeworkConceptAttributes {
  /** 主键ID */
  id: number;
  /** 学科id */
  subject_id?: number;
  /** 学科名称 */
  subject_name?: string;
  /** 学段code */
  grade_section_code?: string;
  /** 教学阶段 */
  grade_section_name?: string;
  /** 设计理念详情 */
  design_detail?: string;
  /** 创建时间 */
  createdAt?: Date;
  /** 更新时间 */
  updatedAt?: Date;
}

@Table({
  tableName: 'city_homework_concept',
  timestamps: true,
  comment: '市级作业学科设计理念',
})
export class CityHomeworkConcept
  extends Model<CityHomeworkConceptAttributes>
  implements CityHomeworkConceptAttributes
{
  @PrimaryKey
  @AutoIncrement
  @Column({
    type: DataType.INTEGER,
    comment: '主键ID',
  })
  id: number;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '学科id',
  })
  subject_id?: number;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学科名称',
  })
  subject_name?: string;

  @Column({
    type: DataType.STRING(50),
    allowNull: true,
    comment: '学段code',
  })
  grade_section_code?: string;

  @Column({
    type: DataType.STRING(255),
    allowNull: true,
    comment: '教学阶段',
  })
  grade_section_name?: string;

  @Column({
    type: DataType.TEXT,
    allowNull: true,
    comment: '设计理念详情',
  })
  design_detail?: string;

  @CreatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    comment: '创建时间',
  })
  createdAt?: Date;

  @UpdatedAt
  @Column({
    type: DataType.DATE,
    defaultValue: DataType.NOW,
    comment: '更新时间',
  })
  updatedAt?: Date;
}
