import {
  Body,
  Controller,
  Del,
  Get,
  Inject,
  Param,
  Post,
  Put,
  Query,
} from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ClassRelationService } from '../service/class_relation.service';
import { CustomError } from '../error/custom.error';
import { ClassRelation } from '../entity';

@Controller('/class_relation')
export class ClassRelationController {
  @Inject()
  ctx: Context;

  @Inject()
  service: ClassRelationService;

  @Get('/', { summary: '查询列表' })
  async index(@Query() query: any) {
    let { offset, limit } = query;
    const { offset: o, limit: l, current, pageSize, ...queryInfo } = query;
    void o;
    void l;

    if (current && pageSize) {
      offset = (current - 1) * pageSize;
      limit = pageSize;
    }
    return this.service.findAll({ query: queryInfo, offset, limit });
  }

  @Get('/:id', { summary: '按ID查询单个' })
  async show(@Param('id') id: string) {
    const res = await this.service.findById(id);
    if (!res) {
      throw new CustomError('未找到指定信息');
    }
    return res;
  }

  @Post('/edit', { summary: '编辑' })
  async edit(@Query() query: any, @Body() body: any) {
    await this.service.update(query, body);
    return true;
  }

  @Post('/', { summary: '新增' })
  async create(@Body() body: any) {
    const transaction = await ClassRelation.sequelize.transaction();
    try {
      // 删除当前单元课时、相似题来源下的单元课时数据
      const { textbook_config_id, unit, period, similarSourceCode } = body;
      const where: Record<string, any> = {};
      if (textbook_config_id) {
        where.textbook_config_id = textbook_config_id;
      }
      if (unit) {
        where.unit = unit;
      }
      if (period) {
        where.period = period;
      }
      if (similarSourceCode) {
        where.similarSourceCode = similarSourceCode;
      }
      await ClassRelation.destroy({
        where,
        transaction,
      });

      // 解构出单元课时信息及其他信息，批量创建
      const { unitArr, ...info } = body;
      let data = [];
      if (unitArr && unitArr.length > 0) {
        data = unitArr.flatMap(item => {
          if (item.periodArr && item.periodArr.length > 0) {
            return item.periodArr.map(ele => ({
              match_unit: item.unit,
              match_period: ele.period,
              ...info,
            }));
          }
          return [
            {
              match_unit: item.unit,
              ...info,
            },
          ];
        });
      }
      await this.service.batchCreate(data, transaction);
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      throw new CustomError(error);
    }
  }

  @Put('/:id', { summary: '更新' })
  async update(@Param('id') id: string, @Body() body: any) {
    await this.service.update({ id }, body);
    return true;
  }

  @Del('/:id', { summary: '删除' })
  async destroy(@Param('id') id: string) {
    await this.service.delete({ id });
    return true;
  }
}
