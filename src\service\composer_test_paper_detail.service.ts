import { Inject, Provide } from '@midwayjs/core';
import { ComposerTestPaperDetail } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
// import { Op, OrderItem } from 'sequelize';
import { QuestionService } from './question.service';

@Provide()
export class ComposerTestPaperDetailService extends BaseService<ComposerTestPaperDetail> {
  @Inject()
  ctx: Context;
  @Inject()
  questionService: QuestionService;

  constructor() {
    super('试卷详情');
  }
  getModel = () => {
    return ComposerTestPaperDetail;
  };

  /**
   * 获取试卷下的所有小题数据
   * @param testPaperId 试卷id
   * @returns
   */
  async getSmallQuestionsByTestPaperId(testPaperId: number) {
    if (!testPaperId) {
      throw new CustomError('请指定正确的试卷id');
    }
    const smallQuestions = await ComposerTestPaperDetail.findAll({
      where: {
        composerTestPaperId: testPaperId,
        // type: '小题',
      },
      attributes: ['question_bank_id', 'score', 'question_sourcesource_type'],
    });
    return smallQuestions;
  }

  /**
   * 获取试卷下下小题
   * @param id 试卷大题ID
   */
  async getPaperDetail(id: number) {
    const questions = await ComposerTestPaperDetail.findAll({
      where: {
        composerTestPaperId: id,
      },
      order: [['order_index', 'ASC']],
      attributes: ['id', 'questionBankId', 'questionSourceType'],
    });
    // 根据小题来源 分别获取小题数据进行组装返回
    const queryQuestions = questions.map(item => {
      return {
        question_id: item.questionBankId,
        source_table: item.sourceTable,
      };
    });
    return this.questionService.findQuestions(queryQuestions);
  }

  /**
   * 通用方法 批量创建试卷详情
   * @param id 试卷id
   * @param data
   */
  async batchCreateDetail(
    composerTestPaperId: number,
    details: any[],
    transaction = null
  ) {
    try {
      const smallQuestions = details.map((smallQues, smallQuesIndex) => {
        return {
          questionBankId: smallQues.questionBankId,
          score: smallQues.score,
          orderIndex: smallQues.orderIndex || smallQuesIndex + 1 || 0,
          questionExtendType: smallQues.questionExtendType,
          questionType: smallQues.questionType,
          questionSourceType: smallQues.questionSourceType,
          composerTestPaperId,
          difficulty: smallQues.difficulty,
          sourceTable: smallQues.sourceTable,
        };
      });
      // 创建小题
      await this.batchCreate(smallQuestions, transaction);
    } catch (error) {
      throw new CustomError(error.message);
    }
  }
}
