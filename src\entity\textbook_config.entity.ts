import { Table, Column, Model, DataType, HasMany } from 'sequelize-typescript';
import { QuestionRelation, ClassRelation } from '.';

export interface TextbookConfigAttributes {
  /** 作业配置id */
  id: number;
  /** 企业编码 */
  enterpriseCode?: string;
  /** 企业名称 */
  enterpriseName?: string;
  /** 学段编码 */
  grade_section_code?: string;
  /** 学段名称 */
  grade_section_name?: string;
  /** 学年学期编码 */
  semesterCode?: string;
  /** 学年学期名称 */
  semesterName?: string;
  /** 年级编码 */
  gradeCode?: string;
  /** 年级名称 */
  gradeName?: string;
  /** 学科编码 */
  subjectCode?: string;
  /** 学科名称 */
  subject?: string;
  /** 来源编码 */
  sourceCode?: string;
  /** 来源名称 */
  source?: string;
  /** 教材版本 */
  textbook_version?: string;
  /** 册次 */
  volume?: string;
  /** 最大相似题数 */
  maxSimilar?: number;
  questionRelations?: QuestionRelation[];
  classRelation?: ClassRelation[];
}

@Table({
  tableName: 'textbook_config',
  timestamps: true,
  comment: '校本作业配置表',
})
export class TextbookConfig
  extends Model<TextbookConfigAttributes>
  implements TextbookConfigAttributes
{
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '作业配置id',
  })
  id: number;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '企业编码',
  })
  enterpriseCode: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '企业名称',
  })
  enterpriseName: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学段编码',
  })
  grade_section_code: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学段名称',
  })
  grade_section_name: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学年学期编码',
  })
  semesterCode: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学年学期名称',
  })
  semesterName: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '年级编码',
  })
  gradeCode: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '年级名称',
  })
  gradeName: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学科编码',
  })
  subjectCode: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '学科名称',
  })
  subject: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '来源编码',
  })
  sourceCode: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '来源名称',
  })
  source: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '教材版本',
  })
  textbook_version: string;

  @Column({
    type: DataType.STRING(64),
    allowNull: true,
    comment: '册次',
  })
  volume: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: true,
    comment: '最大相似题数',
  })
  maxSimilar: number;

  @HasMany(() => QuestionRelation)
  questionRelations?: QuestionRelation[];

  @HasMany(() => ClassRelation)
  classRelations?: ClassRelation[];
}
