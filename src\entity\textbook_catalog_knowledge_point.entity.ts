// models/KnowledgePointTextbookCatalog.ts
import {
  Table,
  Model,
  Column,
  PrimaryKey,
  AutoIncrement,
  ForeignKey,
  DataType,
} from 'sequelize-typescript';
import { TextbookCatalog, KnowledgePoint } from '.';

@Table({
  tableName: 'textbook_catalog_knowledge_point',
  comment: '知识点和教材目录关联表',
})
export class TextbookCatalogKnowledgePoint extends Model {
  @PrimaryKey
  @AutoIncrement
  @Column
  id!: number;

  @ForeignKey(() => KnowledgePoint)
  @Column({
    comment: '知识点id',
    type: DataType.INTEGER,
  })
  pointId!: number;

  @ForeignKey(() => TextbookCatalog)
  @Column({
    comment: '教材目录id',
    type: DataType.INTEGER,
  })
  catalogId!: number;
}
