import { Inject, Provide } from '@midwayjs/core';
import { StandardAcademicQuality } from '../entity/standard_academic_quality.entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';

@Provide()
export class StandardAcademicQualityService extends BaseService<StandardAcademicQuality> {
  @Inject()
  ctx: Context;

  constructor() {
    super('学业质量');
  }
  getModel = () => {
    return StandardAcademicQuality;
  };
}
