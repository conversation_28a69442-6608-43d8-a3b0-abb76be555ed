import { Inject, Logger, Provide } from '@midwayjs/core';
import {
  ComposerPaper,
  ComposerTestPaper,
  ComposerTestPaperDetail,
  ComposerTestPaperStruct,
  QuestionExtendType,
} from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { CustomError } from '../error/custom.error';
import { Op } from 'sequelize';
import { ComposerTestPaperDetailService } from './composer_test_paper_detail.service';
import { Util } from '../common/Util';
import * as dayjs from 'dayjs';
import { ComposerPaperService } from './composer_paper.service';
import { QuestionService } from './question.service';
import { ComposerTestPaperStructService } from './composer_test_paper_struct.service';
import { flattenDeep, groupBy, includes, sumBy } from 'lodash';
import { randomUUID } from '@midwayjs/core/dist/util/uuid';
@Provide()
export class ComposerTestPaperService extends BaseService<ComposerTestPaper> {
  @Inject()
  ctx: Context;

  @Logger()
  logger;

  @Inject()
  composerPaperService: ComposerPaperService;

  @Inject()
  composerTestPaperDetailService: ComposerTestPaperDetailService;

  @Inject()
  composerTestPaperStructService: ComposerTestPaperStructService;

  @Inject()
  questionService: QuestionService;
  constructor() {
    super('试卷');
  }
  getModel = () => {
    return ComposerTestPaper;
  };

  /**
   * 创建
   *
   * @param {*} data 数据
   * @return {*} res
   * @memberof Composer_test_paperService
   */
  async create(data): Promise<any> {
    const transaction = await ComposerTestPaper.sequelize.transaction();
    try {
      // 创建试卷 同步保存试卷详情、试卷结构
      const { ruleType, composerPaperId, details, structs, ...info } = data;
      const maxOrderIndex = await this.getMaxOrderIndex({
        composerPaperId,
      });
      const res = await ComposerTestPaper.create(
        {
          ...info,
          composerPaperId,
          ruleType,
          orderIndex: maxOrderIndex + 1,
        },
        { transaction }
      );
      // 生成试卷结构
      await this.composerTestPaperStructService.batchCreateStruct(
        res.id,
        structs,
        transaction
      );
      // 生成试卷详情
      await this.composerTestPaperDetailService.batchCreateDetail(
        res.id,
        details,
        transaction
      );
      await transaction.commit();
      return await this.findTestPaperWithDetail(res.id);
    } catch (error) {
      await transaction.rollback();
      throw new CustomError('创建试卷失败');
    }
  }

  /**
   * 通用方法 批量创建试卷详情
   * @param id 试卷id
   * @param data
   */
  async batchCreateTestPaperDetail(
    id: number,
    children: any[],
    transaction = null
  ) {
    try {
      const smallQuestions = [];
      const bigQuestions = [];
      //  创建ID映射表
      const idMapping = new Map();
      let index = 0;
      // 组装大题、小题数据 进行临时的父子id维护
      for (const bigQues of children) {
        // 删除大题id避免复制时 接口报错
        delete bigQues.id;
        const { children: childrenArr, orderIndex, ...d } = bigQues;

        bigQuestions.push({
          ...d,
          orderIndex: orderIndex || index + 1 || 0,
          type: '大题',
          composerTestPaperId: id,
        });
        smallQuestions.push(
          ...childrenArr.map((smallQues, smallQuesIndex) => {
            return {
              questionBankId: smallQues.questionBankId,
              pid: index + 1,
              score: smallQues.score,
              orderIndex: smallQues.orderIndex || smallQuesIndex + 1 || 0,
              type: '小题',
              questionExtendType: smallQues.questionExtendType,
              questionType: smallQues.questionType,
              questionSourceType: smallQues.questionSourceType,
              composerTestPaperId: id,
              difficulty: smallQues.difficulty,
            };
          })
        );
        index++;
      }
      // 创建大题
      const bigQuestionsResults =
        await this.composerTestPaperDetailService.batchCreate(
          bigQuestions,
          transaction
        );
      // 记录ID映射
      bigQuestionsResults.forEach((created, index) => {
        idMapping.set(index + 1, created.id);
      });
      // 根据临时id为小题分配实际的大题id
      smallQuestions.forEach(s => {
        if (s.pid && idMapping.has(s.pid)) {
          s.pid = idMapping.get(s.pid);
        }
      });
      // 创建小题
      await this.composerTestPaperDetailService.batchCreate(
        smallQuestions,
        transaction
      );
    } catch (error) {
      throw new CustomError(error.message);
    }
  }

  /**
   * 获取最大顺序
   *
   * @param where 查询条件
   * @return 返回最大订单索引
   */
  async getMaxOrderIndex(where): Promise<number> {
    return await ComposerTestPaper.max('orderIndex', {
      where,
    });
  }

  async getDetail(id) {
    const result = await ComposerTestPaper.findOne({
      where: { id },
    });
    const tree = result.toJSON();
    // if (tree) {
    //   await this.composerTestPaperDetailService.getTree(tree);
    // }
    return tree;
  }

  async _autoBulkCreateTestPaper(
    data,
    composerPaper,
    testPaperNum: number,
    isDestroy: boolean,
    transaction
  ) {
    try {
      // 从字典表中直接获取到试题类型
      const questionTypeInfos = await QuestionExtendType.findAll();
      const questionTypes = questionTypeInfos.map(item => {
        return item.id;
      });
      const list = await this.autoBulkCreateTestPaperList(data, {});
      let count = testPaperNum;
      const newDataArr = [];
      const total = await ComposerTestPaper.count({
        where: {
          composerPaperId: composerPaper.id,
          ruleType: '自动',
          createdAt: {
            [Op.gte]: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            [Op.lte]: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
          },
        },
      });
      let index = 1;
      while (count--) {
        let newData: any = {};
        // 判断是否删除已经生成的自动的试卷是否存在，不存在，新生成的试卷名要在原有基础上增加
        if (isDestroy) {
          newData = {
            name: `${composerPaper.name}自动组卷${dayjs().format('YYYYMMDD')}${
              testPaperNum - count
            }`,
            score: 0,
            ruleType: '自动',
            children: [],
            composerPaperId: composerPaper.id,
          };
        } else {
          // 获取当天智能组卷数量
          newData = {
            name: `${composerPaper.name}自动组卷${dayjs().format('YYYYMMDD')}${
              total + index
            }`,
            score: 0,
            ruleType: '自动',
            children: [],
            composerPaperId: composerPaper.id,
          };
        }
        let dtIndex = 1;
        questionTypes.forEach(item => {
          const testList = list.filter(v => v.questionBankType === item);
          if (testList.length > 0) {
            const childrenObj = {
              name: item,
              orderIndex: dtIndex,
              children: [],
              score: 0,
              type: item,
            };
            testList.forEach(v => {
              // 抽取的组合中随机抽取一个
              const xtList = Util.getRandomSubset(v.ids, v.num);
              const childrenArr = xtList.map(id => {
                childrenObj.score = childrenObj.score + v.score;
                newData.score = newData.score + v.score;
                return {
                  type: item,
                  questionBankId: id,
                  score: v.score,
                };
              });
              childrenObj.children.push(...childrenArr);
            });
            dtIndex++;
            // 计算大题总分
            newData.children.push(childrenObj);
          }
          // 生成试卷
        });
        newDataArr.push(newData);
        index++;
      }
      // 批量创建试卷
      const maxOrderIndex = await this.getMaxOrderIndex({
        composerPaperId: newDataArr[0].composerPaperId,
      });
      const maxTestPaper: number = (await ComposerTestPaper.max('id', {})) || 0;
      const map = new Map();
      const testPapers = newDataArr.map((data, index) => {
        const { ruleType, composerPaperId, children, ...info } = data;
        const paperId = maxTestPaper + index + 1;
        map.set(index, children);
        return {
          ...info,
          composerPaperId,
          ruleType,
          orderIndex: maxOrderIndex + index + 1,
          id: paperId,
        };
      });
      const testPaperResults = await this.batchCreate(testPapers, transaction);
      // 循环询出所有试卷的详情数据 一次提交
      const promises = testPaperResults.map((testPaper, index) => {
        const children = map.get(index);
        return this.batchCreateTestPaperDetail(
          testPaper.id,
          children,
          transaction
        );
      });
      await Promise.all(promises);
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
  /**
   * 自动生成试卷
   * @param data 试卷规则数据
   * @param composerPaper 组卷数据
   * @param testPaperNum 试卷数量
   * @param isDestroy 是否删除旧自动生成试卷
   * @param transaction
   */
  async _autoBulkCreateTestPaperRefactor(
    data: any[],
    composerPaper: {
      id: number;
      name: string;
      subjectId: any;
      gradeSectionCode: string;
    },
    testPaperNum: number,
    isDestroy: boolean,
    transaction = null
  ) {
    try {
      // 从字典表中直接获取到试题类型
      const stageQuestionsTypes = await QuestionExtendType.findAll({
        where: {
          gradeSection: composerPaper.gradeSectionCode,
        },
      });
      const questionTypeInfos = stageQuestionsTypes.filter(item =>
        includes(item.subjectId, composerPaper.subjectId)
      );
      const list = await this.autoBulkCreateTestPaperList(data, {});
      let count = testPaperNum;
      const testPapers = [];
      const structMap = new Map();
      const detailMap = new Map();
      const total = await ComposerTestPaper.count({
        where: {
          composerPaperId: composerPaper.id,
          ruleType: '自动',
          createdAt: {
            [Op.gte]: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            [Op.lte]: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
          },
        },
      });
      const maxOrderIndex = await this.getMaxOrderIndex({
        composerPaperId: composerPaper.id,
      });
      let index = 1;
      while (count--) {
        let dtIndex = 2;
        const structs = [
          {
            id: `hidden_${randomUUID()}`, // 结构的唯一标识
            name: '暂无分组',
            sortOrder: 1,
            score: 0,
            questionIds: [],
          },
        ];
        const details = [];
        const questionGroupObj = groupBy(list, 'questionBankType');
        for (const key in questionGroupObj) {
          const questions = questionGroupObj[key];
          const questionTypeInfo = questionTypeInfos.find(q => q.code === key);
          let questionIndex = 1;
          const tempDetails = questions.map(v => {
            // 抽取的组合中随机抽取指定数量试题
            const xtList = Util.getRandomSubset(v.ids, v.num);
            return xtList.map(id => {
              const obj = {
                questionBankId: id,
                score: v.score,
                sourceTable: v.sourceTable,
                orderIndex: questionIndex,
              };
              questionIndex++;
              return obj;
            });
          });

          const detailArr = flattenDeep(tempDetails);
          const struct = {
            id: randomUUID(), // 结构的唯一标识
            name: questionTypeInfo.name,
            sortOrder: dtIndex,
            score: sumBy(detailArr, 'score'),
            questionIds: detailArr.map(d => d.questionBankId),
          };
          structs.push(struct);
          details.push(...detailArr);
          dtIndex++;
        }
        // 判断是否删除已经生成的自动的试卷是否存在，不存在，新生成的试卷名要在原有基础上增加
        const orderIndex = maxOrderIndex + index + 1;
        const testPaper: any = {
          name: isDestroy
            ? `${composerPaper.name}自动组卷${dayjs().format('YYYYMMDD')}${
                testPaperNum - count
              }`
            : `${composerPaper.name}自动组卷${dayjs().format('YYYYMMDD')}${
                total + index
              }`,
          score: sumBy(structs, 'score'),
          ruleType: '自动',
          composerPaperId: composerPaper.id,
          orderIndex,
        };
        testPapers.push(testPaper);
        structMap.set(orderIndex, structs);
        detailMap.set(orderIndex, details);
        index++;
      }
      // 批量创建试卷
      const testPaperResults = await this.batchCreate(testPapers, transaction);
      // 批量创建试卷结构、试卷详情
      const promises = [];
      testPaperResults.forEach(testPaper => {
        const { orderIndex } = testPaper;
        const structs = structMap.get(orderIndex);
        const details = detailMap.get(orderIndex);
        promises.push(
          this.composerTestPaperStructService.batchCreateStruct(
            testPaper.id,
            structs,
            transaction
          )
        );
        promises.push(
          this.composerTestPaperDetailService.batchCreateDetail(
            testPaper.id,
            details,
            transaction
          )
        );
      });
      await Promise.all(promises);
    } catch (error) {
      this.logger.error(
        `Failed to _autoBulkCreateTestPaperRefactor,message is ${error.message},stack is ${error.stack}`
      );
      throw error;
    }
  }
  /**
   * 自动批量创建试卷
   *
   * @param {Array} data - 试卷数据数组
   * @param {Object} composerPaper - 试卷模板对象
   * @param {Number} testPaperNum - 试卷数量
   * @param {Boolean} isDestroy
   * @return {Promise<void>}
   */
  async autoBulkCreateTestPaper(data, composerPaper, testPaperNum, isDestroy) {
    const transaction = await ComposerTestPaper.sequelize.transaction();
    try {
      // 判断是否删除已经生成的自动的试卷
      const deleteList = [];
      if (isDestroy) {
        const res = await ComposerTestPaper.findAll({
          where: {
            composerPaperId: composerPaper.id,
            ruleType: '自动',
          },
        });
        res.forEach(item => {
          deleteList.push(item.id);
        });
        const filter = { where: { id: { [Op.in]: deleteList } }, transaction };
        await ComposerTestPaper.destroy(filter);
      }
      // 生成试卷
      await this._autoBulkCreateTestPaperRefactor(
        data,
        composerPaper,
        testPaperNum,
        isDestroy,
        transaction
      );
      // 智能组卷完成统计试卷数量
      const count = await ComposerTestPaper.count({
        where: {
          composerPaperId: composerPaper.id,
        },
        transaction,
      });
      await ComposerPaper.update(
        {
          paperCount: count,
        },
        {
          where: { id: composerPaper.id },
          transaction,
        }
      );
      await transaction.commit();
      return true;
    } catch (error) {
      await transaction.rollback();
      console.log(error.message);
      throw new CustomError('试卷生成失败，请联系管理员！');
    }
  }

  /**
   *  获取可组成试卷的列表 以及所有的可能组合数
   * @param {*} data
   * @param {*} questionType 应用类型
   * @param source
   * @return
   */
  async autoBulkCreateTestPaperList(data: any[], queryObj: any) {
    const list = [];
    if (!queryObj) {
      throw new CustomError('缺少参数');
    }

    for (const dataItem of data) {
      // 条件筛选试题数量
      const { difficulty, num, score, sourceTable, type, catalog } = dataItem;
      const questionRes = await this.questionService.findQuestionsByFilter(
        {
          difficulty,
          type,
          catalog,
        },
        null,
        null,
        { createdAt: 1 },
        sourceTable
      );
      if (questionRes.list.length === 0) {
        continue;
      }
      const ids = questionRes.list.map(item => item._id.toString());
      list.push({
        name: '',
        ids,
        score,
        num,
        questionBankType: type,
        sourceTable,
      });
    }
    return list;
  }
  /**
   *  获取可组成试卷的列表 以及所有的可能组合数
   * @param {*} data
   * @param {*} questionType 应用类型
   * @return
   */
  async autoCount(data: any[], queryObj: any) {
    const list = [];
    if (!queryObj) {
      throw new CustomError('缺少参数');
    }
    const questionTypeInfos = await QuestionExtendType.findAll();
    const questionTypes = questionTypeInfos.map(item => {
      return item.name;
    });
    for (const dataItem of data) {
      //科目、年级、教材版本、学科、单元、课时、难度、类型作为条件筛选试题数量
      const { difficulty, num, score, sourceType, type, unit, period } =
        dataItem;
      const questionList = await this.questionService.findByFilter(
        {
          // 'difficulty.code': difficulty, // 难度'
          // 'type.code': type, // 题目类型'
          // ...queryObj,
          'difficulty.id': difficulty, // 难度'
          'type.id': type, // 题目类型'
          'unit.id': unit,
          'period.id': period,
        },
        sourceType
      );
      if (questionList.total === 0) {
        continue;
      }
      const ids = questionList.map(item => item._id);
      const listItem = list.find(item => item.questionBankType === type);
      // 同试题类型 合并ids
      if (listItem) {
        listItem.ids = [].concat(listItem.ids, ids);
      } else {
        list.push({
          name: '',
          ids,
          score,
          num,
          questionBankType: type,
        });
      }
    }
    let sumCount = BigInt(1);
    questionTypes.forEach(item => {
      const typeQuestionInfo = list.find(v => v.questionBankType === item);
      if (typeQuestionInfo) {
        const s = Util.combination(
          typeQuestionInfo.ids.length,
          typeQuestionInfo.num
        );
        sumCount = sumCount * s;
      }
    });
    return sumCount.toString();
  }

  /**
   * 更新试卷及试卷详情
   * @param id 试卷id
   * @param data
   */
  async detailUpdate(id: number, data) {
    const transaction = await ComposerTestPaper.sequelize.transaction();
    try {
      delete data.id;
      const { ruleType, composerPaperId, structs, details, ...info } = data;

      await ComposerTestPaper.update(
        { ...info, composerPaperId, ruleType },
        {
          where: { id },
          transaction,
        }
      );
      await ComposerTestPaperDetail.destroy({
        where: { composerTestPaperId: id },
        transaction,
      });
      await ComposerTestPaperStruct.destroy({
        where: { composerTestPaperId: id },
        transaction,
      });
      // 生成试卷结构
      await this.composerTestPaperStructService.batchCreateStruct(
        id,
        structs,
        transaction
      );
      // 生成试卷详情
      await this.composerTestPaperDetailService.batchCreateDetail(
        id,
        details,
        transaction
      );
      await transaction.commit();
      return await this.findTestPaperWithDetail(id);
    } catch (error) {
      await transaction.rollback();
      throw new CustomError('保存失败');
    }
  }

  async destroy(id: string) {
    const paper = await ComposerTestPaper.findOne({ where: { id } });

    await ComposerTestPaper.destroy({ where: { id } });
    // 删除试卷时，组卷中的已组试卷数减 1
    const composerPaper = await this.composerPaperService.findOne({
      id: paper.composerPaperId,
    });
    composerPaper.update({
      paperCount: composerPaper.paperCount - 1,
    });
  }

  /**
   * 批量删除试卷
   * @param ids
   */
  async bulkDestroy(ids: string[]) {
    const paper = await ComposerTestPaper.findOne({ where: { id: ids[0] } });
    await ComposerTestPaper.destroy({ where: { id: { [Op.in]: ids } } });
    // 批量删除试卷后，统计剩余试卷数存入组卷方案中
    const count = await ComposerTestPaper.count({
      where: { composerPaperId: paper.composerPaperId },
    });
    await ComposerPaper.update(
      { paperCount: count },
      { where: { id: paper.composerPaperId } }
    );
  }

  /**
   * 清除所有试卷
   * @param composerPaperId 组卷id
   */
  async destroyByComposerPaperId(composerPaperId: number) {
    await ComposerTestPaper.destroy({
      where: { composerPaperId },
    });
    // 一键清除，将组卷方案中的已组试卷数设为0
    await ComposerPaper.update(
      { paperCount: 0 },
      { where: { id: composerPaperId } }
    );
  }

  /**
   * 自动组卷
   * @param params
   * @returns
   */
  async executeAutoComposerPaper(params: any) {
    // 执行自动组卷的逻辑
    const { data, testPaperNum, composerPaperId, isDestroy } = params;
    console.log(`Start auto composer paper, count is ${testPaperNum}`);
    const composerPaper = await this.composerPaperService.findById(
      composerPaperId
    );
    try {
      if (!composerPaper) {
        throw new CustomError('请选择组卷方案');
      }
      await this.autoBulkCreateTestPaper(
        data,
        composerPaper,
        testPaperNum,
        isDestroy
      );
      await this.composerPaperService.update(
        { id: composerPaperId },
        {
          process: false,
        }
      );
      return true;
    } catch (error) {
      console.log('🚀 ~ AutoComposerPaper ~ error:', error);
      await this.composerPaperService.update(
        { id: composerPaperId },
        {
          process: false,
        }
      );
      throw new CustomError('组卷失败');
    }
  }

  /**
   * 获取试卷及详情、结构
   * @param id 试卷id
   * @returns
   */
  async findTestPaperWithDetail(id: number) {
    const paper = await ComposerTestPaper.findOne({
      where: { id },
      include: [
        {
          model: ComposerTestPaperDetail,
          as: 'details',
          attributes: { exclude: ['createdAt', 'updatedAt'] },
          order: [['orderIndex', 'ASC']],
        },
        {
          model: ComposerTestPaperStruct,
          as: 'structs',
          attributes: { exclude: ['createdAt', 'updatedAt'] },
          order: [['sortOrder', 'ASC']],
        },
      ],
    });
    return paper;
  }
}
